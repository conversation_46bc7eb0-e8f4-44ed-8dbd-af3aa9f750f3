<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="300 seconds" debug="false">
	<include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />

	<springProperty scope="context" name="springAppName" source="spring.application.name" />
	<springProperty scope="context" name="logPath" source="logging.file.path" />

	<property name="maxHistory" value="72"/>
	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss} [%thread] [%X{requestId}] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${logPath}/${springAppName}-error.log</file>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${logPath}/${springAppName}-error.log.%d{yyyy-MM-dd-HH}</fileNamePattern>
			<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{requestId}] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
	<appender name="ALL" class="ch.qos.logback.core.rolling.RollingFileAppender">
	<file>${logPath}/${springAppName}.log</file>
	<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">  
		<fileNamePattern>${logPath}/${springAppName}.log.%d{yyyy-MM-dd-HH}</fileNamePattern>
		<maxHistory>${maxHistory}</maxHistory>
		</rollingPolicy>
		<encoder>
			<charset>utf-8</charset>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] [%X{requestId}] %-5level %logger - %msg%n</pattern>
		</encoder>
	</appender>
	<logger name="com.dao" level="DEBUG"></logger>
	<springProfile name="dev,pro">
		<!-- 日志级别 -->
		<root level="info">
			<appender-ref ref="STDOUT" />
		</root>
	</springProfile>
</configuration>