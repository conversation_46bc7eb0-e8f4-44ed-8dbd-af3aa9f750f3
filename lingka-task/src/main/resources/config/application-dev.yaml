###########################################################
# 框架配置
###########################################################
spring:
  # 数据源
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: *************************************************************************
    username: lingka
    password: lingka
    driver-class-name: com.mysql.cj.jdbc.Driver
    initialSize: 1
    minIdle: 1
    maxActive: 10
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall,slf4j
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    useGlobalDataSourceStat: true
  # 文件配置
  file:
    location: /Users/<USER>/develop/file/lingka-task
  # fs 内网地址
  filesystem:
      url: http://************:9091
  # 缓存
  redis:
    host: ************
    password: 38659215534F141B5496BDF477D909A0
    port: 6379
    database: 0
  # session存储方式
###########################################################
# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
# 日志配置
###########################################################
logging:
  config: classpath:config/logback-spring.xml
  file:
    path: /Users/<USER>/develop/logs/dev/lingka-task
# app 参数配置
app:
  huifu:
    notify:
      url: https://api.qingbei.online/v1/huifu/pay/notify/create