package com.task.client;

import java.net.URI;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import com.common.bean.Response;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.task.constant.App;

public class ServiceClient<T> {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private String token;
	private String version;
	private String platform;
	private String agent;
	public Response<T> execute(String url,Object request, Class<?>... clazz){
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            try {
                HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
                requestFactory.setHttpClient(httpClient);
                RequestEntity<?> requestEntity = RequestEntity.post(new URI(this.getUrl(url)))
                        .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                        .header(App.HTTP_HEADER_APP_TOKEN, token)
                        .header(App.HTTP_HEADER_APP_PLATFORM, platform)
                        .header(App.HTTP_HEADER_APP_VERSION, version)
                        .header(App.HTTP_HEADER_APP_AGENT, agent)
                        .body(request);
                ResponseEntity<String> responseEntity = new RestTemplate(requestFactory).exchange(requestEntity, String.class);
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
                String responseBody = responseEntity.getBody();
                logger.info("server response : {}", responseBody);
                return objectMapper.readValue(responseEntity.getBody(), this.getJavaType(objectMapper, clazz));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
	}
	
	private String getUrl(String url){
		return App.SERVER_URL + url;
	}
	
	private JavaType getJavaType(ObjectMapper mapper,Class<?>... clazz){
		 return mapper.getTypeFactory().constructParametricType(Response.class, clazz);
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getVersion() {
		return version;
	}

	public void setVersion(String version) {
		this.version = version;
	}

	public String getPlatform() {
		return platform;
	}

	public void setPlatform(String platform) {
		this.platform = platform;
	}

	public String getAgent() {
		return agent;
	}

	public void setAgent(String agent) {
		this.agent = agent;
	}
}
