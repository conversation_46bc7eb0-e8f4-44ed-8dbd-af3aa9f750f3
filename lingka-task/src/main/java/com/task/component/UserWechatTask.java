package com.task.component;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.service.WechatAppService;

@Component
public class UserWechatTask extends BaseTask{

    @Autowired
    private WechatAppService wechatAppService;

    /**
     * 获取用户粉丝列表
     */
    @Scheduled(initialDelay = 100,fixedRate = 30 * 60 * 1000)
    public void getUserList(){
        try {
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
    }
}
