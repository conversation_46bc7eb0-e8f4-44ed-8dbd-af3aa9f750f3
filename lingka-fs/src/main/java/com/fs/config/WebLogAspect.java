package com.fs.config;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.common.bean.Bean;
import com.fasterxml.jackson.databind.ObjectMapper;

@Aspect
@Component
public class WebLogAspect {
	
	private Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Pointcut("execution(public * com.fs.controller..*.*(..))")
	public void webLog(){}
	
	@Before("webLog()")
	public void doBefore(JoinPoint joinPoint) throws Throwable {
	    ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
	    HttpServletRequest request = attributes.getRequest();
	    logger.info("request url : {}",request.getRequestURL().toString());
	    logger.info("request method : {}",request.getMethod());
	    logger.info("request ip : {}",request.getRemoteAddr());
	    logger.info("execure class : {}",joinPoint.getSignature().getDeclaringTypeName());
	    logger.info("execure method : {}",joinPoint.getSignature().getName());
	    //打印请求头部信息
	    Enumeration<String> headerNames = request.getHeaderNames();
	    if(headerNames != null){
	    	while(headerNames.hasMoreElements()){
	    		String headerName = headerNames.nextElement();
	    		logger.info("{} : {}",headerName,request.getHeader(headerName));
	    	}
	    }
	    //打印请求参数
	    Enumeration<String> parameterNames = request.getParameterNames();
	    if(parameterNames != null){
	    	while(parameterNames.hasMoreElements()){
	    		String parameterName = parameterNames.nextElement();
	    		logger.info("{} : {}",parameterName,request.getParameter(parameterName));
	    	}
	    }
	    //打印参数信息
	    Object[] args = joinPoint.getArgs();
	    if(args != null){
	    	for(Object obj : args){
	    		if(obj instanceof Bean){
	    			logger.info("request : {}",this.getJSON(obj));
	    		}else{
	    			logger.info("request : {}",obj);
	    		}
	    	}
	    }
	}
	
	@AfterReturning(returning = "response", pointcut = "webLog()")
	public void doAfterReturning(Object response) throws Throwable {
		if(response instanceof Bean){
			logger.info("response : {}",this.getJSON(response));
		}else{
			logger.info("response : {}",response);
		}
	}
	
	/**
     * 请求参数拼装
     * 
     * @param paramsArray
     * @return
     */
	private String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			logger.error(e.getMessage(),e);
			throw new RuntimeException(e);
		}
	}
}
