package com.fs.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.common.bean.NameValuePair;
import com.common.util.DateUtil;
import com.common.util.StringUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fs.bean.ResponseHandler;
import com.fs.constant.App;

public class BaseController {
	protected final String TITLE = App.TITLE;
	protected final String NOTIFY = App.NOTIFY;
	protected final String OK = ResponseHandler.OK;
	protected final String SUCCESS = ResponseHandler.SUCCESS;
	protected final String ERROR = ResponseHandler.ERROR;
	protected final String FAILURE = ResponseHandler.FAILURE;
	
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	HttpServletRequest httpServletRequest;
	
	@Autowired
	HttpServletResponse httpServletResponse;
	
	@Autowired
	HttpSession session;
	
//	@InitBinder
//	protected void initBinder(WebDataBinder binder) {
//		binder.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
//	}
	
	String redirect(String url){
		return new StringBuilder("redirect:").append(url).toString();
	}
	String redirect(String url, NameValuePair... pairs){
		return new StringBuilder("redirect:").append(StringUtil.getRequestParameters(url, pairs)).toString();
	}
	String forward(String url){
		return url;
	}
	String forward(String url,NameValuePair... pairs){
		return StringUtil.getRequestParameters(url, pairs);
	}
	Boolean isEmpty(String str){
		if(str != null){
			str = str.trim();
			if(!"".equals(str) && !"null".equalsIgnoreCase(str) && !"undefined".equalsIgnoreCase(str)){
				return false;
			}
		}
		return true;
	}
	Boolean isEmpty(Object obj){
		if(obj != null){
			if(!"".equals(obj) && !"null".equals(obj) && !"undefined".equals(obj)){
				return false;
			}
		}
		return true;
	}
	String getDateTime(){
		return DateUtil.getServerTime("yyyyMMddHHmmss");
	}
	String getDateTime(Date date,String format){
		return DateUtil.format(date, format);
	}
	Date getServerTime(){
		return DateUtil.getServerTime();
	}
	String getServerYear(){
		return DateUtil.getServerTime("yyyy");
	}
	String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	String[] getImageArray(String str){
		if(!this.isEmpty(str)){
			String[] array = str.split(App.COMMA);
			if(array != null){
				int length = array.length;
				String[] images = new String[length];
				for(int i = 0; i < length; i++){
					//文件扩展名
			    		String fileName = null;
			    		String fileExtension = null;
					if(array[i] != null){
				    		int pos = array[i].lastIndexOf(".");
				    		fileName = array[i].substring(0,pos);
				    		fileExtension = array[i].substring(pos);
				    	}
//					images[i] = this.append(EncryptUtil.encodeWithAES(fileName),fileExtension);
					images[i] = this.append(fileName,fileExtension);
				}
				return images;
			}
		}
		return new String[0];
	}
	String getImage(String str){
		if(!this.isEmpty(str)){
			//文件扩展名
	    		String fileName = null;
	    		String fileExtension = null;
			if(str != null){
		    		int pos = str.lastIndexOf(".");
		    		fileName = str.substring(0,pos);
		    		fileExtension = str.substring(pos);
		    	}
//			return this.append(EncryptUtil.encodeWithAES(fileName),fileExtension);
			return this.append(fileName,fileExtension);
		}
		return null;
	}
	String decodeFileUrl(String str){
		if(!this.isEmpty(str)){
//			return EncryptUtil.decodeWithAES(str);
			return str;
		}
		return null;
	}
	String getFile(String url){
		return StringUtil.getRequestParameters("/file/download", new NameValuePair("url",url));
	}
	String append(String... strs){
		StringBuilder builder = new StringBuilder();
		if(strs != null){
			for(String str : strs){
				if(!this.isEmpty(str)){
					builder.append(str);
				}
			}
		}
		return builder.toString();
	}
	String getFormatDate(Date date){
		return DateUtil.format(date, "yyyy-MM-dd");
	}
	String[] getNoEmptyStrArray(String[] strs){
		List<String> list = new ArrayList<String>();
		if(strs != null){
			for(String str : strs){
				if(!this.isEmpty(str)){
					list.add(str);
				}
			}
		}
		return list.toArray(new String[list.size()]);
	}

	BigDecimal getBigDecimal(String str){
		if(isEmpty(str)){
			return null;
		}
		return new BigDecimal(str);
	}
	
	Integer getInteger(String str){
		if(isEmpty(str)){
			return null;
		}
		return Integer.valueOf(str);
	}
	
	String getString(BigDecimal bigDecimal){
		if(bigDecimal != null){
			return bigDecimal.toString();
		}
		return null;
	}
	
	String getString(Integer integer){
		if(integer != null){
			return integer.toString();
		}
		return null;
	}
	String getString(String[] strs){
		if(strs != null){
			return StringUtils.arrayToDelimitedString(strs, App.COMMA);
		}
		return null;
	}
}
