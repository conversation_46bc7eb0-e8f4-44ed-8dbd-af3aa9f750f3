package com.fs.sequence;

import java.util.concurrent.atomic.AtomicInteger;

public class CommonSequence{
	private static final int SEQ_START = 100000;
	private static final int SEQ_MAX = 999999;
	private static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger(SEQ_START);
	public static final int nextValue(){
		ATOMIC_INTEGER.compareAndSet(SEQ_MAX + 1, SEQ_START);
        return ATOMIC_INTEGER.getAndIncrement();
    }
}