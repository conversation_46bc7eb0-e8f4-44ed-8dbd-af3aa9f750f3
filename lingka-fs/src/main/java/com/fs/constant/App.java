package com.fs.constant;

import java.util.Calendar;

public class App {
	public static final String TOKEN = "token";
	public static final String NOTIFY = "notify";
	public static final String CHARSET = "UTF-8";
	public static final String TITLE = "title";
	public static final String SESSION_SYSUSER = "sysuser";
	public static final String SYSMENU = "sysmenu";
	public static final String COMMA = ",";
	public static final String DOT = ".";
	public static final String LINE_SEPARATOR = System.getProperty("line.separator");
	
	public static final String YEAR_START = "1980";
	
	
	public static final String HTTP_HEADER_APP_TOKEN = "X-App-Token";
	public static final String HTTP_HEADER_APP_VERSION = "X-App-Version";
	public static final String HTTP_HEADER_APP_PLATFORM = "X-App-Platform";
	public static final String HTTP_HEADER_APP_AGENT = "X-App-Agent";
	
	public static final int TOKEN_EXPIRE_UNIT = Calendar.MONTH;
	public static final int TOKEN_EXPIRE_NUMBER = 12;
	
	public static final boolean PRODUCTION = true;
	
	public static final int CAPTCHA_LIMIT_MAX = 3;
	public static final int CAPTCHA_LIMIT_EXPIRE_TIME = 24;

}
