###########################################################
# 框架配置
###########################################################
spring:
  # 数据源
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: ********************************************************************
    username: dthd
    password: dthd
    driver-class-name: com.mysql.cj.jdbc.Driver
    initialSize: 1
    minIdle: 1
    maxActive: 10
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall,slf4j
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    useGlobalDataSourceStat: true
  # 文件配置
  file:
    location: /home/<USER>/zxs-task
  # fs 内网地址
  filesystem:
    lan:
      url: http://127.0.0.1:9099
  # 缓存
  redis:
    host: ***********
    password: 38659215534F141B5496BDF477D909A0
    port: 6379
  # session存储方式
  session:
    store-type: redis
  # json解析配置
  jackson:
    serialization:
      fail-on-empty-beans: false
    parser:
      allow-backslash-escaping-any-character: true
      allow-unquoted-control-chars: true
      allow-unquoted-field-names: true
  # 路径匹配配置
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  servlet:
    multipart:
      # 设置 上传文件的大小
      max-file-size: 3000MB
      # 设置 整个请求的大小
      max-request-size: 300MB
###########################################################
# MyBatis配置
###########################################################
mybatis:
  mapper-locations: classpath:mapper/*.xml
###########################################################
# OSS配置
ali:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    bucket: diantong-bucket
    lan:
      endpoint: oss-cn-beijing-internal.aliyuncs.com
###########################################################
# 队列配置
rocketmq:
  name-server: null
###########################################################
# 日志配置
###########################################################
logging:
  config: classpath:config/logback-spring.xml
  file:
    path: /home/<USER>/pro/dthd-fs
###########################################################
# 接口文档配置
###########################################################
springdoc:
  api-docs:
    version: openapi_3_0
  swagger-ui:
    display-request-duration: true
    groups-order: DESC
    operationsSorter: method
    disable-swagger-default-url: true
    use-root-path: true
  show-actuator: false
###########################################################
# 系统自定义配置
###########################################################