package com.api.controller;

import com.dto.UserCartDTO;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 用户购物车排序测试
 */
public class UserCartSortTest {

    public static void main(String[] args) {
        // 创建测试数据
        List<UserCartDTO> userCartDTOs = new ArrayList<>();
        
        UserCartDTO cart1 = new UserCartDTO();
        cart1.setId(1L);
        cart1.setProductTotalPrice(new BigDecimal("15.50"));
        cart1.setNumber(2);
        userCartDTOs.add(cart1);
        
        UserCartDTO cart2 = new UserCartDTO();
        cart2.setId(2L);
        cart2.setProductTotalPrice(new BigDecimal("25.00"));
        cart2.setNumber(1);
        userCartDTOs.add(cart2);
        
        UserCartDTO cart3 = new UserCartDTO();
        cart3.setId(3L);
        cart3.setProductTotalPrice(new BigDecimal("15.50"));
        cart3.setNumber(3);
        userCartDTOs.add(cart3);
        
        UserCartDTO cart4 = new UserCartDTO();
        cart4.setId(4L);
        cart4.setProductTotalPrice(new BigDecimal("10.00"));
        cart4.setNumber(1);
        userCartDTOs.add(cart4);
        
        System.out.println("排序前:");
        for (UserCartDTO cart : userCartDTOs) {
            System.out.println("ID: " + cart.getId() + ", 价格: " + cart.getProductTotalPrice() + ", 数量: " + cart.getNumber());
        }
        
        // 使用原始的排序方法
        userCartDTOs.sort(Comparator.comparing(UserCartDTO::getProductTotalPrice).reversed().thenComparing(UserCartDTO::getNumber).reversed());
        
        System.out.println("\n排序后:");
        for (UserCartDTO cart : userCartDTOs) {
            System.out.println("ID: " + cart.getId() + ", 价格: " + cart.getProductTotalPrice() + ", 数量: " + cart.getNumber());
        }
        
        // 预期顺序应该是：
        // 1. ID=2, 价格=25.00, 数量=1 (价格最高)
        // 2. ID=3, 价格=15.50, 数量=3 (价格相同，数量更多)
        // 3. ID=1, 价格=15.50, 数量=2 (价格相同，数量较少)
        // 4. ID=4, 价格=10.00, 数量=1 (价格最低)
    }
}
