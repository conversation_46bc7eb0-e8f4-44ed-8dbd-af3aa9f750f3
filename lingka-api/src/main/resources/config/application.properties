server.port=8080
spring.application.name=lingka-api
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.url=****************************************************************************
spring.datasource.username=lingka
spring.datasource.password=lingka
spring.datasource.druid.initialSize=1
spring.datasource.druid.minIdle=1
spring.datasource.druid.maxActive=10
spring.datasource.druid.maxWait=60000
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
spring.datasource.druid.minEvictableIdleTimeMillis=300000
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.poolPreparedStatements=true
spring.datasource.druid.maxPoolPreparedStatementPerConnectionSize=20
spring.datasource.druid.filters=stat,wall,slf4j
spring.datasource.druid.connectionProperties=druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
spring.datasource.druid.useGlobalDataSourceStat=true

spring.file.location=/Users/<USER>/file/lingka-api
spring.redis.host=***************
spring.redis.password=qq#1259799716
spring.redis.port=6379
spring.redis.database=0
spring.session.store-type=redis
spring.jackson.serialization.fail-on-empty-beans=false
spring.jackson.parser.allow-backslash-escaping-any-character=true
spring.jackson.parser.allow-unquoted-control-chars=true
spring.jackson.parser.allow-unquoted-field-names=true
spring.mvc.pathmatch.matching-strategy=ANT_PATH_MATCHER
# 文件上传配置
spring.servlet.multipart.max-file-size=3000MB
spring.servlet.multipart.max-request-size=300MB
mybatis.mapper-locations=classpath:mapper/*.xml
pagehelper.helper-dialect=mysql
pagehelper.reasonable=true
pagehelper.support-methods-arguments=true
pagehelper.params=count=countSql
ali.oss.endpoint=oss-cn-guangzhou.aliyuncs.com
ali.oss.bucket=zcsocial-dev
ali.oss.lan.endpoint=oss-cn-guangzhou.aliyuncs.com
logging.config=classpath:config/logback-spring.xml
logging.file.path=./lingka-api
# 阿里云配置
ali.cloud.key.id=LTAI5tQFA2Va9BhJ4CfQFBUt
ali.cloud.key.secret=******************************
