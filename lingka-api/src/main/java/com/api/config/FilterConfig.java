package com.api.config;

import javax.servlet.DispatcherType;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;

@SpringBootConfiguration
public class FilterConfig {
	@Bean
    public FilterRegistrationBean streamFilterRegistration() {
        FilterRegistrationBean<HttpStreamFilter> registration = new FilterRegistrationBean<>();
        registration.setDispatcherTypes(DispatcherType.REQUEST);
        registration.setFilter(this.gethHttpStreamFilter());
        registration.addUrlPatterns("/*");
        registration.setName("httpStreamFilter");
        registration.setOrder(3);
        return registration;
    }


    @Bean
    public HttpStreamFilter gethHttpStreamFilter() {
        return new HttpStreamFilter();
    }
}
