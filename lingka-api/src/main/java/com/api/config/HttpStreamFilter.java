package com.api.config;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import com.api.constant.App;
import com.api.sequence.LogSequence;
import com.common.bean.JsonWebToken;
import com.common.bean.Log;
import com.common.bean.Response;
import com.common.util.EncryptUtil;
import com.common.util.IPUtil;
import com.common.util.StringUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class HttpStreamFilter implements Filter {
	private final Logger logger = LoggerFactory.getLogger(this.getClass());
	private static final String REQUEST_ID = "requestId";
	@Autowired
	private RedisTemplate<String,String> redisTemplate;


	@Override
	public void init(FilterConfig filterConfig) throws ServletException {

	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {
		HttpServletRequest req = (HttpServletRequest) request;
        String contentType = request.getContentType();
        HttpServletResponse res = (HttpServletResponse) response;
        // 过滤掉 options 请求
        if (RequestMethod.OPTIONS.name().equals(req.getMethod())){
			chain.doFilter(request, response);
		}else {
			Log log = new Log();
			log.setStartTime(System.currentTimeMillis());
			String requestId = LogSequence.get();
			MDC.put(REQUEST_ID, requestId);
			logger.info("request url : {}",req.getRequestURL().toString());
			logger.info("request method : {}",req.getMethod());
			logger.info("request ip : {}",request.getRemoteAddr());
			log.setRequestId(MDC.get(REQUEST_ID));
			log.setIp(IPUtil.getClientIP(req));
			log.setUrl(req.getRequestURI());
			log.setPlatform(req.getHeader(App.HTTP_HEADER_APP_PLATFORM));
			log.setAgent(req.getHeader(App.HTTP_HEADER_APP_AGENT));
			log.setVersion(req.getHeader(App.HTTP_HEADER_APP_VERSION));
			String token = req.getHeader(App.HTTP_HEADER_APP_TOKEN);
			try{
				if(!StringUtil.isEmpty(token)){
					JsonWebToken jsonWebToken = EncryptUtil.parseJwt(token);
					log.setUserId(jsonWebToken.getUserId());
					log.setType(jsonWebToken.getType());
				}
			}catch (Exception e){
				logger.error(e.getMessage(),e);
			}
			if (contentType != null && contentType.contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
				chain.doFilter(request, response);
			} else {
				ContentCachingRequestWrapper requestWrapper = new ContentCachingRequestWrapper(req);
				ContentCachingResponseWrapper responseWrapper = new ContentCachingResponseWrapper(res);
				try {
					chain.doFilter(requestWrapper, responseWrapper);
				} finally {
					if (StringUtils.hasLength(req.getQueryString())){
						logger.info("query String parameters : {}", req.getQueryString());
					}
					if (requestWrapper.getContentAsByteArray().length > 0) {
						logger.info("request payload: {}", new String(requestWrapper.getContentAsByteArray()));
					}
					String responseBody = new String(responseWrapper.getContentAsByteArray());
					logger.info("http response status {}", res.getStatus());
					responseWrapper.copyBodyToResponse();
					if (responseWrapper.getContentType() != null && responseWrapper.getContentType().contains(MediaType.APPLICATION_JSON_VALUE)){
						logger.info("response : {}", responseBody);
						try {
							Response<?> serviceResponse = (Response<?>) this.getObject(responseBody, Response.class);
							log.setResponseCode(serviceResponse.getCode());
						}catch (Exception e){
							logger.error(e.getMessage(),e);
						}
					}
					responseWrapper.copyBodyToResponse();
					}
				}
				log.setEndTime(System.currentTimeMillis());
				// 发送日志到队列
//				try {
//					redisTemplate.opsForList().leftPush(CacheKey.LOG,this.getJSON(log));
//				} catch (Exception e) {
//					logger.error(e.getMessage(),e);
//				}
				// 删除请求编号
				MDC.remove(REQUEST_ID);
			}
		}

	@Override
	public void destroy() {

	}

	private String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			logger.error(e.getMessage(),e);
			throw new RuntimeException(e);
		}
	}

	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
		objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
		try{
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

}
