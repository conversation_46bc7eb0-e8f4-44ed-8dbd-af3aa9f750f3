package com.api.config;

import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.validation.MessageCodesResolver;
import org.springframework.validation.Validator;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.DefaultServletHandlerConfigurer;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.ViewResolverRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@SpringBootConfiguration
public class WebMvcConfig implements WebMvcConfigurer {

	@Override
	public void addArgumentResolvers(@NotNull List<HandlerMethodArgumentResolver> arg0) {
		

	}

	@Override
	public void addCorsMappings(@NotNull CorsRegistry arg0) {
		

	}

	@Override
	public void addFormatters(@NotNull FormatterRegistry arg0) {
		

	}
	
	@Bean
	public AuthenticationInterceptor getAuthenticationInterceptor(){
		return new AuthenticationInterceptor();
	}

	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(this.getAuthenticationInterceptor());
	}

	@Override
	public void addResourceHandlers(@NotNull ResourceHandlerRegistry arg0) {
		

	}

	@Override
	public void addReturnValueHandlers(@NotNull List<HandlerMethodReturnValueHandler> arg0) {
		

	}

	@Override
	public void addViewControllers(@NotNull ViewControllerRegistry arg0) {
		

	}

	@Override
	public void configureAsyncSupport(@NotNull AsyncSupportConfigurer arg0) {
		

	}

	@Override
	public void configureContentNegotiation(@NotNull ContentNegotiationConfigurer arg0) {
		

	}

	@Override
	public void configureDefaultServletHandling(@NotNull DefaultServletHandlerConfigurer arg0) {
		

	}

	@Override
	public void configureHandlerExceptionResolvers(@NotNull List<HandlerExceptionResolver> arg0) {
		

	}

	@Override
	public void configureMessageConverters(@NotNull List<HttpMessageConverter<?>> arg0) {
		

	}

	@Override
	public void configurePathMatch(@NotNull PathMatchConfigurer arg0) {
		

	}

	@Override
	public void configureViewResolvers(@NotNull ViewResolverRegistry arg0) {
		

	}

	@Override
	public void extendHandlerExceptionResolvers(@NotNull List<HandlerExceptionResolver> arg0) {
		

	}

	@Override
	public void extendMessageConverters(@NotNull List<HttpMessageConverter<?>> arg0) {
		

	}

	@Override
	public MessageCodesResolver getMessageCodesResolver() {
		
		return null;
	}

	@Override
	public Validator getValidator() {
		
		return null;
	}

}
