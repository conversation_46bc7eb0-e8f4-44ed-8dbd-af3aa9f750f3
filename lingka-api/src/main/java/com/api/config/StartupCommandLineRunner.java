package com.api.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import com.api.constant.App;
import com.domain.Sequence;
import com.dto.WechatAppDTO;
import com.service.SequenceService;
import com.service.WechatAppService;

@Component
public class StartupCommandLineRunner implements CommandLineRunner {

    @Value("${spring.application.name}")
	private String appName;

	@Value("${spring.file.location}")
	private String fileLocation;

	@Value("${ali.oss.bucket}")
	private String bucketName;

	@Value("${ali.cloud.key.id}")
	private String aliCloudKeyId;

	@Value("${ali.cloud.key.secret}")
	private String aliCloudKeySecret;

	@Value("${ali.oss.endpoint}")
	private String endpoint;

	@Value("${ali.oss.lan.endpoint}")
	private String lanEndpoint;


	@Autowired private SequenceService sequenceService;
	@Autowired private WechatAppService wechatAppService;

	@Override
	public void run(String... args) {
		this.setAppId();
		this.loadApplicationValues();
		this.loadAliCloudConfigValues();
		this.loadOssConfig();
		//this.loadWechatAppValues();
	}

	private void setAppId(){
		Sequence sequence = new Sequence();
		this.sequenceService.create(sequence);
		String id = String.valueOf(sequence.getId());
		App.ID = id.substring(id.length() - 4);
	}
	
	private void loadApplicationValues(){
		App.NAME = this.appName;
		App.FILE_LOCATION = this.fileLocation;
	}

	private void loadAliCloudConfigValues(){
		com.common.constant.App.ALI_CLOUD_KEY_ID = this.aliCloudKeyId;
		com.common.constant.App.ALI_CLOUD_KEY_SECRET = this.aliCloudKeySecret;
	}

	private void loadOssConfig(){
		com.common.constant.App.FILE_BUCKET_NAME = this.bucketName;
		com.common.constant.App.FILE_ENDPOINT = this.endpoint;
		com.common.constant.App.FILE_LAN_ENDPOINT = this.lanEndpoint;
	}

	private void loadWechatAppValues(){
		WechatAppDTO wechatAppDTO = wechatAppService.findById(10000001L);
		com.common.constant.App.WEHCAT_APP_ID = wechatAppDTO.getAppid();
		com.common.constant.App.WEHCAT_APP_SECRET = wechatAppDTO.getSecret();
	}

}
