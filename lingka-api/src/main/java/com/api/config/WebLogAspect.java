package com.api.config;

import java.util.Enumeration;

import javax.servlet.http.HttpServletRequest;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.common.bean.Bean;
import com.common.bean.Response;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.query.Page;
import com.fasterxml.jackson.databind.ObjectMapper;


@Aspect
@Component
public class WebLogAspect {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private static final String REQUEST_ID = "requestId";

    @Pointcut("execution(public * com.api.controller..*.*(..))")
    public void webLog() {
    }

    @Before("webLog()")
    public void doBefore(JoinPoint joinPoint) {
        // 请求参数
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = null;
        if (attributes != null) {
            request = attributes.getRequest();
        }
//		logger.info("request url : {}",request.getRequestURL().toString());
        logger.info("execure class : {}", joinPoint.getSignature().getDeclaringTypeName());
        logger.info("execure method : {}", joinPoint.getSignature().getName());
        //打印请求头部信息
        if (request != null) {
            Enumeration<String> headerNames = request.getHeaderNames();
            if (headerNames != null) {
                while (headerNames.hasMoreElements()) {
                    String headerName = headerNames.nextElement();
                    logger.info("{} : {}", headerName, request.getHeader(headerName));
                }
            }
        }

        //打印请求参数

        if (request != null) {
            Enumeration<String> parameterNames = request.getParameterNames();
            if (parameterNames != null) {
                while (parameterNames.hasMoreElements()) {
                    String parameterName = parameterNames.nextElement();
                    logger.info("{} : {}", parameterName, request.getParameter(parameterName));
                }
            }
        }

        String requestJSON;
        //打印参数信息
        Object[] args = joinPoint.getArgs();
        if (args != null) {
            for (Object obj : args) {
                if (obj instanceof Bean || obj instanceof Page) {
                    requestJSON = this.getJSON(obj);
                    logger.info("spring mvc bean format : {}", requestJSON);
                } else {
                    logger.info("request : {}", obj);
                }
            }
        }
//		log.setToken(request.getHeader(App.HTTP_HEADER_APP_TOKEN));
//		String token = request.getHeader(App.HTTP_HEADER_APP_TOKEN);
//		log.setToken(token);
//		if(!StringUtil.isEmpty(token)){
//			JsonWebToken jsonWebToken = EncryptUtil.parseJwt(token);
//			log.setType(jsonWebToken.getType());
//			log.setUserId(jsonWebToken.getUserId());
//			log.setUsername(jsonWebToken.getUsername());
//		}
    }

    @AfterReturning(returning = "response", pointcut = "webLog()")
    public void doAfterReturning(Object response) {
        if (response instanceof Bean) {
            if (response instanceof Response) {
                ((Response<?>) response).setRequestId(MDC.get(REQUEST_ID));
            }
        }
    }

    /**
     * 请求参数拼装
     *
     */
    private String getJSON(Object obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }
}
