package com.api.validator;

import java.math.BigDecimal;

public class ProductSkuValidator extends Validator {
	public ProductSkuValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入分类ID");
			this.result = false;
		}
		return this;
	}

	public ProductSkuValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}


	public ProductSkuValidator onUnit(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入SKU单位");
			this.result = false;
		}
		return this;
	}



	public ProductSkuValidator onPrice(BigDecimal str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入SKU价格");
			this.result = false;
		}
		return this;
	}

	public ProductSkuValidator onSize(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入SKU大小");
			this.result = false;
		}
		return this;
	}

}
