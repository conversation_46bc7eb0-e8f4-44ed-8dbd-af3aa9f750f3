package com.api.validator;

import java.util.List;

import com.query.OrderTicketDetailQuery;
import com.query.OrderTicketQuery;
import com.query.ProductIngredientConfigQuery;
import com.query.ProductSkuQuery;

public class OrderValidator extends Validator {
	public OrderValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品ID");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onUserCartIds(List<Long> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入购物车ID");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onTableId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入桌子ID");
			this.result = false;
		}
		return this;
	}


	public OrderValidator onName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分名称");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onStage(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入上下架状态");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onTableFlag(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请选择是否群酒");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onSort(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分排序");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onProductSkus(List<ProductSkuQuery> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入产品SKU");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onOrderTicket(OrderTicketQuery str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入门票相关参数");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onOrderTicketDetailList(List<OrderTicketDetailQuery> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入门票详情参数");
			this.result = false;
		}
		return this;
	}

	public OrderValidator onApplyTimeStr(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入报名时间");
			this.result = false;
		}
		return this;
	}

}
