package com.api.validator;

import java.util.List;

import com.query.ProductIngredientConfigQuery;
import com.query.ProductSkuQuery;

public class ProductValidator extends Validator {
	public ProductValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品ID");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onCatalogId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入分类ID");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}


	public ProductValidator onName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分名称");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onStage(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入上下架状态");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onTableFlag(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请选择是否群酒");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onSort(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分排序");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onProductSkus(List<ProductSkuQuery> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入产品SKU");
			this.result = false;
		}
		return this;
	}

	public ProductValidator onProductIngredientConfig(ProductIngredientConfigQuery str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入小料配置");
			this.result = false;
		}
		return this;
	}

}
