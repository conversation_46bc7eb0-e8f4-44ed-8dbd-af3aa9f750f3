package com.api.validator;

import java.math.BigDecimal;

public class ProductIngredientValidator extends Validator {
	public ProductIngredientValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入小料ID");
			this.result = false;
		}
		return this;
	}

	public ProductIngredientValidator onName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入小料名字");
			this.result = false;
		}
		return this;
	}



	public ProductIngredientValidator onPrice(BigDecimal str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入小料价格");
			this.result = false;
		}
		return this;
	}

	public ProductIngredientValidator onSize(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入SKU大小");
			this.result = false;
		}
		return this;
	}

}
