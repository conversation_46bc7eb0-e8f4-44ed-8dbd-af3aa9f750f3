package com.api.validator;

public class UserTicketValidator extends Validator {

	public UserTicketValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketValidator onUserId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入零卡ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketValidator onTicketId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入门票ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketValidator onApplyTime(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入报名时间");
			this.result = false;
		}else if (!isDate(str)) {
			this.addAttribute(errors, "报名时间格式不合法，请输入yyyy-MM-dd格式");
			this.result = false;
		}
		return this;
	}

	public UserTicketValidator onStatus(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入启用/禁用状态");
			this.result = false;
		}
		return this;
	}



}
