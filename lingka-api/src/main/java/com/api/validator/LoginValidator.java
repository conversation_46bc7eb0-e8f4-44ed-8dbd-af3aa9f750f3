package com.api.validator;

import com.api.constant.App;

public class LoginValidator extends Validator {
	public LoginValidator onUsername(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入用户名");
			this.result = false;
		}
		return this;
	}
	public LoginValidator onPassword(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入密码");
			this.result = false;
		}
		return this;
	}

	public LoginValidator onOldPassword(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入密码");
			this.result = false;
		}
		return this;
	}
	
	public LoginValidator onCaptcha(String client, String server){
		if(App.PRODUCTION){
			if(this.isEmpty(client)){
				this.addAttribute(errors, "请输入短信验证码");
				this.result = false;
			}else if(this.isEmpty(server)){
				this.addAttribute(errors, "短信验证码输入错误");
				this.result = false;
			}else if(!client.equals(server)){
				this.addAttribute(errors, "短信验证码输入错误");
				this.result = false;
			}
		}
		return this;
	}

	public LoginValidator onSmsCode(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入短信验证码");
			this.result = false;
		}
		return this;
	}

}
