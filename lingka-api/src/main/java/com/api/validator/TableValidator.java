package com.api.validator;

public class TableValidator extends Validator {
	public TableValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入桌子ID");
			this.result = false;
		}
		return this;
	}

	public TableValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public TableValidator onPeopleNumber(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入人数");
			this.result = false;
		}
		return this;
	}

	public TableValidator onTableName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入桌子名称");
			this.result = false;
		}
		return this;
	}

	public TableValidator onOrderMode(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入点单模式");
			this.result = false;
		}
		return this;
	}

}
