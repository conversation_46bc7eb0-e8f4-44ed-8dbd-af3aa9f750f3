package com.api.validator;

import java.util.List;

public class UserShopValidator extends Validator {

	public UserShopValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public UserShopValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public UserShopValidator onUserId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入员工ID");
			this.result = false;
		}
		return this;
	}

	public UserShopValidator onRoleIds(List<Long> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入角色ID");
			this.result = false;
		}
		return this;
	}

	public UserShopValidator onStatus(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入启用/禁用状态");
			this.result = false;
		}
		return this;
	}



}
