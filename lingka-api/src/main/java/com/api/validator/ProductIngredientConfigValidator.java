package com.api.validator;

public class ProductIngredientConfigValidator extends Validator {
	public ProductIngredientConfigValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入分类ID");
			this.result = false;
		}
		return this;
	}

	public ProductIngredientConfigValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}


	public ProductIngredientConfigValidator onName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分名称");
			this.result = false;
		}
		return this;
	}

	public ProductIngredientConfigValidator onType(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请选择小料类型");
			this.result = false;
		}
		return this;
	}

	public ProductIngredientConfigValidator onRequired(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请选择小料是否必选");
			this.result = false;
		}
		return this;
	}




}
