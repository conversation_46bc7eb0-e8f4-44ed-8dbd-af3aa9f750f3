package com.api.validator;

import java.math.BigDecimal;
import java.util.List;

import com.query.TicketProductQuery;

public class TicketValidator extends Validator {
	public TicketValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入门票ID");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onProductNumber(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入包含的酒水数量");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onPrice(BigDecimal str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入价格");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onPriceCatalog(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入价格属性");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onStage(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入上下架状态");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入门票名称");
			this.result = false;
		}
		return this;
	}

	public TicketValidator onProductId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品ID");
			this.result = false;
		}
		return this;
	}

}
