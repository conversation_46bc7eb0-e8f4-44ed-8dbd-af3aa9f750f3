package com.api.validator;

import java.util.List;

import com.query.ProductOptionQuery;

public class ProductOptionValidator extends Validator {
    public ProductOptionValidator onId(Long str) {
        if (this.isEmpty(str)) {
            this.addAttribute(errors, "请输入分类ID");
            this.result = false;
        }
        return this;
    }

    public ProductOptionValidator onShopId(Long str) {
        if (this.isEmpty(str)) {
            this.addAttribute(errors, "请输入店铺ID");
            this.result = false;
        }
        return this;
    }


    public ProductOptionValidator onKey(String str) {
        if (this.isEmpty(str)) {
            this.addAttribute(errors, "请输入模版名称");
            this.result = false;
        }
        return this;
    }

    public ProductOptionValidator onValue(Integer str) {
        if (this.isEmpty(str)) {
            this.addAttribute(errors, "请输入模版选项");
            this.result = false;
        }
        return this;
    }

    public ProductOptionValidator onValues(List<String> str) {
        if (this.isEmpty(str) || str.isEmpty()) {
            this.addAttribute(errors, "请输入模版选项值");
            this.result = false;
        }
        return this;
    }

    public ProductOptionValidator onOptionValues(List<ProductOptionQuery> str) {
        if (this.isEmpty(str) || str.isEmpty()) {
            this.addAttribute(errors, "请输入模版选项值");
            this.result = false;
            return this;
        }
        for (ProductOptionQuery productOptionQueryEntity : str) {
            if (this.isEmpty(productOptionQueryEntity.getValue())) {
                this.addAttribute(errors, "请输入模版选项值");
                this.result = false;
            }
        }
        return this;
    }

}
