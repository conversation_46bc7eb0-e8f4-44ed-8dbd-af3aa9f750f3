package com.api.validator;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.aliyun.oss.internal.OSSUtils;
import com.common.constant.App;
import com.query.ShopOpenCloseTimeQuery;

public class ShopValidator extends Validator {
    public ShopValidator onShopName(String nickname) {
        if (this.isEmpty(nickname)) {
            this.addAttribute(errors, "请输入店铺名称");
            this.result = false;
        } else if (this.hasNotPrintable(nickname)) {
            this.addAttribute(errors, "店铺名称有不可见字符");
            this.result = false;
        }
        return this;
    }

    public ShopValidator onOwnerMobile(String ownerMobile) {
        if (this.isEmpty(ownerMobile)) {
            this.addAttribute(errors, "请输入店铺拥有者手机号");
            this.result = false;
        } else if (!this.mobile(ownerMobile)) {
            this.addAttribute(errors, "店铺拥有者手机号格式错误");
            this.result = false;
        }
        return this;
    }

    public ShopValidator onPhoto(String urls) {
        if (urls != null) {
            String[] urlList = urls.split(App.COMMA);
            if (urlList.length > 0 && !urlList[0].isEmpty()) {
                for (String key : urlList) {
                    if (!OSSUtils.validateObjectKey(key)) {
                        this.addAttribute(errors, "图片地址非法");
                        this.result = false;
                        break;
                    }
                }
            }
        }
        return this;
    }

    public ShopValidator onAddress(String address) {
        if (this.hasNotPrintable(address)) {
            this.addAttribute(errors, "地址有不可见字符");
            this.result = false;
        }
        return this;
    }

    public ShopValidator onShippingTime(List<ShopOpenCloseTimeQuery> shippingTimeList) {
        Map<String, List<ShopOpenCloseTimeQuery>> dayMap = new HashMap<>();

        for (ShopOpenCloseTimeQuery query : shippingTimeList) {
            String dayStr = query.getDay();
            if (dayStr == null) {
                return error("星期不能为空");
            }
            int day;
            try {
                day = Integer.parseInt(dayStr);
            } catch (NumberFormatException e) {
                return error("星期不是数字: " + e.getMessage());
            }
            if (day < 0 || day > 7) {
                return error("星期超出范围 (0-7)");
            }
            if (query.getOpenTime() == null || query.getCloseTime() == null) {
                return error("day " + dayStr + " 营业时间不能为空");
            }
            if (query.getOpenTime().isAfter(query.getCloseTime())) {
                return error("day " + dayStr + " 营业开始时间不能晚于结束时间");
            }

            // 按 day 分组
            dayMap.computeIfAbsent(dayStr, k -> new ArrayList<>()).add(query);
        }

        // 检查重叠
        for (Map.Entry<String, List<ShopOpenCloseTimeQuery>> entry : dayMap.entrySet()) {
            List<ShopOpenCloseTimeQuery> dayList = entry.getValue();

            // 按 openTime 排序，方便检查相邻区间
            dayList.sort(Comparator.comparing(ShopOpenCloseTimeQuery::getOpenTime));

            for (int i = 1; i < dayList.size(); i++) {
                ShopOpenCloseTimeQuery prev = dayList.get(i - 1);
                ShopOpenCloseTimeQuery curr = dayList.get(i);
                if (!prev.getCloseTime().isBefore(curr.getOpenTime())) {
                    return error("day " + entry.getKey() + " 营业时间段有重叠");
                }
            }
        }
        return this;
    }

    private ShopValidator error(String message) {
        this.addAttribute(errors, message);
        this.result = false;
        return this;
    }

}
