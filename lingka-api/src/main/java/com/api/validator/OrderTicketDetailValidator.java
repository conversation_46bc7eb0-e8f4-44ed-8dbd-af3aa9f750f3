package com.api.validator;

import java.util.List;

import com.query.ProductIngredientConfigQuery;
import com.query.ProductSkuQuery;

public class OrderTicketDetailValidator extends Validator {
	public OrderTicketDetailValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品ID");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onNumber(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入购买的门票数量");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}



	public OrderTicketDetailValidator onApplyTime(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入报名时间");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onStage(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入上下架状态");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onTableFlag(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请选择是否群酒");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onTicketId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入门票ID");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onProductSkus(List<ProductSkuQuery> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入产品SKU");
			this.result = false;
		}
		return this;
	}

	public OrderTicketDetailValidator onProductIngredientConfig(ProductIngredientConfigQuery str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入小料配置");
			this.result = false;
		}
		return this;
	}

}
