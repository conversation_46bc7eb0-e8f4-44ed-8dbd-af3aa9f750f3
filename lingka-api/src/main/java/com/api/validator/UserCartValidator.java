package com.api.validator;

import java.util.List;

public class UserCartValidator extends Validator {

	public UserCartValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onIds(List<Long> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "购物车IDS不能为空");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onProductId(Long productId){
		if(this.isEmpty(productId)){
			this.addAttribute(errors, "请输入产品ID");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onProductOptionIds(List<Long> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入产品选项");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onProductSkuId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品规格");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onNumber(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品数量");
			this.result = false;
		}else if (str < 0){
			this.addAttribute(errors, "产品数量不能小于0");
			this.result = false;
		}
		return this;
	}

	public UserCartValidator onProductIngredientIds(List<Long> str){
		if(this.isEmpty(str) || str.isEmpty()){
			this.addAttribute(errors, "请输入产品小料");
			this.result = false;
		}
		return this;
	}

}
