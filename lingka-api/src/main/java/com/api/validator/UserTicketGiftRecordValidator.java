package com.api.validator;

public class UserTicketGiftRecordValidator extends Validator {

	public UserTicketGiftRecordValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketGiftRecordValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketGiftRecordValidator onUserTicketId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入用户门票ID");
			this.result = false;
		}
		return this;
	}




}
