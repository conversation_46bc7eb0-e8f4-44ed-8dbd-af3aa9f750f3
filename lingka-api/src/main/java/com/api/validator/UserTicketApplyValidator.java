package com.api.validator;

public class UserTicketApplyValidator extends Validator {

	public UserTicketApplyValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public UserTicketApplyValidator onUsername(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入用户名");
			this.result = false;
		}
		return this;
	}

	public UserTicketApplyValidator onStage(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入审核状态");
			this.result = false;
		}
		return this;
	}

	public UserTicketApplyValidator onComment(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入备注");
			this.result = false;
		}
		return this;
	}


}
