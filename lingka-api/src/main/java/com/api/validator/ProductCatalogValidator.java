package com.api.validator;

public class ProductCatalogValidator extends Validator {
	public ProductCatalogValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入分类ID");
			this.result = false;
		}
		return this;
	}

	public ProductCatalogValidator onShopId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入店铺ID");
			this.result = false;
		}
		return this;
	}


	public ProductCatalogValidator onName(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分名称");
			this.result = false;
		}
		return this;
	}

	public ProductCatalogValidator onSort(Integer str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入细分排序");
			this.result = false;
		}
		return this;
	}

}
