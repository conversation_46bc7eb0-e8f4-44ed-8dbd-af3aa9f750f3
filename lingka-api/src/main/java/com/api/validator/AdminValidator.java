package com.api.validator;

public class AdminValidator extends Validator {

	public AdminValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入管理员ID");
			this.result = false;
		}
		return this;
	}

	public AdminValidator onUsername(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入用户名");
			this.result = false;
		}
		return this;
	}
	public AdminValidator onMobile(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入手机号");
			this.result = false;
		}
		return this;
	}

	public AdminValidator onStatus(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入启用/禁用状态");
			this.result = false;
		}
		return this;
	}



}
