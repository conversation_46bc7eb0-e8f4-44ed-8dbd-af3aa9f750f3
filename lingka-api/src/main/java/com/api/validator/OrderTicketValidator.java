package com.api.validator;

import java.util.List;

import com.query.ProductIngredientConfigQuery;
import com.query.ProductSkuQuery;

public class OrderTicketValidator extends Validator {
	public OrderTicketValidator onId(Long str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入产品ID");
			this.result = false;
		}
		return this;
	}

	public OrderTicketValidator onPhotoList(List<String> str){
		if(this.isEmpty(str) && !str.isEmpty()){
			this.addAttribute(errors, "请上传照片");
			this.result = false;
		}
		return this;
	}

	public OrderTicketValidator onApplyTime(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入报名时间");
			this.result = false;
		}
		return this;
	}

	public OrderTicketValidator onStage(String str){
		if(this.isEmpty(str)){
			this.addAttribute(errors, "请输入上下架状态");
			this.result = false;
		}
		return this;
	}



}
