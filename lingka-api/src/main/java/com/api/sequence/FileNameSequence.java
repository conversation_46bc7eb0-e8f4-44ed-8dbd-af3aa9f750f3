package com.api.sequence;

import java.util.concurrent.atomic.AtomicInteger;

public class FileNameSequence{
	private static final int SEQ_START = 10000000;
	private static final int SEQ_MAX = 99999999;
	private static final AtomicInteger ATOMIC_INTEGER = new AtomicInteger(SEQ_START);
	public static int nextValue(){
		ATOMIC_INTEGER.compareAndSet(SEQ_MAX + 1, SEQ_START);
        return ATOMIC_INTEGER.getAndIncrement();
    }
}