package com.api.controller;

import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.UserTicketGiftRecordValidator;
import com.common.bean.Response;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.common.constant.UserTicketCatalog;
import com.common.constant.UserTicketGiftRecordStage;
import com.common.constant.UserTicketStage;
import com.common.util.DateUtil;
import com.domain.UserTicket;
import com.domain.UserTicketGiftRecord;
import com.dto.UserTicketDTO;
import com.dto.UserTicketGiftRecordDTO;
import com.query.UserTicketGiftRecordQuery;
import com.github.pagehelper.PageInfo;
import com.service.UserService;
import com.service.UserTicketGiftRecordService;
import com.service.UserTicketService;

/**
 * 用户门票转赠
 *
 * <AUTHOR>
 * @date 2025-09-14
 */
@RestController
public class UserTicketGiftRecordController extends BaseController {

    @Autowired
    private UserTicketGiftRecordService userTicketGiftRecordService;
    @Autowired
    private UserTicketService userTicketService;
    @Autowired
    private UserService userService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 发起门票转赠
     */
    @RequestMapping(value = "/v1/user/ticket/gift/record/create", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> create(@RequestBody UserTicketGiftRecord userTicketGiftRecord) {
        UserTicketGiftRecordValidator userTicketGiftRecordValidator = new UserTicketGiftRecordValidator();
        if (!userTicketGiftRecordValidator.onUserTicketId(userTicketGiftRecord.getUserTicketId()).result()) {
            return new Response<>(ERROR, userTicketGiftRecordValidator.getErrorMessage());
        }
        // 给门票转赠增加分布式锁
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_TICKET_GIFT, userTicketGiftRecord.getUserTicketId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            // 查询该门票是否已经有转赠的记录，有的话，直接返回ID
            UserTicketGiftRecordQuery userTicketGiftRecordQuery = new UserTicketGiftRecordQuery();
            userTicketGiftRecordQuery.setUserTicketId(userTicketGiftRecord.getUserTicketId());
            userTicketGiftRecordQuery.setStatus(DataStatus.Y.getCode());
            userTicketGiftRecordQuery.setSourceUserId(this.getUserToken().getId());
            userTicketGiftRecordQuery.setStage(UserTicketGiftRecordStage.N.getCode());
            List<UserTicketGiftRecordDTO> userTicketGiftRecordDTOs = userTicketGiftRecordService.findAll(userTicketGiftRecordQuery);
            if (userTicketGiftRecordDTOs != null && !userTicketGiftRecordDTOs.isEmpty()) {
                return new Response<>(userTicketGiftRecordDTOs.get(0).getId());
            }
            // 查询该门票是否符合转赠条件
            UserTicketDTO userTicketDTO = userTicketService.findById(userTicketGiftRecord.getUserTicketId());
            if (userTicketDTO == null || !DataStatus.Y.getCode().equals(userTicketDTO.getStatus())) {
                return new Response<>(ERROR, "门票不存在");
            }
            if (!UserTicketStage.VERIFIED.getCode().equals(userTicketDTO.getStage())) {
                return new Response<>(ERROR, "门票状态不正确，无法转赠");
            }
            // 判断门票是否属于你
            if (!userTicketDTO.getUserId().equals(this.getUserToken().getId())) {
                return new Response<>(ERROR, "门票不属于您，无法转赠");
            }
            // 判断门票是否过期
            Date datetime = this.getServerTime();
            // 分隔时间节点
            Date splitTime = this.getDatetime(DateUtil.format(datetime, DATE_FORMAT) + " " + App.USER_TICKET_SPLIT_TIME, DATETIME_FORMAT);
            // 以当前时间的中午十二点为界限，当日中午十二点前生成的是前一日，当日中午十二点后生成的是当日
            Date applyTime = userTicketDTO.getApplyTime();
            if (datetime.before(splitTime)) {
                // 判断报名时间是否是前一天之后，如果不是则提示门票已经过期，无法转赠
                if (applyTime.before(DateUtil.getFutureDay(datetime, -1))) {
                    return new Response<>(ERROR, "门票已过期，无法转赠");
                }
            } else {
                // 判断报名时间是否今天之后，如果不是则提示门票已经过期，无法转赠
                if (applyTime.before(DateUtil.getDayStartTime(datetime))) {
                    return new Response<>(ERROR, "门票已过期，无法转赠");
                }
            }
            userTicketGiftRecord.setSourceUserId(this.getUserToken().getId());
            userTicketGiftRecord.setUserTicketId(userTicketDTO.getId());
            userTicketGiftRecord.setStage(UserTicketGiftRecordStage.N.getCode());
            userTicketGiftRecord.setStatus(DataStatus.Y.getCode());
            userTicketGiftRecord.setModifyTime(datetime);
            userTicketGiftRecord.setCreateTime(datetime);
            userTicketGiftRecordService.create(userTicketGiftRecord);
            return new Response<>(userTicketGiftRecord.getId());
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }


    /**
     * 接受门票转赠
     */
    @RequestMapping(value = "/v1/user/ticket/gift/record/accept", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> accept(@RequestBody UserTicketGiftRecord userTicketGiftRecord) {
        UserTicketGiftRecordValidator userTicketGiftRecordValidator = new UserTicketGiftRecordValidator();
        if (!userTicketGiftRecordValidator.onId(userTicketGiftRecord.getId()).result()) {
            return new Response<>(ERROR, userTicketGiftRecordValidator.getErrorMessage());
        }
        UserTicketGiftRecordDTO userTicketGiftRecordDTO = userTicketGiftRecordService.findById(userTicketGiftRecord.getId());
        if (userTicketGiftRecordDTO == null || !DataStatus.Y.getCode().equals(userTicketGiftRecordDTO.getStatus())) {
            return new Response<>(ERROR, "转赠记录不存在");
        }
        // 增加分布式锁
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_TICKET_GIFT, userTicketGiftRecordDTO.getUserTicketId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿点击过快");
        }
        try {
            if (!UserTicketGiftRecordStage.N.getCode().equals(userTicketGiftRecordDTO.getStage())) {
                return new Response<>(ERROR, "门票已经被领取");
            }
            if (userTicketGiftRecordDTO.getSourceUserId().equals(this.getUserToken().getId())) {
                return new Response<>(ERROR, "不能接受自己的转赠");
            }
            userTicketGiftRecord.setTargetUserId(this.getUserToken().getId());
            userTicketGiftRecord.setStage(UserTicketGiftRecordStage.Y.getCode());
            userTicketGiftRecord.setModifyTime(this.getServerTime());
            // 修改门票归属人
            UserTicket userTicket = new UserTicket();
            userTicket.setId(userTicketGiftRecordDTO.getUserTicketId());
            userTicket.setUserId(this.getUserToken().getId());
            userTicket.setCatalog(UserTicketCatalog.OTHER_GIFT.getCode());
            userTicket.setModifyTime(this.getServerTime());
            userTicketGiftRecordService.modifyById(userTicketGiftRecord,userTicket);
            return new Response<>(OK, SUCCESS);
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return new Response<>(ERROR, FAILURE);
        }finally {
            lock.unlock();
        }
    }


    /**
     * 分页查询用户门票转赠记录列表
     */
    @RequestMapping(value = "/v1/user/ticket/gift/record/query", method = RequestMethod.POST)
    public Response<PageInfo<UserTicketGiftRecordDTO>> query(@RequestBody UserTicketGiftRecordQuery userTicketGiftRecordQuery) {
        userTicketGiftRecordQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserTicketGiftRecordDTO> pageInfo = userTicketGiftRecordService.find(userTicketGiftRecordQuery);
        return new Response<>(pageInfo);
    }


//    /**
//     * 修改用户门票转赠记录
//     */
//    @RequestMapping(value = "/v1/user/ticket/gift/record/modify", method = RequestMethod.POST)
//    public Response<?> modify(@RequestBody UserTicketGiftRecord userTicketGiftRecord) {
//        Date datetime = this.getServerTime();
//        userTicketGiftRecord.setModifyTime(datetime);
//        userTicketGiftRecordService.modifyById(userTicketGiftRecord);
//        return new Response<>(OK, SUCCESS);
//    }

//    /**
//     * 删除用户门票转赠记录
//     */
//    @RequestMapping(value = "/v1/user/ticket/gift/record/remove", method = RequestMethod.POST)
//    public Response<?> remove(@RequestBody UserTicketGiftRecordQuery request) {
//        Date datetime = this.getServerTime();
//        UserTicketGiftRecord userTicketGiftRecord = new UserTicketGiftRecord();
//        userTicketGiftRecord.setId(request.getId());
//        userTicketGiftRecord.setStatus(DataStatus.N.getCode());
//        userTicketGiftRecord.setModifyTime(datetime);
//        userTicketGiftRecordService.modifyById(userTicketGiftRecord);
//        return new Response<>(OK, SUCCESS);
//    }


}
