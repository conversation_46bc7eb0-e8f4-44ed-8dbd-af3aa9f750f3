package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.domain.UserShopHistory;
import com.dto.ShopConfigDTO;
import com.dto.UserShopHistoryDTO;
import com.query.UserShopHistoryQuery;
import com.github.pagehelper.PageInfo;
import com.service.ShopConfigService;
import com.service.UserShopHistoryService;

/**
 * 用户浏览店铺历史记录接口
 *
 */
@RestController
public class UserShopHistoryController extends BaseController {

    @Autowired
    private UserShopHistoryService userShopHistoryService;
    @Autowired
    private ShopConfigService shopConfigService;

    /**
     * 增加用户浏览店铺历史记录
     */
    @Token(tokenType = {TokenType.C})
    @RequestMapping(value = "/v1/user/shop/history/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody UserShopHistory userShopHistory) {
        Long shopId = userShopHistory.getShopId();
        if (shopId == null) {
            return new Response<>(ERROR, "店铺ID不能为空");
        }
        ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(shopId);
        if (shopConfigDTO == null || DataStatus.N.getCode().equals(shopConfigDTO.getStatus())) {
            return new Response<>(ERROR, FAILURE + "：店铺配置不存在");
        }
        Long userId = getUserToken().getId();
        Date datetime = this.getServerTime();
        userShopHistory.setUserId(userId);
        userShopHistory.setStatus(DataStatus.Y.getCode());
        userShopHistory.setModifyTime(datetime);
        int count = 0;
        UserShopHistoryQuery query = new UserShopHistoryQuery();
        query.setUserId(userId);
        query.setShopId(shopId);
        List<UserShopHistoryDTO> list = userShopHistoryService.findAll(query);
        if (!list.isEmpty()) {
            UserShopHistoryDTO existingRecord = list.get(0);
            userShopHistory.setId(existingRecord.getId());
            count += userShopHistoryService.modifyById(userShopHistory);
        } else {
            count += userShopHistoryService.create(userShopHistory);
        }
        if (count > 0) {
            return new Response<>(OK, SUCCESS);
        }
        return new Response<>(ERROR, FAILURE);
    }

    /**
     * 分页查询用户浏览店铺历史记录列表
     */
    @Token(tokenType = {TokenType.C})
    @RequestMapping(value = "/v1/user/shop/history/query", method = RequestMethod.POST)
    public Response<PageInfo<UserShopHistoryDTO>> query(@RequestBody UserShopHistoryQuery userShopHistoryQuery) {
        Long userId = getUserToken().getId();
        userShopHistoryQuery.setShopId(null);
        userShopHistoryQuery.setUserId(userId);
        userShopHistoryQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserShopHistoryDTO> pageInfo = userShopHistoryService.find(userShopHistoryQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询用户浏览店铺历史记录列表
     */
    @Token(tokenType = {TokenType.C})
    @RequestMapping(value = "/v1/user/shop/history/all/my/query", method = RequestMethod.POST)
    public Response<List<UserShopHistoryDTO>> queryAll() {
        UserShopHistoryQuery userShopHistoryQuery = new UserShopHistoryQuery();
        Long userId = getUserToken().getId();
        userShopHistoryQuery.setShopId(null);
        userShopHistoryQuery.setUserId(userId);
        userShopHistoryQuery.setStatus(DataStatus.Y.getCode());
        List<UserShopHistoryDTO> userShopHistoryDTOs = userShopHistoryService.findAll(userShopHistoryQuery);
        return new Response<>(userShopHistoryDTOs);
    }

    /**
     * 删除用户浏览店铺历史记录
     */
    @Token(tokenType = {TokenType.C})
    @RequestMapping(value = "/v1/user/shop/history/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody UserShopHistoryQuery request) {
        if (request.getShopId() == null) {
            return new Response<>(ERROR, "店铺ID不能为空");
        }
        Long userId = getUserToken().getId();
        request.setUserId(userId);
        Date datetime = this.getServerTime();
        List<UserShopHistoryDTO> dtoList = userShopHistoryService.findAll(request);
        int count = 0;
        if (!dtoList.isEmpty()) {
            UserShopHistoryDTO existingRecord = dtoList.get(0);
            UserShopHistory userShopHistory = new UserShopHistory();
            userShopHistory.setId(existingRecord.getId());
            userShopHistory.setStatus(DataStatus.N.getCode());
            userShopHistory.setModifyTime(datetime);
            count = userShopHistoryService.modifyById(userShopHistory);
        }
        if (count > 0) {
            return new Response<>(OK, SUCCESS);
        }
        return new Response<>(ERROR, FAILURE);
    }

    /**
     * 清空用户浏览店铺历史记录
     */
    @Token(tokenType = {TokenType.C})
    @RequestMapping(value = "/v1/user/shop/history/remove/all", method = RequestMethod.POST)
    public Response<?> removeAll() {
        Long userId = getUserToken().getId();
        int count = userShopHistoryService.removeByUserId(userId);
        if (count > 0) {
            return new Response<>(OK, SUCCESS);
        }
        return new Response<>(ERROR, FAILURE);
    }
}
