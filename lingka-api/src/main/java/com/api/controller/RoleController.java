package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Role;
import com.dto.RoleDTO;
import com.query.RoleQuery;
import com.github.pagehelper.PageInfo;
import com.service.RoleService;

/**
 * 角色控制器
 *
 * <AUTHOR>
 * @date 2025-08-17
 */
@RestController
public class RoleController extends BaseController {

    @Autowired
    private RoleService roleService;

    /**
     * 增加角色
     */
    @RequestMapping(value = "/v1/role/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody Role role) {
        Date datetime = this.getServerTime();
        role.setStatus(DataStatus.Y.getCode());
        role.setModifyTime(datetime);
        role.setCreateTime(datetime);
        roleService.create(role);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询角色列表
     */
    @RequestMapping(value = "/v1/role/query", method = RequestMethod.POST)
    public Response<PageInfo<RoleDTO>> query(@RequestBody RoleQuery roleQuery) {
        roleQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<RoleDTO> pageInfo = roleService.find(roleQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询角色列表
     */
    @RequestMapping(value = "/v1/role/all/query", method = RequestMethod.POST)
    public Response<List<RoleDTO>> queryAll(@RequestBody RoleQuery roleQuery) {
        roleQuery.setStatus(DataStatus.Y.getCode());
        List<RoleDTO> roleDTOs = roleService.findAll(roleQuery);
        return new Response<>(roleDTOs);
    }


    /**
     * 修改角色
     */
    @RequestMapping(value = "/v1/role/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Role role) {
        Date datetime = this.getServerTime();
        role.setModifyTime(datetime);
        roleService.modifyById(role);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除角色
     */
    @RequestMapping(value = "/v1/role/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody RoleQuery request) {
        Date datetime = this.getServerTime();
        Role role = new Role();
        role.setId(request.getId());
        role.setStatus(DataStatus.N.getCode());
        role.setModifyTime(datetime);
        roleService.modifyById(role);
        return new Response<>(OK, SUCCESS);
    }


}
