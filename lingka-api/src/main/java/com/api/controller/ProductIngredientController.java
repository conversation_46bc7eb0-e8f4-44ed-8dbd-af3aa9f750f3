package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ProductIngredient;
import com.dto.ProductIngredientDTO;
import com.query.ProductIngredientQuery;
import com.github.pagehelper.PageInfo;
import com.service.ProductIngredientService;

/**
 * 产品小料控制器
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
public class ProductIngredientController extends BaseController {

    @Autowired
    private ProductIngredientService productIngredientService;

    /**
     * 增加产品小料
     */
    @RequestMapping(value = "/v1/product/ingredient/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody ProductIngredient productIngredient) {
        Date datetime = this.getServerTime();
        productIngredient.setStatus(DataStatus.Y.getCode());
        productIngredient.setModifyTime(datetime);
        productIngredient.setCreateTime(datetime);
        productIngredientService.create(productIngredient);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询产品小料列表
     */
    @RequestMapping(value = "/v1/product/ingredient/query", method = RequestMethod.POST)
    public Response<PageInfo<ProductIngredientDTO>> query(@RequestBody ProductIngredientQuery productIngredientQuery) {
        productIngredientQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProductIngredientDTO> pageInfo = productIngredientService.find(productIngredientQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询产品小料列表
     */
    @RequestMapping(value = "/v1/product/ingredient/all/query", method = RequestMethod.POST)
    public Response<List<ProductIngredientDTO>> queryAll(@RequestBody ProductIngredientQuery productIngredientQuery) {
        productIngredientQuery.setStatus(DataStatus.Y.getCode());
        List<ProductIngredientDTO> productIngredientDTOs = productIngredientService.findAll(productIngredientQuery);
        return new Response<>(productIngredientDTOs);
    }


    /**
     * 修改产品小料
     */
    @RequestMapping(value = "/v1/product/ingredient/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody ProductIngredient productIngredient) {
        Date datetime = this.getServerTime();
        productIngredient.setModifyTime(datetime);
        productIngredientService.modifyById(productIngredient);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除产品小料
     */
    @RequestMapping(value = "/v1/product/ingredient/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ProductIngredientQuery request) {
        Date datetime = this.getServerTime();
        ProductIngredient productIngredient = new ProductIngredient();
        productIngredient.setId(request.getId());
        productIngredient.setStatus(DataStatus.N.getCode());
        productIngredient.setModifyTime(datetime);
        productIngredientService.modifyById(productIngredient);
        return new Response<>(OK, SUCCESS);
    }


}
