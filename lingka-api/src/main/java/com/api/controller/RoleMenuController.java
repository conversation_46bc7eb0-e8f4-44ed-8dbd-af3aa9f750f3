package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.RoleMenu;
import com.dto.RoleMenuDTO;
import com.query.RoleMenuQuery;
import com.github.pagehelper.PageInfo;
import com.service.RoleMenuService;

/**
 * 角色菜单关联控制器
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@RestController
public class RoleMenuController extends BaseController {

    @Autowired
    private RoleMenuService roleMenuService;

    /**
     * 增加角色菜单关联
     */
    @RequestMapping(value = "/v1/role/menu/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody RoleMenu roleMenu) {
        Date datetime = this.getServerTime();
        roleMenu.setStatus(DataStatus.Y.getCode());
        roleMenu.setModifyTime(datetime);
        roleMenu.setCreateTime(datetime);
        roleMenuService.create(roleMenu);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询角色菜单关联列表
     */
    @RequestMapping(value = "/v1/role/menu/query", method = RequestMethod.POST)
    public Response<PageInfo<RoleMenuDTO>> query(@RequestBody RoleMenuQuery roleMenuQuery) {
        roleMenuQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<RoleMenuDTO> pageInfo = roleMenuService.find(roleMenuQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询角色菜单关联列表
     */
    @RequestMapping(value = "/v1/role/menu/all/query", method = RequestMethod.POST)
    public Response<List<RoleMenuDTO>> queryAll(@RequestBody RoleMenuQuery roleMenuQuery) {
        roleMenuQuery.setStatus(DataStatus.Y.getCode());
        List<RoleMenuDTO> roleMenuDTOs = roleMenuService.findAll(roleMenuQuery);
        return new Response<>(roleMenuDTOs);
    }


    /**
     * 修改角色菜单关联
     */
    @RequestMapping(value = "/v1/role/menu/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody RoleMenu roleMenu) {
        Date datetime = this.getServerTime();
        roleMenu.setModifyTime(datetime);
        roleMenuService.modifyById(roleMenu);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除角色菜单关联
     */
    @RequestMapping(value = "/v1/role/menu/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody RoleMenuQuery request) {
        Date datetime = this.getServerTime();
        RoleMenu roleMenu = new RoleMenu();
        roleMenu.setId(request.getId());
        roleMenu.setStatus(DataStatus.N.getCode());
        roleMenu.setModifyTime(datetime);
        roleMenuService.modifyById(roleMenu);
        return new Response<>(OK, SUCCESS);
    }


}
