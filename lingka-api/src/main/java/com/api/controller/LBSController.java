package com.api.controller;

import com.api.constant.App;
import com.api.validator.Validator;
import com.common.bean.TiandituGeocoderLocation;
import com.common.bean.TiandituGeocoderToAddressResult;
import com.common.util.LbsUtil;
import com.common.util.LbsUtil.Point;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.common.bean.Response;
import com.common.constant.TokenType;

/**
 * 地理位置服务
 */
@RestController
@RequestMapping("/v1/lbs")
public class LBSController extends BaseController {
    /**
     * 地理编码与逆地理编码
     * <p>
     * 请求参数二选一传
     *
     * @param location 经纬度（GCJ02坐标系），格式：location=lat<纬度>,lng<经度>坐标。示例：location=39.92594,116.37304
     * @param address  要解析获取坐标及相关信息的输入地址。示例：address=北京市海淀区彩和坊路海淀西大街74号
     * @return 地址详情或者坐标
     * @see <a href="http://lbs.tianditu.gov.cn/server/geocodinginterface.html">天地图API>WEB服务API>地理编码接口</a>
     * @see <a href="http://lbs.tianditu.gov.cn/server/geocoding.html">天地图API>WEB服务API>逆地理编码查询</a>
     */
    @GetMapping("/geocoder")
    @Token(tokenType = {TokenType.S, TokenType.C})
    @ResponseBody
    public Response<?> geocoder(@RequestParam(name = "location", required = false) String location, @RequestParam(name = "address", required = false) String address) {
        if (address != null && !address.isEmpty()) {
            Validator validator = new Validator();
            if (validator.hasNotPrintable(address)) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
            TiandituGeocoderLocation loc = LbsUtil.addressToLocation(address);
            if (loc != null) {
                return new Response<>(OK, SUCCESS, loc);
            } else {
                return new Response<>(ERROR, FAILURE + "：地址解析失败");
            }
        } else if (location != null && !location.isEmpty()) {
            String[] poi = location.split(App.COMMA);
            if (poi.length == 2) {
                String lat = poi[0];
                String lon = poi[1];
                Point point = new Point(Double.parseDouble(lat), Double.parseDouble(lon));
                TiandituGeocoderToAddressResult result = LbsUtil.pointToLocation(point);
                if (result != null) {
                    return new Response<>(result);
                }
            } else {
                return new Response<>(ERROR, FAILURE + "：坐标格式错误");
            }
        }
        return new Response<>(ERROR, FAILURE);
    }
}
