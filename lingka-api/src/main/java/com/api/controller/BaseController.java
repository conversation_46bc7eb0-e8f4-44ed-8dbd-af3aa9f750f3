package com.api.controller;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.locks.Lock;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.http.client.utils.URLEncodedUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.integration.redis.util.RedisLockRegistry;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import com.api.constant.App;
import com.api.sequence.CommonSequence;
import com.api.sequence.FileNameSequence;
import com.common.bean.DeviceToken;
import com.common.bean.JsonWebToken;
import com.common.bean.NameValuePair;
import com.common.bean.ResponseHandler;
import com.common.bean.UserToken;
import com.common.util.DateUtil;
import com.common.util.EncryptUtil;
import com.common.util.FileUtil;
import com.common.util.IPUtil;
import com.common.util.NumberUtil;
import com.common.util.StringUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class BaseController {
    protected final String TITLE = App.TITLE;
    protected final String NOTIFY = App.NOTIFY;
    protected final String OK = ResponseHandler.OK;
    protected final String SUCCESS = ResponseHandler.SUCCESS;
    protected final String ERROR = ResponseHandler.ERROR;
    protected final String FAILURE = ResponseHandler.FAILURE;
    protected final String FORBIDDEN = ResponseHandler.FORBIDDEN;
    protected final String FORBIDDEN_MESSAGE = ResponseHandler.FORBIDDEN_MESSAGE;
    protected final String LOGOUT = ResponseHandler.LOGOUT;
    protected final String LOGOUT_MESSAGE = ResponseHandler.LOGOUT_MESSAGE;
    protected final int TAGS_LIST_MAX = 3;
    protected final int TAGS_VIEW_MAX = 5;
    protected final String DATE_FORMAT = "yyyy-MM-dd";
    protected final String CHINESE_DATE_FORMAT = "yyyy年MM月dd日";
    protected final String UNDIVIDED_DATE_FORMAT = "yyyyMMdd";
    protected final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    protected final long LOCK_MAX_WAITTIME = 1L;
    protected final String EMPTY_MESSAGE = App.EMPTY_MESSAGE;
    protected final String EMPTY = "";
    final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    HttpServletRequest httpServletRequest;

    @Autowired
    HttpServletResponse httpServletResponse;

    @Autowired
    HttpSession session;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;


    //	@InitBinder
//	protected void initBinder(WebDataBinder binder) {
//		binder.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd"), true));
//	}
    String getClientIP() {
        return IPUtil.getClientIP(httpServletRequest);
    }

    String getServerName() {
//		return this.httpServletRequest.getHeader("X-Forwarded-Proto") + "://" + this.httpServletRequest.getServerName();
        return "http://127.0.0.1:" + httpServletRequest.getServerPort();
    }

    /**
     * 获取订单号
     *
     * @return
     */
    String getOrderCode() {
        return this.getDateTime() + App.ID + CommonSequence.nextValue();
    }

    String getFlowCode() {
        return com.common.constant.App.USER_FLOW_PREFIX + this.getDateTime() + App.ID + CommonSequence.nextValue();
    }

    DeviceToken getDeviceToken() {
        DeviceToken deviceToken = null;
        try {
            String token = httpServletRequest.getHeader(App.HTTP_HEADER_APP_TOKEN);
            if (!this.isEmpty(token)) {
                deviceToken = new DeviceToken();
                JsonWebToken jsonWebToken = EncryptUtil.parseJwt(token);
                deviceToken.setId(Integer.parseInt(jsonWebToken.getSubject()));
                deviceToken.setCompanyId(Integer.parseInt(jsonWebToken.getIssuer()));
            } else {
                logger.info("{} not found", App.HTTP_HEADER_APP_TOKEN);
//				throw new RuntimeException(this.append(App.HTTP_HEADER_APP_TOKEN," not found"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return deviceToken;
    }

    String redirect(String url) {
        return "redirect:" + url;
    }

    String redirect(String url, NameValuePair... pairs) {
        return "redirect:" + StringUtil.getRequestParameters(url, pairs);
    }

    String forward(String url) {
        return url;
    }

    String forward(String url, NameValuePair... pairs) {
        return StringUtil.getRequestParameters(url, pairs);
    }

    Boolean isEmpty(String str) {
        if (str != null) {
            str = str.trim();
            return str.isEmpty() || "null".equalsIgnoreCase(str) || "undefined".equalsIgnoreCase(str);
        }
        return true;
    }

    Boolean isEmpty(Object obj) {
        if (obj != null) {
            return "".equals(obj) || "null".equals(obj) || "undefined".equals(obj);
        }
        return true;
    }

    String getEmpty(String str) {
        if (this.isEmpty(str)) {
            return EMPTY;
        } else {
            return str;
        }
    }

    String getDateTime() {
        return DateUtil.getServerTime("yyyyMMddHHmmss");
    }

    String getDateTime(Date date, String format) {
        return DateUtil.format(date, format);
    }

    Date getServerTime() {
        return DateUtil.getServerTime();
    }

    String getServerYear() {
        return DateUtil.getServerTime("yyyy");
    }

    String getJSON(Object obj) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    Object getObject(String str, Class<?> clazz) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        try {
            return objectMapper.readValue(str, clazz);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    String[] getImageArray(String str) {
        if (!this.isEmpty(str)) {
            String[] array = str.split(App.COMMA);
            int length = array.length;
            String[] images = new String[length];
            for (int i = 0; i < length; i++) {
                //文件扩展名
                String fileName = null;
                String fileExtension = null;
                if (array[i] != null) {
                    int pos = array[i].lastIndexOf(".");
                    fileName = array[i].substring(0, pos);
                    fileExtension = array[i].substring(pos);
                }
//					images[i] = this.append(EncryptUtil.encodeWithAES(fileName),fileExtension);
                images[i] = this.append(fileName, fileExtension);
            }
            return images;
        }
        return new String[0];
    }

    String format(String str) {
        return NumberUtil.format(str, "0.00");
    }

    String getImage(String str) {
        if (!this.isEmpty(str)) {
            return this.append(this.getServerName(), "/v1/file/image?url=", str);
        }
        return null;
    }

    String append(String... strs) {
        StringBuilder builder = new StringBuilder();
        if (strs != null) {
            for (String str : strs) {
                if (!this.isEmpty(str)) {
                    builder.append(str);
                }
            }
        }
        return builder.toString();
    }

    String getFormatDate(Date date) {
        return DateUtil.format(date, "yyyy-MM-dd");
    }

    String[] getNoEmptyStrArray(String[] strs) {
        List<String> list = new ArrayList<>();
        if (strs != null) {
            for (String str : strs) {
                if (!this.isEmpty(str)) {
                    list.add(str);
                }
            }
        }
        return list.toArray(new String[0]);
    }

    BigDecimal getBigDecimal(String str) {
        if (isEmpty(str)) {
            return null;
        }
        return new BigDecimal(str);
    }

    BigDecimal getPrice(BigDecimal bigDecimal) {
        if (isEmpty(bigDecimal)) {
            return null;
        }
        return bigDecimal.setScale(0, RoundingMode.HALF_UP);
    }

    Integer getInteger(String str) {
        if (isEmpty(str)) {
            return null;
        }
        return Integer.valueOf(str);
    }

    String getString(BigDecimal bigDecimal) {
        if (bigDecimal != null) {
            return bigDecimal.toString();
        }
        return null;
    }

    String getString(Long l) {
        if (l != null) {
            return l.toString();
        }
        return null;
    }

    String getString(Integer integer) {
        if (integer != null) {
            return integer.toString();
        }
        return null;
    }

    Date getDatetime(String datetime) {
        return this.getDatetime(datetime, "yyyyMMddHHmmss");
    }

    Date getDatetime(String datetime, String format) {
        if (datetime != null) {
            return DateUtil.parse(datetime, format);
        }
        return null;
    }

    String getString(String[] strs) {
        if (strs != null) {
            return StringUtils.arrayToDelimitedString(strs, App.COMMA);
        }
        return null;
    }

    Lock getLock(RedisTemplate<String, String> redisTemplate, String lockKeyPrefix, Object lockKey) {
        return new RedisLockRegistry(redisTemplate.getConnectionFactory(), lockKeyPrefix).obtain(lockKey);
    }

    /**
     * 24小时内显示今天
     *
     * @param date
     * @return
     */
    String getDatetimeDisplay(Date date) {
        Date datetime = getServerTime();
        if (DateUtil.getFutureDay(date, 1).after(datetime)) {
            return "今天";
        } else if (DateUtil.getFutureDay(date, 2).after(datetime)) {
            return "一天前";
        } else if (DateUtil.getFutureDay(date, 3).after(datetime)) {
            return "二天前";
        } else if (DateUtil.getFutureDay(date, 4).after(datetime)) {
            return "三天前";
        } else if (DateUtil.getFutureDay(date, 5).after(datetime)) {
            return "四天前";
        } else if (DateUtil.getFutureDay(date, 6).after(datetime)) {
            return "五天前";
        } else if (DateUtil.getFutureDay(date, 7).after(datetime)) {
            return getDateTime(date, DATE_FORMAT);
        } else {
            return getDateTime(date, DATE_FORMAT);
        }
    }

    String getView(String value) {
        if (this.isEmpty(value)) {
            return EMPTY_MESSAGE;
        } else {
            return value;
        }
    }

    String getMobile(String str) {
        if (!isEmpty(str)) {
            return str.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        }
        return null;
    }

    UserToken getUserToken() {
        UserToken userToken = null;
        try {
            String token = httpServletRequest.getHeader(App.HTTP_HEADER_APP_TOKEN);
            if (!this.isEmpty(token)) {
                userToken = new UserToken();
                JsonWebToken jsonWebToken = EncryptUtil.parseJwt(token);
                userToken.setId(jsonWebToken.getUserId());
            } else {
                logger.info("{} not found", App.HTTP_HEADER_APP_TOKEN);
//				throw new RuntimeException(this.append(App.HTTP_HEADER_APP_TOKEN," not found"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return userToken;
    }

    @Nullable
    JsonWebToken getRawJsonWebToken() {
        JsonWebToken jsonWebToken = null;
        try {
            String token = httpServletRequest.getHeader(App.HTTP_HEADER_APP_TOKEN);
            if (!this.isEmpty(token)) {
                jsonWebToken = EncryptUtil.parseJwt(token);
            } else {
                logger.info("{} not found", App.HTTP_HEADER_APP_TOKEN);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return jsonWebToken;
    }

    String getName(String fileName) {
        fileName = fileName.replace("\\", "/");
        int index = fileName.lastIndexOf("/");
        if (index == -1) {
            return fileName;
        }
        return fileName.substring(index + 1);
    }

    String getExtension(String fileName) {
        fileName = this.getName(fileName);
        int index = fileName.lastIndexOf(App.DOT);
        if (index == -1) {
            return "";
        }
        return fileName.substring(index);
    }

    File getDestFile(String fileName) {
        String datetime = DateUtil.getServerTime("yyyyMMddHHmmss");
        String dir = App.FILE_LOCATION +
                "/" + datetime.substring(0, 4) +
                "/" + datetime.substring(4, 6) +
                "/" + datetime.substring(6, 8) +
                "/" + datetime.substring(8, 10) +
                "/" + datetime.substring(10, 12);
        FileUtil.make(dir);
        return new File(dir, datetime.substring(12, 14) +
                App.ID +
                FileNameSequence.nextValue() +
                this.getExtension(fileName));
    }

    String getOssUrl(String fileName) {
        return getFileDir() + "/" + getUniqueFileName(fileName);
    }

    String getFileDir() {
        String datetime = DateUtil.getServerTime("yyyyMMddHHmmss");
        return datetime.substring(0, 4) +
                "/" + datetime.substring(4, 6) +
                "/" + datetime.substring(6, 8) +
                "/" + datetime.substring(8, 10) +
                "/" + datetime.substring(10, 12);
    }

    String getUniqueFileName(String fileName) {
        String datetime = DateUtil.getServerTime("yyyyMMddHHmmss");
        return datetime.substring(12, 14) +
                App.ID +
                FileNameSequence.nextValue() +
                this.getExtension(fileName);
    }

    String getFileSystemUrl(String url) {
        return this.getServerName() + "/v1/file/download?url=" + url;
    }

    /**
     * 获取推广凭证
     *
     * @param url
     * @return
     */
    String getAdToken(String url) {
        String query = url.substring(url.indexOf("?") + 1);
        List<org.apache.http.NameValuePair> nameValuePairs = URLEncodedUtils.parse(query, StandardCharsets.UTF_8);
        for (org.apache.http.NameValuePair nameValuePair : nameValuePairs) {
            if ("token".equalsIgnoreCase(nameValuePair.getName())) {
                return nameValuePair.getValue();
            }
        }
        return null;
    }

    String getAdClient(String url) {
        String query = url.substring(url.indexOf("?") + 1);
        List<org.apache.http.NameValuePair> nameValuePairs = URLEncodedUtils.parse(query, StandardCharsets.UTF_8);
        for (org.apache.http.NameValuePair nameValuePair : nameValuePairs) {
            if ("client".equalsIgnoreCase(nameValuePair.getName())) {
                return nameValuePair.getValue();
            }
        }
        return null;
    }

    /**
     * 生成唯一编号
     *
     * @return
     */
    String getUniqueNo() {
        return this.append(this.getDateTime(), App.ID, Integer.toString(CommonSequence.nextValue()));
    }

    /**
     * 直播课程直播时间段格式化
     *
     * @param startDate
     * @param length
     * @return
     * <AUTHOR>
     */
    public String startTime(Date startDate, String length) {
        long lengthTime = this.isEmpty(length) ? Long.valueOf("0") : Long.valueOf(length);
        long startTime = this.isEmpty(startDate) ? Long.valueOf("0") : Long.valueOf(startDate.getTime());
        long endTime = startTime + lengthTime * 60 * 1000;
        startDate = new Date(startTime);
        Date endDate = new Date(endTime);
        DateFormat df = new SimpleDateFormat("MM月dd日 HH:mm:ss");
        String startDateString = df.format(startDate);
        String endDateString = df.format(endDate);
        StringBuilder stringBuilder = new StringBuilder();
        StringBuilder append = stringBuilder.append(startDateString, 0, startDateString.lastIndexOf(":"))
                .append("-")
                .append(endDateString.substring(endDateString.indexOf(" "), endDateString.lastIndexOf(":")).trim());
        return append.toString();
    }

    String getEncryptionName(String name) {
        if (!isEmpty(name)) {
            return name.substring(0, name.length() - 1).replaceAll("(?<=.).", App.MULTI) + (name.length() - 1 == 1 ? App.MULTI : name.charAt(name.length() - 1));
        }
        return null;
    }

}
