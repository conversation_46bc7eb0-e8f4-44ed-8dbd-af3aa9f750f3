package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.ProductCatalogValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.domain.ProductCatalog;
import com.dto.ProductCatalogDTO;
import com.query.ProductCatalogQuery;
import com.github.pagehelper.PageInfo;
import com.service.ProductCatalogService;

/**
 * 酒品细分
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
public class ProductCatalogController extends BaseController {

    @Autowired
    private ProductCatalogService productCatalogService;

    /**
     * 增加酒品细分
     */
    @RequestMapping(value = "/v1/product/catalog/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> create(@RequestBody ProductCatalog productCatalog) {
        ProductCatalogValidator validator = new ProductCatalogValidator();
        if (!validator.onName(productCatalog.getName()).onShopId(productCatalog.getShopId()).onSort(productCatalog.getSort()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        // 查询该店铺下是否存在同名的酒品小料
        ProductCatalogQuery productCatalogQuery = new ProductCatalogQuery();
        productCatalogQuery.setShopId(productCatalog.getShopId());
        productCatalogQuery.setName(productCatalog.getName());
        productCatalogQuery.setStatus(DataStatus.Y.getCode());
        List<ProductCatalogDTO> productCatalogDTOs = productCatalogService.findAll(productCatalogQuery);
        if (productCatalogDTOs != null && !productCatalogDTOs.isEmpty()) {
            return new Response<>(ERROR, "该店铺下已存在该酒品细分名称");
        }
        Date datetime = this.getServerTime();
        productCatalog.setStatus(DataStatus.Y.getCode());
        productCatalog.setModifyTime(datetime);
        productCatalog.setCreateTime(datetime);
        productCatalogService.create(productCatalog);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询酒品细分
     */
    @RequestMapping(value = "/v1/product/catalog/query", method = RequestMethod.POST)
    public Response<PageInfo<ProductCatalogDTO>> query(@RequestBody ProductCatalogQuery productCatalogQuery) {
        productCatalogQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProductCatalogDTO> pageInfo = productCatalogService.find(productCatalogQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询酒品小料列表
     */
    @RequestMapping(value = "/v1/product/catalog/all/query", method = RequestMethod.POST)
    public Response<List<ProductCatalogDTO>> queryAll(@RequestBody ProductCatalogQuery productCatalogQuery) {
        productCatalogQuery.setStatus(DataStatus.Y.getCode());
        List<ProductCatalogDTO> productCatalogDTOs = productCatalogService.findAllBySort(productCatalogQuery);
        return new Response<>(productCatalogDTOs);
    }


    /**
     * 修改酒品细分
     */
    @RequestMapping(value = "/v1/product/catalog/modify", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> modify(@RequestBody ProductCatalog productCatalog) {
        // 校验必填项
        ProductCatalogValidator validator = new ProductCatalogValidator();
        if (!validator.onId(productCatalog.getId()).onName(productCatalog.getName()).onShopId(productCatalog.getShopId()).onSort(productCatalog.getSort()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        // 查询是否有相同的细分
        ProductCatalogQuery productCatalogQuery = new ProductCatalogQuery();
        productCatalogQuery.setShopId(productCatalog.getShopId());
        productCatalogQuery.setName(productCatalog.getName());
        productCatalogQuery.setStatus(DataStatus.Y.getCode());
        List<ProductCatalogDTO> productCatalogDTOs = productCatalogService.findAll(productCatalogQuery);
        if (productCatalogDTOs != null && !productCatalogDTOs.isEmpty() && !productCatalogDTOs.get(0).getId().equals(productCatalog.getId())) {
            return new Response<>(ERROR, "该店铺下已存在该酒品细分名称");
        }
        Date datetime = this.getServerTime();
        productCatalog.setModifyTime(datetime);
        productCatalogService.modifyById(productCatalog);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除酒品小料
     */
    @RequestMapping(value = "/v1/product/catalog/remove", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> remove(@RequestBody ProductCatalogQuery request) {
        Date datetime = this.getServerTime();
        ProductCatalogValidator validator = new ProductCatalogValidator();
        if (!validator.onId(request.getId()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        ProductCatalogDTO productCatalogDTO = productCatalogService.findById(request.getId());
        if (productCatalogDTO == null) {
            return new Response<>(ERROR, "未查询到该酒品细分,无法删除");
        }
        ProductCatalog productCatalog = new ProductCatalog();
        productCatalog.setId(request.getId());
        productCatalog.setName(productCatalogDTO.getName() + productCatalogDTO.getId());
        productCatalog.setStatus(DataStatus.N.getCode());
        productCatalog.setModifyTime(datetime);
        productCatalogService.modifyById(productCatalog);
        return new Response<>(OK, SUCCESS);
    }


}
