package com.api.controller;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.validator.TableValidator;
import com.common.bean.Response;
import com.common.bean.WechatUnlimitedQRCodeRequest;
import com.common.client.ALiOSSClient;
import com.common.client.WechatClient;
import com.common.constant.DataStatus;
import com.domain.Table;
import com.dto.TableDTO;
import com.dto.WechatAppDTO;
import com.query.TableQuery;
import com.github.pagehelper.PageInfo;
import com.service.TableService;
import com.service.WechatAppService;

/**
 * 桌子管理接口
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@RestController
public class TableController extends BaseController {

    @Autowired
    private TableService tableService;
    @Autowired
    private WechatAppService wechatAppService;

    /**
     * 增加桌子
     */
    @RequestMapping(value = "/v1/table/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody Table table) {
        TableValidator validator = new TableValidator();
        if (!validator.onShopId(table.getShopId()).onTableName(table.getTableName()).onPeopleNumber(table.getPersonNumber()).onOrderMode(table.getOrderMode()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        TableQuery tableQuery = new TableQuery();
        tableQuery.setTableName(table.getTableName());
        tableQuery.setShopId(table.getShopId());
        tableQuery.setStatus(DataStatus.Y.getCode());
        List<TableDTO> tableDTOS = tableService.findAll(tableQuery);
        if (tableDTOS != null && !tableDTOS.isEmpty()) {
            return new Response<>(ERROR, "该店铺下已存在该桌子名称");
        }
        Date datetime = this.getServerTime();
        table.setStatus(DataStatus.Y.getCode());
        table.setModifyTime(datetime);
        table.setCreateTime(datetime);
        tableService.create(table);
        // 调用微信小程序的二维码接口，生成桌子二维码
        WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
        if (wechatAppDTO == null) {
            return new Response<>(ERROR, "桌子已经添加，暂时无法生成二维码,请联系管理员");
        }
        String unlimitedQRCodeUrl = "/wxa/getwxacodeunlimit?" + "access_token=" +
                wechatAppDTO.getAccessToken();
        WechatClient<String> client = new WechatClient<>();
        client.setMethod(HttpMethod.POST);
        WechatUnlimitedQRCodeRequest wechatUnlimitedQRCodeRequest = new WechatUnlimitedQRCodeRequest();
        wechatUnlimitedQRCodeRequest.setScene(table.getId().toString());
        wechatUnlimitedQRCodeRequest.setWidth("300px");
        ResponseEntity<byte[]> response = client.execute(unlimitedQRCodeUrl, wechatUnlimitedQRCodeRequest);
        if (response != null && response.getHeaders().containsKey("Content-Type") && response.getHeaders().get("Content-Type") != null && !response.getHeaders().get("Content-Type").isEmpty()){
            String contentType = response.getHeaders().get("Content-Type").get(0);
            if (contentType.contains("image/jpeg") && response.getBody() != null){
                // 正常返回了二维码
                ByteArrayInputStream inputStream = new ByteArrayInputStream(response.getBody());
                ALiOSSClient aLiOSSClient = new ALiOSSClient();
                String ossUrl = this.getOssUrl("qr_code.png");
                aLiOSSClient.upload(ossUrl, inputStream);
                Table tableModify = new Table();
                tableModify.setId(table.getId());
                tableModify.setQrCode(ossUrl);
                tableModify.setModifyTime(datetime);
                tableService.modifyById(tableModify);
            }else {
                return new Response<>(ERROR, "桌子已经添加，暂时无法生成二维码,请联系管理员");
            }
        }else {
            return new Response<>(ERROR, "桌子已经添加，暂时无法生成二维码,请联系管理员");
        }
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询桌子列表
     */
    @RequestMapping(value = "/v1/table/query", method = RequestMethod.POST)
    public Response<PageInfo<TableDTO>> query(@RequestBody TableQuery tableQuery) {
        tableQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<TableDTO> pageInfo = tableService.find(tableQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询桌子列表
     */
    @RequestMapping(value = "/v1/table/all/query", method = RequestMethod.POST)
    public Response<List<TableDTO>> queryAll(@RequestBody TableQuery tableQuery) {
        tableQuery.setStatus(DataStatus.Y.getCode());
        List<TableDTO> tableDTOs = tableService.findAll(tableQuery);
        return new Response<>(tableDTOs);
    }


    /**
     * 修改桌子
     */
    @RequestMapping(value = "/v1/table/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Table table) {
        TableValidator validator = new TableValidator();
        if (!validator.onId(table.getId()).onShopId(table.getShopId()).onTableName(table.getTableName()).onPeopleNumber(table.getPersonNumber()).onOrderMode(table.getOrderMode()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        // 查询该店铺下是否存在同名的桌子
        TableQuery tableQuery = new TableQuery();
        tableQuery.setTableName(table.getTableName());
        tableQuery.setShopId(table.getShopId());
        tableQuery.setStatus(DataStatus.Y.getCode());
        List<TableDTO> tableDTOS = tableService.findAll(tableQuery);
        if (tableDTOS != null && !tableDTOS.isEmpty() && !tableDTOS.get(0).getId().equals(table.getId())) {
            return new Response<>(ERROR, "该店铺下已存在该桌子名称");
        }
        Date datetime = this.getServerTime();
        table.setModifyTime(datetime);
        tableService.modifyById(table);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除桌子
     */
    @RequestMapping(value = "/v1/table/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody TableQuery request) {
        Date datetime = this.getServerTime();
        Table table = new Table();
        table.setId(request.getId());
        table.setStatus(DataStatus.N.getCode());
        table.setModifyTime(datetime);
        tableService.modifyById(table);
        return new Response<>(OK, SUCCESS);
    }


}
