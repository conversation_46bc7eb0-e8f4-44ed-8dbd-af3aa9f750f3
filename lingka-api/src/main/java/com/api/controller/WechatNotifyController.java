package com.api.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.WeChatMsgSecCheckAsyncPayload;
import com.common.bean.WechatNotifyRequest;
import com.common.constant.AuditSuggest;
import com.common.constant.ShopConfigModifyAudit;
import com.common.util.aes.AesException;
import com.common.util.aes.WXBizJsonMsgCrypt;
import com.domain.Audit;
import com.domain.ShopConfigModifyApply;
import com.dto.AuditDTO;
import com.dto.ShopConfigModifyApplyDTO;
import com.dto.WechatAppDTO;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.query.AuditQuery;
import com.service.AuditService;
import com.service.ShopConfigModifyApplyService;
import com.service.UserService;
import com.service.WechatAppService;

@RestController
public class WechatNotifyController extends BaseController {

    @Autowired
    private WechatAppService wechatAppService;
    @Autowired
    private UserService userService;
    @Autowired
    private AuditService auditService;
    @Autowired
    private ShopConfigModifyApplyService shopConfigModifyApplyService;

    private final String url = "/v1/wechat/notify";
    final String sToken = "aF45fG";
    final String sEncodingAESKey = "vSwD6H0mGgpdFBRJ6Kavy9Zwi3KgT5r2MVyeAebKxH3";

    WXBizJsonMsgCrypt wxCpt;

    private WXBizJsonMsgCrypt getWxCpt() {
        if (wxCpt == null) {
            try {
                WechatAppDTO wechatAppDTO = wechatAppService.findById(10000000L);
                wxCpt = new WXBizJsonMsgCrypt(sToken, sEncodingAESKey, wechatAppDTO.getAppid());
            } catch (AesException e) {
                logger.error("获取微信消息加密解密失败", e);
            }
        }
        return wxCpt;
    }

    @GetMapping(value = url)
    public String subscribeEvent(@ModelAttribute WechatNotifyRequest notifyRequest) {
        // 验证微信服务器的请求
        String sEchoStr = "error";
        try {
            boolean pass = getWxCpt().verifySignature(notifyRequest.getSignature(), notifyRequest.getTimestamp(), notifyRequest.getNonce());
            if (pass) {
                sEchoStr = notifyRequest.getEchostr();
            }
        } catch (AesException e) {
            logger.error("微信消息验证失败", e);
            return "error";
        }
        return sEchoStr;
    }

    /**
     * 微信消息推送接收器
     */
    @PostMapping(value = url)
    public String subscribeEvent(@RequestParam("timestamp") String sReqTimeStamp, @RequestParam("nonce") String sReqNonce, @RequestParam("msg_signature") String sReqMsgSig, @RequestBody String sReqData) {
        try {
            String sMsg = getWxCpt().decryptMsg(sReqMsgSig, sReqTimeStamp, sReqNonce, sReqData);
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
            JsonNode jsonNode = objectMapper.readValue(sMsg, JsonNode.class);
            if (jsonNode.has("MsgType") && "event".equals(jsonNode.get("MsgType").asText()) && jsonNode.has("Event")) {
                String event = jsonNode.get("Event").asText();
                switch (event) {
                    case "wxa_media_check":
                        WeChatMsgSecCheckAsyncPayload payload = objectMapper.readValue(sMsg, WeChatMsgSecCheckAsyncPayload.class);
                        if (payload.getErrCode() == 0) {
                            String traceId = payload.getTraceId();
                            AuditQuery auditQuery = new AuditQuery();
                            auditQuery.setTraceId(traceId);
                            List<AuditDTO> auditDTOs = auditService.findAll(auditQuery);
                            if (auditDTOs != null && !auditDTOs.isEmpty()) {
                                for (AuditDTO auditDTO : auditDTOs) {
                                    auditDTO.setSuggest(payload.getResult().getSuggest());
                                    auditDTO.setLabel(String.valueOf(payload.getResult().getLabel()));
                                    Audit audit = new Audit(auditDTO);
                                    audit.setModifyTime(getServerTime());
                                    auditService.modifyById(audit);
                                    ShopConfigModifyApplyDTO applyOld = shopConfigModifyApplyService.findById(auditDTO.getModifyId());
                                    if (AuditSuggest.PASS.getCode().equals(payload.getResult().getSuggest()) && ShopConfigModifyAudit.PENDING.getCode().equals(applyOld.getAudit())) { // API 判断通过，自动通过审核
                                        ShopConfigModifyApply apply = new ShopConfigModifyApply(applyOld);
                                        apply.setAudit(ShopConfigModifyAudit.APPROVED.getCode());
                                        apply.setModifyTime(getServerTime());
                                        shopConfigModifyApplyService.modifyById(apply);
                                    }
                                }
                            } else {
                                logger.warn("未找到对应的审核记录，traceId: {}", traceId);
                            }
                        }
                        break;
                    case "subscribe":
                    default:
                        break;
                }
            }
        } catch (AesException e) {
            logger.error("微信消息解密失败", e);
            return "error";
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "error";
        }
        return "";
    }
}
