package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ProductIngredientConfig;
import com.dto.ProductIngredientConfigDTO;
import com.query.ProductIngredientConfigQuery;
import com.github.pagehelper.PageInfo;
import com.service.ProductIngredientConfigService;

/**
 * 产品小料配置控制器
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
public class ProductIngredientConfigController extends BaseController {

    @Autowired
    private ProductIngredientConfigService productIngredientConfigService;

    /**
     * 增加产品小料配置
     */
    @RequestMapping(value = "/v1/product/ingredient/config/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody ProductIngredientConfig productIngredientConfig) {
        Date datetime = this.getServerTime();
        productIngredientConfig.setStatus(DataStatus.Y.getCode());
        productIngredientConfig.setModifyTime(datetime);
        productIngredientConfig.setCreateTime(datetime);
        productIngredientConfigService.create(productIngredientConfig);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询产品小料配置列表
     */
    @RequestMapping(value = "/v1/product/ingredient/config/query", method = RequestMethod.POST)
    public Response<PageInfo<ProductIngredientConfigDTO>> query(@RequestBody ProductIngredientConfigQuery productIngredientConfigQuery) {
        productIngredientConfigQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProductIngredientConfigDTO> pageInfo = productIngredientConfigService.find(productIngredientConfigQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询产品小料配置列表
     */
    @RequestMapping(value = "/v1/product/ingredient/config/all/query", method = RequestMethod.POST)
    public Response<List<ProductIngredientConfigDTO>> queryAll(@RequestBody ProductIngredientConfigQuery productIngredientConfigQuery) {
        productIngredientConfigQuery.setStatus(DataStatus.Y.getCode());
        List<ProductIngredientConfigDTO> productIngredientConfigDTOs = productIngredientConfigService.findAll(productIngredientConfigQuery);
        return new Response<>(productIngredientConfigDTOs);
    }


    /**
     * 修改产品小料配置
     */
    @RequestMapping(value = "/v1/product/ingredient/config/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody ProductIngredientConfig productIngredientConfig) {
        Date datetime = this.getServerTime();
        productIngredientConfig.setModifyTime(datetime);
        productIngredientConfigService.modifyById(productIngredientConfig);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除产品小料配置
     */
    @RequestMapping(value = "/v1/product/ingredient/config/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ProductIngredientConfigQuery request) {
        Date datetime = this.getServerTime();
        ProductIngredientConfig productIngredientConfig = new ProductIngredientConfig();
        productIngredientConfig.setId(request.getId());
        productIngredientConfig.setStatus(DataStatus.N.getCode());
        productIngredientConfig.setModifyTime(datetime);
        productIngredientConfigService.modifyById(productIngredientConfig);
        return new Response<>(OK, SUCCESS);
    }


}
