package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.UserShopValidator;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.domain.UserRole;
import com.domain.UserShop;
import com.dto.RoleDTO;
import com.dto.UserDTO;
import com.dto.UserRoleDTO;
import com.dto.UserShopDTO;
import com.query.UserRoleQuery;
import com.query.UserShopQuery;
import com.github.pagehelper.PageInfo;
import com.service.RoleService;
import com.service.UserRoleService;
import com.service.UserService;
import com.service.UserShopService;

/**
 * 店铺员工管理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@RestController
public class UserShopController extends BaseController {

    @Autowired
    private UserShopService userShopService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 增加店铺员工
     */
    @RequestMapping(value = "/v1/user/shop/create", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> create(@RequestBody UserShopQuery request) {
        UserShopValidator validator = new UserShopValidator();
        if (!validator.onShopId(request.getShopId()).onUserId(request.getUserId()).onRoleIds(request.getRoleIds()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        UserDTO userDTO = userService.findById(request.getUserId());
        if (userDTO == null || DataStatus.N.getCode().equals(userDTO.getStatus())) {
            return new Response<>(ERROR, "用户不存在");
        }
        // 查询是否已经添加过
        UserShopQuery userShopQuery = new UserShopQuery();
        userShopQuery.setShopId(request.getShopId());
        userShopQuery.setUserId(request.getUserId());
        List<UserShopDTO> userShopDTOs = userShopService.findAll(userShopQuery);
        if (userShopDTOs != null && !userShopDTOs.isEmpty()) {
            return new Response<>(ERROR, "该用户已经添加过该店铺员工");
        }
        Date datetime = this.getServerTime();
        UserShop userShop = new UserShop();
        userShop.setShopId(request.getShopId());
        userShop.setUserId(request.getUserId());
        userShop.setStatus(DataStatus.Y.getCode());
        userShop.setModifyTime(datetime);
        userShop.setCreateTime(datetime);
        List<UserRole> userRoles = new ArrayList<>();
        for (Long roleId : request.getRoleIds()) {
            UserRole userRole = new UserRole();
            userRole.setShopId(request.getShopId());
            userRole.setUserId(request.getUserId());
            userRole.setRoleId(roleId);
            userRole.setStatus(DataStatus.Y.getCode());
            userRole.setModifyTime(datetime);
            userRole.setCreateTime(datetime);
            userRoles.add(userRole);
        }
        userShopService.createUserShopAndUserRoles(userShop, userRoles);
        // 直接将该员工的token删除，让其重新登录
        this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + request.getUserId());
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询增加店铺员工列表
     */
    @RequestMapping(value = "/v1/user/shop/query", method = RequestMethod.POST)
    public Response<PageInfo<UserShopDTO>> query(@RequestBody UserShopQuery userShopQuery) {
        userShopQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserShopDTO> pageInfo = userShopService.find(userShopQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询店铺员工列表
     */
    @RequestMapping(value = "/v1/user/shop/all/query", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<List<UserShopDTO>> queryAll(@RequestBody UserShopQuery userShopQuery) {
        List<Long> userIds = new ArrayList<>();
        if (!isEmpty(userShopQuery.getRoleIds()) && !userShopQuery.getRoleIds().isEmpty()) {
            UserRoleQuery userRoleQuery = new UserRoleQuery();
            userRoleQuery.setRoleIds(userShopQuery.getRoleIds());
            userRoleQuery.setShopId(userShopQuery.getShopId());
            userRoleQuery.setStatus(DataStatus.Y.getCode());
            List<UserRoleDTO> userRoleDTOS = userRoleService.findAll(userRoleQuery);
            for (UserRoleDTO userRoleDTO : userRoleDTOS) {
                userIds.add(userRoleDTO.getUserId());
            }
            if (userIds.isEmpty()) {
                return new Response<>(new ArrayList<>());
            }
            userShopQuery.setUserIds(userIds);
        }
        if (!isEmpty(userShopQuery.getRoleId())) {
            UserRoleQuery userRoleQuery = new UserRoleQuery();
            userRoleQuery.setRoleId(userShopQuery.getRoleId());
            userRoleQuery.setShopId(userShopQuery.getShopId());
            userRoleQuery.setStatus(DataStatus.Y.getCode());
            List<UserRoleDTO> userRoleDTOS = userRoleService.findAll(userRoleQuery);
            for (UserRoleDTO userRoleDTO : userRoleDTOS) {
                userIds.add(userRoleDTO.getUserId());
            }
            if (userIds.isEmpty()) {
                return new Response<>(new ArrayList<>());
            }
            userShopQuery.setUserIds(userIds);
        }
        userShopQuery.setStatus(DataStatus.Y.getCode());
        List<UserShopDTO> userShopDTOs = userShopService.findAll(userShopQuery);
        if (userShopDTOs == null || userShopDTOs.isEmpty()) {
            return new Response<>(new ArrayList<>());
        }
        for (UserShopDTO userShopDTO : userShopDTOs) {
            userIds.add(userShopDTO.getUserId());
        }
        Map<Long, UserDTO> userDTOMap = userService.findMapByIds(userIds);
        UserRoleQuery userRoleQuery = new UserRoleQuery();
        userRoleQuery.setUserIds(userIds);
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOS = userRoleService.findAll(userRoleQuery);
        Map<String, List<Long>> userIdShopIdRoleIdsMap = new HashMap<>();
        List<Long> roleIds = new ArrayList<>();
        for (UserRoleDTO userRoleDTO : userRoleDTOS) {
            roleIds.add(userRoleDTO.getRoleId());
            String key = userRoleDTO.getUserId() + "_" + userRoleDTO.getShopId();
            if (!userIdShopIdRoleIdsMap.containsKey(key)) {
                userIdShopIdRoleIdsMap.put(key, new ArrayList<>());
            }
            userIdShopIdRoleIdsMap.get(key).add(userRoleDTO.getRoleId());
        }
        Map<Long, RoleDTO> roleDTOMap = roleService.findMapByIds(roleIds);
        for (UserShopDTO userShopDTO : userShopDTOs) {
            if (userDTOMap.containsKey(userShopDTO.getUserId())) {
                UserDTO userDTO = userDTOMap.get(userShopDTO.getUserId());
                userShopDTO.setUserNickname(userDTO.getNickname());
                userShopDTO.setUserMobile(userDTO.getMobile());
                userShopDTO.setUserAvatar(userDTO.getAvatar());
                userShopDTO.setUserMobile(userDTO.getMobile());
                userShopDTO.setUserIntroduce(userDTO.getIntroduce());
            }
            String key = userShopDTO.getUserId() + "_" + userShopDTO.getShopId();
            if (userIdShopIdRoleIdsMap.containsKey(key)) {
                List<String> userRoleNames = new ArrayList<>();
                List<Long> roleIdList = userIdShopIdRoleIdsMap.get(key);
                userShopDTO.setUserRoleIds(roleIdList);
                for (Long roleId : roleIdList) {
                    if (roleDTOMap.containsKey(roleId)) {
                        RoleDTO roleDTO = roleDTOMap.get(roleId);
                        userRoleNames.add(roleDTO.getName());
                    }
                }
                userShopDTO.setUserRoleNames(userRoleNames);
            }
        }
        return new Response<>(userShopDTOs);
    }


    /**
     * 修改店铺员工
     */
    @RequestMapping(value = "/v1/user/shop/modify", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<?> modify(@RequestBody UserShopQuery request) {
        UserShopValidator validator = new UserShopValidator();
        if (!validator.onId(request.getId()).onRoleIds(request.getRoleIds()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        UserShopDTO userShopDTO = userShopService.findById(request.getId());
        if (userShopDTO == null) {
            return new Response<>(ERROR, "未查询到该员工,无法修改");
        }
        if (userShopDTO.getUserId() == null || userShopDTO.getShopId() == null) {
            return new Response<>(ERROR, "未查询到该员工,无法修改");
        }
        int count = userRoleService.removeByUserIdAndShopIdAndCreate(userShopDTO.getUserId(), userShopDTO.getShopId(), request.getRoleIds());
        if (count > 0) {
            return new Response<>(OK, SUCCESS);

        }
        return new Response<>(ERROR, "修改失败");
    }

    /**
     * 删除店铺员工
     */
    @RequestMapping(value = "/v1/user/shop/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody UserShopQuery request) {
        Date datetime = this.getServerTime();
        UserShopDTO userShopDTO = userShopService.findById(request.getId());
        if (userShopDTO == null) {
            return new Response<>(ERROR, "未查询到该员工,无法删除");
        }
        UserRoleQuery userRoleQuery = new UserRoleQuery();
        userRoleQuery.setUserId(userShopDTO.getUserId());
        userRoleQuery.setShopId(userShopDTO.getShopId());
        List<UserRoleDTO> userRoleDTOS = userRoleService.findAll(userRoleQuery);
        List<Long> userRoleIds = new ArrayList<>();
        for (UserRoleDTO userRoleDTO : userRoleDTOS) {
            userRoleIds.add(userRoleDTO.getId());
        }
        UserShop userShop = new UserShop();
        userShop.setId(request.getId());
        userShop.setStatus(DataStatus.N.getCode());
        userShop.setModifyTime(datetime);
        userShopService.removeById(userShop.getId(), userRoleIds);
        //  直接将该员工的token删除，让其重新登录
        this.redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + userShopDTO.getUserId());
        return new Response<>(OK, SUCCESS);
    }


}
