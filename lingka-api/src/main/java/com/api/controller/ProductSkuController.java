package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ProductSku;
import com.dto.ProductSkuDTO;
import com.query.ProductSkuQuery;
import com.github.pagehelper.PageInfo;
import com.service.ProductSkuService;

/**
 * 产品SKU控制器
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
public class ProductSkuController extends BaseController {

    @Autowired
    private ProductSkuService productSkuService;

    /**
     * 增加产品SKU
     */
    @RequestMapping(value = "/v1/product/sku/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody ProductSku productSku) {
        Date datetime = this.getServerTime();
        productSku.setStatus(DataStatus.Y.getCode());
        productSku.setModifyTime(datetime);
        productSku.setCreateTime(datetime);
        productSkuService.create(productSku);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询产品SKU列表
     */
    @RequestMapping(value = "/v1/product/sku/query", method = RequestMethod.POST)
    public Response<PageInfo<ProductSkuDTO>> query(@RequestBody ProductSkuQuery productSkuQuery) {
        productSkuQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProductSkuDTO> pageInfo = productSkuService.find(productSkuQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询产品SKU列表
     */
    @RequestMapping(value = "/v1/product/sku/all/query", method = RequestMethod.POST)
    public Response<List<ProductSkuDTO>> queryAll(@RequestBody ProductSkuQuery productSkuQuery) {
        productSkuQuery.setStatus(DataStatus.Y.getCode());
        List<ProductSkuDTO> productSkuDTOs = productSkuService.findAll(productSkuQuery);
        return new Response<>(productSkuDTOs);
    }


    /**
     * 修改产品SKU
     */
    @RequestMapping(value = "/v1/product/sku/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody ProductSku productSku) {
        Date datetime = this.getServerTime();
        productSku.setModifyTime(datetime);
        productSkuService.modifyById(productSku);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除产品SKU
     */
    @RequestMapping(value = "/v1/product/sku/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ProductSkuQuery request) {
        Date datetime = this.getServerTime();
        ProductSku productSku = new ProductSku();
        productSku.setId(request.getId());
        productSku.setStatus(DataStatus.N.getCode());
        productSku.setModifyTime(datetime);
        productSkuService.modifyById(productSku);
        return new Response<>(OK, SUCCESS);
    }


}
