package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.validator.TicketValidator;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.ProductStage;
import com.domain.Ticket;
import com.domain.TicketProduct;
import com.dto.ProductDTO;
import com.dto.TicketDTO;
import com.dto.TicketProductDTO;
import com.query.TicketProductQuery;
import com.query.TicketQuery;
import com.github.pagehelper.PageInfo;
import com.service.ProductService;
import com.service.TicketService;
import com.service.TicketProductService;

/**
 * 门票管理
 *
 * <AUTHOR>
 * @date 2025-09-11
 */
@RestController
public class TicketController extends BaseController {

    @Autowired
    private TicketService ticketService;
    @Autowired
    private TicketProductService ticketProductService;
    @Autowired
    private ProductService productService;

    /**
     * 增加门票
     */
    @RequestMapping(value = "/v1/ticket/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody TicketQuery request) {
        TicketValidator validator = new TicketValidator();
        if (!validator.onShopId(request.getShopId()).onName(request.getName()).onPriceCatalog(request.getPriceCatalog())
                .onPrice(request.getPrice()).onProductNumber(request.getProductNumber()).onStage(request.getStage()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Ticket ticket = new Ticket();
        Date datetime = this.getServerTime();
        BeanUtils.copyProperties(request, ticket);
        List<TicketProduct> ticketProducts = new ArrayList<>();
        if (request.getTicketProducts() != null && !request.getTicketProducts().isEmpty()) {
            // 记录列表中的产品ID，防止重复添加
            List<Long> existingProductIds = new ArrayList<>();
            for (TicketProductQuery ticketProductQuery : request.getTicketProducts()) {
                if (existingProductIds.contains(ticketProductQuery.getProductId())) {
                    return new Response<>(ERROR, "添加门票失败,请将重复的酒水合并为1列");
                }
                existingProductIds.add(ticketProductQuery.getProductId());
                if (!validator.onProductId(ticketProductQuery.getProductId()).onProductNumber(ticketProductQuery.getProductNumber()).result()) {
                    return new Response<>(ERROR, validator.getErrorMessage());
                }
                TicketProduct ticketProductCreate = new TicketProduct();
                ticketProductCreate.setProductId(ticketProductQuery.getProductId());
                if (ticketProductQuery.getProductNumber() > request.getProductNumber()) {
                    return new Response<>(ERROR, "具体酒水配置的数量不能超过门票配置的数量");
                }
                ticketProductCreate.setProductNumber(ticketProductQuery.getProductNumber());
                ticketProductCreate.setStatus(DataStatus.Y.getCode());
                ticketProductCreate.setModifyTime(datetime);
                ticketProductCreate.setCreateTime(datetime);
                ticketProducts.add(ticketProductCreate);
            }
            // 查询这些产品是否被下架
            List<ProductDTO> productDTOS = productService.findByIds(existingProductIds);
            for (ProductDTO productDTO : productDTOS) {
                if (!ProductStage.UP.getCode().equals(productDTO.getStage())) {
                    return new Response<>(ERROR, "添加门票失败,门票里面包含的酒水【" + productDTO.getName() + "】已下架");
                }
            }
        }
        ticket.setStatus(DataStatus.Y.getCode());
        ticket.setModifyTime(datetime);
        ticket.setCreateTime(datetime);
        ticketService.create(ticket, ticketProducts);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询门票列表
     */
    @RequestMapping(value = "/v1/ticket/query", method = RequestMethod.POST)
    public Response<PageInfo<TicketDTO>> query(@RequestBody TicketQuery ticketQuery) {
        ticketQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<TicketDTO> pageInfo = ticketService.find(ticketQuery);
        if (pageInfo.getList() != null && !pageInfo.getList().isEmpty()) {
            List<Long> ticketIds = new ArrayList<>();
            for (TicketDTO ticketDTO : pageInfo.getList()) {
                ticketIds.add(ticketDTO.getId());
            }
            // 查询门票产品
            TicketProductQuery ticketProductQuery = new TicketProductQuery();
            ticketProductQuery.setTicketIds(ticketIds);
            ticketProductQuery.setStatus(DataStatus.Y.getCode());
            List<TicketProductDTO> ticketProductDTOs = ticketProductService.findAll(ticketProductQuery);
            // 查询产品信息
            List<Long> productIds = new ArrayList<>();

            // 构建门票产品映射
            Map<Long, List<TicketProductDTO>> ticketProductMap = new HashMap<>();
            for (TicketProductDTO ticketProductDTO : ticketProductDTOs) {
                productIds.add(ticketProductDTO.getProductId());
                if (!ticketProductMap.containsKey(ticketProductDTO.getTicketId())) {
                    ticketProductMap.put(ticketProductDTO.getTicketId(), new ArrayList<>());
                }
                ticketProductMap.get(ticketProductDTO.getTicketId()).add(ticketProductDTO);
            }
            Map<Long, ProductDTO> productDTOMap = productService.findMapByIds(productIds);
            // 循环处理数据
            for (TicketDTO ticketDTO : pageInfo.getList()) {
                if (ticketProductMap.containsKey(ticketDTO.getId())) {
                    List<TicketProductDTO> ticketProductDTOS = ticketProductMap.get(ticketDTO.getId());
                    for (TicketProductDTO ticketProductDTO : ticketProductDTOS) {
                        if (productDTOMap.containsKey(ticketProductDTO.getProductId())) {
                            ticketProductDTO.setProductName(productDTOMap.get(ticketProductDTO.getProductId()).getName());
                        }
                    }
                    ticketDTO.setTicketProducts(ticketProductDTOS);
                }
            }
        }
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询门票列表
     */
    @RequestMapping(value = "/v1/ticket/all/query", method = RequestMethod.POST)
    public Response<List<TicketDTO>> queryAll(@RequestBody TicketQuery ticketQuery) {
        ticketQuery.setStatus(DataStatus.Y.getCode());
        List<TicketDTO> ticketDTOs = ticketService.findAll(ticketQuery);
        // 查询门票产品信息
        if (ticketDTOs != null && !ticketDTOs.isEmpty()) {
            List<Long> ticketIds = new ArrayList<>();
            for (TicketDTO ticketDTO : ticketDTOs) {
                ticketIds.add(ticketDTO.getId());
            }
            // 查询门票产品
            TicketProductQuery ticketProductQuery = new TicketProductQuery();
            ticketProductQuery.setTicketIds(ticketIds);
            ticketProductQuery.setStatus(DataStatus.Y.getCode());
            List<TicketProductDTO> ticketProductDTOs = ticketProductService.findAll(ticketProductQuery);
            // 查询产品信息
            List<Long> productIds = new ArrayList<>();
            // 构建门票产品映射
            Map<Long, List<TicketProductDTO>> ticketProductMap = new HashMap<>();
            for (TicketProductDTO ticketProductDTO : ticketProductDTOs) {
                productIds.add(ticketProductDTO.getProductId());
                if (!ticketProductMap.containsKey(ticketProductDTO.getTicketId())) {
                    ticketProductMap.put(ticketProductDTO.getTicketId(), new ArrayList<>());
                }
                ticketProductMap.get(ticketProductDTO.getTicketId()).add(ticketProductDTO);
            }
            Map<Long, ProductDTO> productDTOMap = productService.findMapByIds(productIds);
            // 循环处理数据
            for (TicketDTO ticketDTO : ticketDTOs) {
                if (ticketProductMap.containsKey(ticketDTO.getId())) {
                    List<TicketProductDTO> ticketProductDTOS = ticketProductMap.get(ticketDTO.getId());
                    for (TicketProductDTO ticketProductDTO : ticketProductDTOS) {
                        if (productDTOMap.containsKey(ticketProductDTO.getProductId())) {
                            ticketProductDTO.setProductName(productDTOMap.get(ticketProductDTO.getProductId()).getName());
                        }
                    }
                    ticketDTO.setTicketProducts(ticketProductDTOS);
                }
            }
        }
        return new Response<>(ticketDTOs);
    }


    /**
     * 修改门票
     */
    @RequestMapping(value = "/v1/ticket/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody TicketQuery request) {
        TicketValidator validator = new TicketValidator();
        if (!validator.onId(request.getId()).onShopId(request.getShopId()).onName(request.getName()).onPriceCatalog(request.getPriceCatalog())
                .onPrice(request.getPrice()).onProductNumber(request.getProductNumber()).onStage(request.getStage()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        TicketDTO ticketDTO = ticketService.findById(request.getId());
        if (ticketDTO == null) {
            return new Response<>(ERROR, "门票不存在");
        }
        Date datetime = this.getServerTime();
        Ticket ticket = new Ticket();
        BeanUtils.copyProperties(request, ticket);
        ticket.setModifyTime(datetime);

        // 构建门票产品对象
        List<TicketProduct> ticketProducts = new ArrayList<>();
        if (request.getTicketProducts() != null && !request.getTicketProducts().isEmpty()) {
            // 记录列表中的产品ID，防止重复添加
            List<Long> existingProductIds = new ArrayList<>();
            for (TicketProductQuery ticketProductQuery : request.getTicketProducts()) {
                if (existingProductIds.contains(ticketProductQuery.getProductId())) {
                    return new Response<>(ERROR, "添加门票失败,请将重复的酒水合并为1列");
                }
                existingProductIds.add(ticketProductQuery.getProductId());
                if (!validator.onProductId(ticketProductQuery.getProductId()).onProductNumber(ticketProductQuery.getProductNumber()).result()) {
                    return new Response<>(ERROR, validator.getErrorMessage());
                }
                if (ticketProductQuery.getProductNumber() > request.getProductNumber()) {
                    return new Response<>(ERROR, "具体酒水配置的数量不能超过门票配置的数量");
                }
                TicketProduct ticketProduct = new TicketProduct();
                ticketProduct.setId(ticketProductQuery.getId());
                ticketProduct.setProductId(ticketProductQuery.getProductId());
                ticketProduct.setProductNumber(ticketProductQuery.getProductNumber());
                ticketProduct.setStatus(DataStatus.Y.getCode());
                ticketProduct.setModifyTime(datetime);
                if (ticketProductQuery.getId() == null) {
                    ticketProduct.setCreateTime(datetime);
                }
                ticketProducts.add(ticketProduct);
            }
            // 查询这些产品是否被下架
            List<ProductDTO> productDTOS = productService.findByIds(existingProductIds);
            for (ProductDTO productDTO : productDTOS) {
                if (!ProductStage.UP.getCode().equals(productDTO.getStage())) {
                    return new Response<>(ERROR, "修改门票失败,门票里面包含的酒水【" + productDTO.getName() + "】已下架");
                }
            }
        }
        ticketService.modifyAndTicketProducts(ticket, ticketProducts);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除门票
     */
    @RequestMapping(value = "/v1/ticket/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody TicketQuery request) {
        Date datetime = this.getServerTime();
        Ticket ticket = new Ticket();
        ticket.setId(request.getId());
        ticket.setStatus(DataStatus.N.getCode());
        ticket.setModifyTime(datetime);
        ticketService.modifyById(ticket);
        return new Response<>(OK, SUCCESS);
    }


}
