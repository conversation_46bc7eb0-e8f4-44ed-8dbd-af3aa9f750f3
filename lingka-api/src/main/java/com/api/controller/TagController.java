package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.Tag;
import com.dto.TagDTO;
import com.query.TagQuery;
import com.github.pagehelper.PageInfo;
import com.service.TagService;

/**
 * 标签控制器
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@RestController
public class TagController extends BaseController {

    @Autowired
    private TagService tagService;

    /**
     * 增加标签
     */
    @RequestMapping(value = "/v1/tag/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody Tag tag) {
        Date datetime = this.getServerTime();
        tag.setStatus(DataStatus.Y.getCode());
        tag.setModifyTime(datetime);
        tag.setCreateTime(datetime);
        tagService.create(tag);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询标签列表
     */
    @RequestMapping(value = "/v1/tag/query", method = RequestMethod.POST)
    public Response<PageInfo<TagDTO>> query(@RequestBody TagQuery tagQuery) {
        tagQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<TagDTO> pageInfo = tagService.find(tagQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询标签列表
     */
    @RequestMapping(value = "/v1/tag/all/query", method = RequestMethod.POST)
    public Response<List<TagDTO>> queryAll(@RequestBody TagQuery tagQuery) {
        tagQuery.setStatus(DataStatus.Y.getCode());
        List<TagDTO> tagDTOs = tagService.findAll(tagQuery);
        return new Response<>(tagDTOs);
    }


    /**
     * 修改标签
     */
    @RequestMapping(value = "/v1/tag/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Tag tag) {
        Date datetime = this.getServerTime();
        tag.setModifyTime(datetime);
        tagService.modifyById(tag);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除标签
     */
    @RequestMapping(value = "/v1/tag/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody TagQuery request) {
        Date datetime = this.getServerTime();
        Tag tag = new Tag();
        tag.setId(request.getId());
        tag.setStatus(DataStatus.N.getCode());
        tag.setModifyTime(datetime);
        tagService.modifyById(tag);
        return new Response<>(OK, SUCCESS);
    }


}
