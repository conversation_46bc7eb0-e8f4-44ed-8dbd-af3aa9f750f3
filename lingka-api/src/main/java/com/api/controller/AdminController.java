package com.api.controller;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.constant.App;
import com.api.validator.AdminValidator;
import com.api.validator.LoginValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.Device;
import com.common.constant.Gender;
import com.common.constant.Platform;
import com.common.constant.PublicFirstLogin;
import com.common.constant.TokenType;
import com.common.util.DateUtil;
import com.common.util.EncryptUtil;
import com.common.util.MD5Util;
import com.domain.Admin;
import com.dto.AdminDTO;
import com.query.AdminQuery;
import com.github.pagehelper.PageInfo;
import com.service.AdminService;

/**
 * 管理员模块接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@RestController
public class AdminController extends BaseController {

    @Autowired
    private AdminService adminService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 增加管理员
     */
    @RequestMapping(value = "/v1/admin/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.S})
    public Response<?> create(@RequestBody Admin admin) {
        AdminValidator validator = new AdminValidator();
        if (!validator.onUsername(admin.getUsername()).onMobile(admin.getMobile()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        String username = admin.getUsername().trim();
        AdminDTO adminDTO = adminService.findByUsername(username);
        if (adminDTO != null) {
            return new Response<>(ERROR, "用户名已存在");
        }
        String mobile = admin.getMobile().trim();
        AdminDTO adminDTO1 = adminService.findByMobile(mobile);
        if (adminDTO1 != null) {
            return new Response<>(ERROR, "手机号已存在");
        }
        Date datetime = this.getServerTime();
        admin.setUsername(username);
        admin.setMobile(mobile);
        admin.setPassword(App.PASSWORD);
        admin.setFirstLogin(PublicFirstLogin.Y.getCode());
        admin.setStatus(DataStatus.Y.getCode());
        admin.setModifyTime(datetime);
        admin.setCreateTime(datetime);
        adminService.create(admin);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 管理员登录
     */
    @RequestMapping(value = "/v1/admin/login", method = RequestMethod.POST)
    public Response<AdminDTO> login(@RequestBody AdminQuery request) {
        LoginValidator loginValidator = new LoginValidator();
        if (!loginValidator.onUsername(request.getUsername()).onPassword(request.getPassword()).result()) {
            return new Response<>(ERROR, loginValidator.getErrorMessage());
        }
        AdminDTO adminDTO = adminService.findByUsername(request.getUsername());
        if (adminDTO == null) {
            return new Response<>(ERROR, "用户名/密码错误");
        }
        if (!adminDTO.getPassword().equals(MD5Util.toMD5(request.getPassword()))) {
            return new Response<>(ERROR, "用户名/密码错误");
        }
        if (!DataStatus.Y.getCode().equals(adminDTO.getStatus())) {
            return new Response<>(ERROR, "用户名/密码错误");
        }
        adminDTO.setPassword(null);
        adminDTO.setToken(this.getUserToken(adminDTO));
        return new Response<>(adminDTO);
    }


    /**
     * 分页查询管理员列表
     */
    @RequestMapping(value = "/v1/admin/query", method = RequestMethod.POST)
    public Response<PageInfo<AdminDTO>> query(@RequestBody AdminQuery adminQuery) {
        PageInfo<AdminDTO> pageInfo = adminService.find(adminQuery);
        for (AdminDTO adminDTO : pageInfo.getList()) {
            adminDTO.setGenderName(Gender.getName(adminDTO.getGender()));
        }
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询管理员列表
     */
    @RequestMapping(value = "/v1/admin/all/query", method = RequestMethod.POST)
    public Response<List<AdminDTO>> queryAll(@RequestBody AdminQuery adminQuery) {
        adminQuery.setStatus(DataStatus.Y.getCode());
        List<AdminDTO> adminDTOs = adminService.findAll(adminQuery);
        for (AdminDTO adminDTO : adminDTOs) {
            adminDTO.setGenderName(Gender.getName(adminDTO.getGender()));
        }
        return new Response<>(adminDTOs);
    }


    /**
     * 修改管理员
     */
    @RequestMapping(value = "/v1/admin/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Admin admin) {
        AdminValidator validator = new AdminValidator();
        if (!validator.onId(admin.getId()).onUsername(admin.getUsername()).onMobile(admin.getMobile()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        AdminDTO adminDTOById = adminService.findById(admin.getId());
        if (adminDTOById == null || DataStatus.N.getCode().equals(adminDTOById.getStatus())) {
            return new Response<>(ERROR, "管理员不存在,请核对管理员编号是否有误");
        }
        AdminDTO adminDTOByUsername = adminService.findByUsername(admin.getUsername());
        if (adminDTOByUsername != null && !adminDTOByUsername.getId().equals(admin.getId())) {
            return new Response<>(ERROR, "用户名已存在");
        }
        AdminDTO adminDTOByMobile = adminService.findByMobile(admin.getMobile());
        if (adminDTOByMobile != null && !adminDTOByMobile.getId().equals(admin.getId())) {
            return new Response<>(ERROR, "手机号已存在");
        }
        Date datetime = this.getServerTime();
        admin.setModifyTime(datetime);
        admin.setStatus(null);
        adminService.modifyById(admin);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 修改管理员个人信息
     */
    @RequestMapping(value = "/v1/admin/my/modify", method = RequestMethod.POST)
    @Token(tokenType = TokenType.S)
    public Response<?> modifyMy(@RequestBody Admin request) {
        request.setId(this.getUserToken().getId());
        return this.modify(request);
    }


    /**
     * 启用/禁用管理员
     */
    @RequestMapping(value = "/v1/admin/status/modify", method = RequestMethod.POST)
    public Response<?> modifyStatus(@RequestBody AdminQuery request) {
        AdminValidator validator = new AdminValidator();
        if (!validator.onId(request.getId()).onStatus(request.getStatus()).result()) {
            return new Response<>(validator.getErrorMessage());
        }
        if (request.getId() == 10000000L) {
            return new Response<>(ERROR, "此管理员禁止启用/禁用");
        }
        Date datetime = this.getServerTime();
        Admin admin = new Admin();
        admin.setId(request.getId());
        admin.setStatus(request.getStatus());
        admin.setModifyTime(datetime);
        adminService.modifyById(admin);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除管理员
     */
    @RequestMapping(value = "/v1/admin/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody AdminQuery request) {
        AdminValidator validator = new AdminValidator();
        if (!validator.onId(request.getId()).result()) {
            return new Response<>(validator.getErrorMessage());
        }
        if (request.getId() == 10000000L) {
            return new Response<>(ERROR, "此管理员禁止删除");
        }
        Date datetime = this.getServerTime();
        Admin admin = new Admin();
        admin.setId(request.getId());
        admin.setMobile("");
        admin.setUsername("");
        admin.setStatus(DataStatus.N.getCode());
        admin.setModifyTime(datetime);
        adminService.modifyById(admin);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 重置密码
     */
    @RequestMapping(value = "/v1/admin/password/reset", method = RequestMethod.POST)
    public Response<?> resetPassword(@RequestBody AdminQuery request){
        AdminDTO adminDTO = adminService.findById(request.getId());
        if (adminDTO == null || DataStatus.N.getCode().equals(adminDTO.getStatus())){
            return new Response<>(ERROR,"该账户已经被禁用，无法重置密码");
        }
        Admin adminModify = new Admin();
        adminModify.setId(request.getId());
        adminModify.setPassword(App.PASSWORD);
        adminModify.setModifyTime(this.getServerTime());
        adminService.modifyById(adminModify);
        return new Response<>(OK,SUCCESS);
    }

    /**
     * 修改密码
     */
    @RequestMapping(value = "/v1/admin/password/modify", method = RequestMethod.POST)
    public Response<?> modifyPassword(@RequestBody AdminQuery request){
        LoginValidator validator = new LoginValidator();
        if (validator.onPassword(request.getPassword()).onOldPassword(request.getOldPassword()).result()){
            AdminDTO adminDTO = adminService.findById(this.getUserToken().getId());
            if (adminDTO.getId() == 10000000){
                return new Response<>(ERROR,"管理员账户不可修改密码");
            }
            // 判断旧密码是否输入正确
            if (!MD5Util.toMD5(request.getOldPassword()).equals(adminDTO.getPassword())){
                return new Response<>(ERROR,"旧密码输入错误，无法修改密码");
            }
            Date datetime = this.getServerTime();
            Admin adminModify = new Admin();
            adminModify.setId(adminDTO.getId());
            adminModify.setPassword(MD5Util.toMD5(request.getPassword()));
            adminModify.setModifyTime(datetime);
            adminService.modifyById(adminModify);
            return new Response<>(OK,SUCCESS);
        }else {
            return new Response<>(ERROR,validator.getErrorMessage());
        }
    }

    @RequestMapping(value = "/v1/admin/my/view",method = RequestMethod.POST)
    @Token(tokenType = TokenType.S)
    public Response<AdminDTO> view(@RequestBody AdminQuery request){
        Long userId = this.getUserToken().getId();
        AdminDTO adminDTO = adminService.findById(userId);
        return new Response<>(adminDTO);
    }


    /**
     * 获取用户凭证
     */
    private String getUserToken(AdminDTO adminDTO) {
        Date date = this.getServerTime();
        JsonWebToken jsonWebToken = new JsonWebToken();
        jsonWebToken.setId(UUID.randomUUID().toString());
        jsonWebToken.setIssuedAt(date);
        jsonWebToken.setExpiration(DateUtil.add(date, App.TOKEN_EXPIRE_UNIT, App.TOKEN_EXPIRE_NUMBER));
        jsonWebToken.setIssuer(String.valueOf(adminDTO.getId()));
        jsonWebToken.setSubject(adminDTO.getUsername());
        // 用户信息
        jsonWebToken.setUserId(adminDTO.getId());
        jsonWebToken.setType(TokenType.S.name());
        String token = EncryptUtil.createJwt(jsonWebToken);
        Device device = Platform.DEVIVCE.get(this.httpServletRequest.getHeader(App.HTTP_HEADER_APP_PLATFORM)); // 当请求头中没有合法的 X-App-Platform 时会登录失败
        this.redisTemplate.opsForValue().set(CacheKey.USER_TOKEN + TokenType.S.name() + device.name() + adminDTO.getId(), token);
        return token;
    }


}
