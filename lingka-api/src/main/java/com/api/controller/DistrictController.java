package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.util.POIUtil;
import com.common.util.StringUtil;
import com.domain.City;
import com.domain.District;
import com.domain.Province;
import com.dto.CityDTO;
import com.dto.DistrictDTO;
import com.dto.ProvinceDTO;
import com.query.DistrictQuery;
import com.github.pagehelper.PageInfo;
import com.service.CityService;
import com.service.DistrictService;
import com.service.ProvinceService;

/**
 * 区(县)控制器
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
public class DistrictController extends BaseController {

    @Autowired
    private DistrictService districtService;
    @Autowired
    private CityService cityService;
    @Autowired
    private ProvinceService provinceService;

    /**
     * 增加区(县)
     */
    @RequestMapping(value = "/v1/district/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody District district) {
        Date datetime = this.getServerTime();
        district.setStatus(DataStatus.Y.getCode());
        district.setModifyTime(datetime);
        district.setCreateTime(datetime);
        districtService.create(district);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询区(县)列表
     */
    @RequestMapping(value = "/v1/district/query", method = RequestMethod.POST)
    public Response<PageInfo<DistrictDTO>> query(@RequestBody DistrictQuery districtQuery) {
        districtQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<DistrictDTO> pageInfo = districtService.find(districtQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询区(县)列表
     */
    @RequestMapping(value = "/v1/district/all/query", method = RequestMethod.POST)
    public Response<List<DistrictDTO>> queryAll(@RequestBody DistrictQuery districtQuery) {
        districtQuery.setStatus(DataStatus.Y.getCode());
        List<DistrictDTO> districtDTOs = districtService.findAll(districtQuery);
        return new Response<>(districtDTOs);
    }


    /**
     * 修改区(县)
     */
    @RequestMapping(value = "/v1/district/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody District district) {
        Date datetime = this.getServerTime();
        district.setModifyTime(datetime);
        districtService.modifyById(district);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除区(县)
     */
    @RequestMapping(value = "/v1/district/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody DistrictQuery request) {
        Date datetime = this.getServerTime();
        District district = new District();
        district.setId(request.getId());
        district.setStatus(DataStatus.N.getCode());
        district.setModifyTime(datetime);
        districtService.modifyById(district);
        return new Response<>(OK, SUCCESS);
    }

    @RequestMapping("/v1/distinct/import")
    public Response<?> importV1() {
        String[][] rows = POIUtil.parse("/Users/<USER>/develop/file/1.xlsx");
        if (rows == null || rows.length == 0) {
            return new Response<>(ERROR, FAILURE);
        }
        for (int i = 0; i < rows.length; i++) {
            if (i == 0) {
                // 表头,跳过
                continue;
            }
            //如果当前行为空，或者当前行不为空但首列为空字符串且数组长度为0，则跳出当前循环
            if (rows[i] == null || (StringUtil.isEmpty(rows[i][0]) && rows[i].length == 1)) {
                break;
            }
            // 数据
            String provinceName = rows[i][1].trim();
            String provinceId = rows[i][2].trim().substring(3);
            String cityName = rows[i][3].trim();
            String cityId = rows[i][4].trim().substring(3);
            String distinctName = rows[i][5].trim();
            String distinctId = rows[i][6].trim().substring(3);
            Province province = new Province();
            province.setId(Long.valueOf(provinceId));
            province.setName(provinceName);
            province.setStatus(DataStatus.Y.getCode());
            ProvinceDTO provinceDTO = provinceService.findById(Long.valueOf(provinceId));
            if (provinceDTO == null) {
                provinceService.create(province);
            }
            City city = new City();
            city.setId(Long.valueOf(cityId));
            city.setProvinceId(province.getId());
            city.setName(cityName);
            city.setStatus(DataStatus.Y.getCode());
            CityDTO cityDTO = cityService.findById(city.getId());
            if (cityDTO == null) {
                cityService.create(city);
            }
            District district = new District();
            district.setId(Long.valueOf(distinctId));
            district.setName(distinctName);
            district.setCityId(city.getId());
            district.setStatus(DataStatus.Y.getCode());
            DistrictDTO districtDTO = districtService.findById(district.getId());
            if (districtDTO == null) {
                districtService.create(district);
            }
        }
        return new Response<>(OK, SUCCESS);
    }


}
