package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.City;
import com.dto.CityDTO;
import com.query.CityQuery;
import com.github.pagehelper.PageInfo;
import com.service.CityService;

/**
 * 城市控制器
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@RestController
public class CityController extends BaseController {

    @Autowired
    private CityService cityService;

    /**
     * 增加城市
     */
    @RequestMapping(value = "/v1/city/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody City city) {
        Date datetime = this.getServerTime();
        city.setStatus(DataStatus.Y.getCode());
        city.setModifyTime(datetime);
        city.setCreateTime(datetime);
        cityService.create(city);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询城市列表
     */
    @RequestMapping(value = "/v1/city/query", method = RequestMethod.POST)
    public Response<PageInfo<CityDTO>> query(@RequestBody CityQuery cityQuery) {
        cityQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<CityDTO> pageInfo = cityService.find(cityQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询城市列表
     */
    @RequestMapping(value = "/v1/city/all/query", method = RequestMethod.POST)
    public Response<List<CityDTO>> queryAll(@RequestBody CityQuery cityQuery) {
        cityQuery.setStatus(DataStatus.Y.getCode());
        List<CityDTO> cityDTOs = cityService.findAll(cityQuery);
        return new Response<>(cityDTOs);
    }


    /**
     * 修改城市
     */
    @RequestMapping(value = "/v1/city/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody City city) {
        Date datetime = this.getServerTime();
        city.setModifyTime(datetime);
        cityService.modifyById(city);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除城市
     */
    @RequestMapping(value = "/v1/city/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody CityQuery request) {
        Date datetime = this.getServerTime();
        City city = new City();
        city.setId(request.getId());
        city.setStatus(DataStatus.N.getCode());
        city.setModifyTime(datetime);
        cityService.modifyById(city);
        return new Response<>(OK, SUCCESS);
    }


}
