package com.api.controller;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.constant.App;
import com.api.validator.OrderTicketDetailValidator;
import com.api.validator.OrderTicketValidator;
import com.api.validator.OrderValidator;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.OrderPayStage;
import com.common.constant.OrderStage;
import com.common.constant.OrderType;
import com.common.constant.ProductStage;
import com.common.constant.TokenType;
import com.common.constant.UserCartStatus;
import com.common.constant.UserTicketApplyStage;
import com.common.util.DateUtil;
import com.common.util.StringUtil;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.OrderTicket;
import com.domain.OrderTicketDetail;
import com.domain.UserCart;
import com.domain.UserTicketApply;
import com.dto.OrderDTO;
import com.dto.OrderProductDTO;
import com.dto.OrderTicketDTO;
import com.dto.OrderTicketDetailDTO;
import com.dto.ProductDTO;
import com.dto.ProductIngredientDTO;
import com.dto.ProductOptionDTO;
import com.dto.ProductSkuDTO;
import com.dto.ShopConfigDTO;
import com.dto.TableDTO;
import com.dto.TicketDTO;
import com.dto.UserCartDTO;
import com.query.OrderProductQuery;
import com.query.OrderQuery;
import com.github.pagehelper.PageInfo;
import com.query.OrderTicketDetailQuery;
import com.query.OrderTicketQuery;
import com.query.ShopConfigQuery;
import com.service.OrderProductService;
import com.service.OrderService;
import com.service.OrderTicketDetailService;
import com.service.OrderTicketService;
import com.service.ProductIngredientService;
import com.service.ProductOptionService;
import com.service.ProductService;
import com.service.ProductSkuService;
import com.service.ShopConfigService;
import com.service.TableService;
import com.service.TicketService;
import com.service.UserCartService;
import com.service.UserTicketApplyService;

/**
 * 订单管理
 * <AUTHOR>
 * @date 2025-09-06
 */
@RestController
public class OrderController extends BaseController {

    @Autowired
    private OrderService orderService;
    @Autowired
    private UserCartService userCartService;
    @Autowired
    private ProductService productService;
    @Autowired
    private ProductSkuService productSkuService;
    @Autowired
    private ProductOptionService productOptionService;
    @Autowired
    private ProductIngredientService productIngredientService;
    @Autowired
    private TableService tableService;
    @Autowired
    private OrderProductService orderProductService;
    @Autowired
    private TicketService ticketService;
    @Autowired
    private OrderTicketService orderTicketService;
    @Autowired
    private OrderTicketDetailService orderTicketDetailService;
    @Autowired
    private UserTicketApplyService userTicketApplyService;
    @Autowired
    private ShopConfigService shopConfigService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 用户普通点单
     */
    @RequestMapping(value = "/v1/order/product/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> createProduct(@RequestBody OrderQuery request) {
        OrderValidator validator = new OrderValidator();
        if (!validator.onUserCartIds(request.getUserCartIds()).onShopId(request.getShopId()).onTableId(request.getTableId()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Long userId = this.getUserToken().getId();
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_ORDER, userId.toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            // 查询桌子信息
            TableDTO tableDTO = tableService.findById(request.getTableId());
            if (tableDTO == null || !DataStatus.Y.getCode().equals(tableDTO.getStatus())) {
                return new Response<>(ERROR, "桌子不存在或已失效");
            }
            if (!tableDTO.getShopId().equals(request.getShopId())) {
                return new Response<>(ERROR, "桌子不属于该店铺");
            }
            List<Long> productIds = new ArrayList<>();
            List<Long> productSKUIds = new ArrayList<>();
            List<Long> productOptionIds = new ArrayList<>();
            List<Long> productIngredientIds = new ArrayList<>();
            // 查询是否存在无效的产品
            List<UserCartDTO> userCartDTOS = userCartService.findByIds(request.getUserCartIds());
            for (UserCartDTO userCartDTO : userCartDTOS) {
                if (!UserCartStatus.Y.getCode().equals(userCartDTO.getUserCartStatus())) {
                    return new Response<>(ERROR, "购物车中存在无效商品,请删除后再次下单");
                }
                if (!isEmpty(userCartDTO.getProductOptionIds())) {
                    productOptionIds.addAll(StringUtil.splitWithLong(userCartDTO.getProductOptionIds(), App.COMMA));
                }
                if (!userCartDTO.getUserId().equals(userId)) {
                    return new Response<>(ERROR, "购物车中存在不是您的商品,请删除后再次下单");
                }
                productIds.add(userCartDTO.getProductId());
                productSKUIds.add(userCartDTO.getProductSkuId());
                if (!isEmpty(userCartDTO.getProductIngredientIds())) {
                    productIngredientIds.addAll(StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA));
                }
            }
            Date datetime = this.getServerTime();
            Map<Long, ProductDTO> productDTOMap = productService.findMapByIds(productIds);
            Map<Long, ProductSkuDTO> productSkuDTOMap = productSkuService.findMapByIds(productSKUIds);
            Map<Long, ProductOptionDTO> productOptionDTOMap = productOptionService.findMapByIds(productOptionIds);
            Map<Long, ProductIngredientDTO> productIngredientDTOMap = productIngredientService.findMapByIds(productIngredientIds);
            // 记录已经下架的产品名字
            List<String> downProductNames = new ArrayList<>();
            AtomicBoolean isSameShop = new AtomicBoolean(true);
            productDTOMap.forEach((k, v) -> {
                if (!request.getShopId().equals(v.getShopId())) {
                    isSameShop.set(false);
                }
                if (!ProductStage.UP.getCode().equals(v.getStage())) {
                    downProductNames.add(v.getName());
                }
            });
            if (!isSameShop.get()) {
                return new Response<>(ERROR, "购物车中存在不同店铺的商品,请删除后再次下单");
            }
            if (!downProductNames.isEmpty()) {
                return new Response<>(ERROR, "购物车中存在下架商品【" + StringUtil.join(downProductNames, App.COMMA) + "】,请删除后再次下单");
            }
            // 将购物车中的产品合并为同一个订单
            Order order = new Order();
            order.setShopId(request.getShopId());
            order.setUserId(userId);
            order.setTableId(request.getTableId());
            order.setCode(this.getOrderCode());
            order.setOrderStage(OrderStage.WAIT_PAY.getCode());
            order.setType(OrderType.PRODUCT.getCode());
            order.setUserComment(request.getUserComment());
            List<OrderProduct> orderProducts = new ArrayList<>();
            // 存要修改的购物车信息
            List<UserCart> userCarts = new ArrayList<>();
            // 查询产品价格等信息
            BigDecimal totalPrice = new BigDecimal("0.00");
            for (UserCartDTO userCartDTO : userCartDTOS) {
                OrderProduct orderProduct = new OrderProduct();
                // 设置产品数量
                orderProduct.setNumber(userCartDTO.getNumber());
                BigDecimal price = new BigDecimal("0.00");
                if (productDTOMap.containsKey(userCartDTO.getProductId())) {
                    ProductDTO productDTO = productDTOMap.get(userCartDTO.getProductId());
                    orderProduct.setProductId(productDTO.getId());
                    orderProduct.setProductName(productDTO.getName());
                    orderProduct.setProductCover(productDTO.getPhoto());
                }
                if (productSkuDTOMap.containsKey(userCartDTO.getProductSkuId())) {
                    ProductSkuDTO productSkuDTO = productSkuDTOMap.get(userCartDTO.getProductSkuId());
                    price = price.add(productSkuDTO.getPrice());
                    orderProduct.setProductSkuName(productSkuDTO.getSize() + productSkuDTO.getUnit());
                }
                if (!isEmpty(userCartDTO.getProductOptionIds())) {
                    List<String> productOptionNames = new ArrayList<>();
                    List<Long> productOptionIdList = StringUtil.splitWithLong(userCartDTO.getProductOptionIds(), App.COMMA);
                    for (Long productOptionId : productOptionIdList) {
                        if (productOptionDTOMap.containsKey(productOptionId)) {
                            ProductOptionDTO productOptionDTO = productOptionDTOMap.get(productOptionId);
                            productOptionNames.add(productOptionDTO.getValue());
                        }
                    }
                    orderProduct.setProductOptionName(StringUtil.join(productOptionNames, App.COMMA));
                }
                if (!isEmpty(userCartDTO.getProductIngredientIds())) {
                    List<String> productIngredientNames = new ArrayList<>();
                    List<Long> productIngredientIdList = StringUtil.splitWithLong(userCartDTO.getProductIngredientIds(), App.COMMA);
                    for (Long productIngredientId : productIngredientIdList) {
                        if (productIngredientDTOMap.containsKey(productIngredientId)) {
                            price = price.add(productIngredientDTOMap.get(productIngredientId).getPrice());
                            productIngredientNames.add(productIngredientDTOMap.get(productIngredientId).getName());
                        }
                    }
                    orderProduct.setProductIngredientName(StringUtil.join(productIngredientNames, App.COMMA));
                }
                orderProduct.setAmount(price.multiply(new BigDecimal(userCartDTO.getNumber())));
                orderProduct.setStatus(DataStatus.Y.getCode());
                orderProduct.setModifyTime(datetime);
                orderProduct.setCreateTime(datetime);
                orderProducts.add(orderProduct);
                totalPrice = totalPrice.add(price.multiply(new BigDecimal(userCartDTO.getNumber())));
                UserCart userCart = new UserCart();
                userCart.setId(userCartDTO.getId());
                userCart.setStatus(DataStatus.N.getCode());
                userCart.setModifyTime(datetime);
                userCarts.add(userCart);
                UserCart userCartModify = new UserCart();
                userCartModify.setId(userCartDTO.getId());
                userCartModify.setStatus(DataStatus.N.getCode());
                userCartModify.setModifyTime(datetime);
                userCarts.add(userCartModify);
            }
            order.setAmount(totalPrice);
            order.setOrderTime(datetime);
            order.setUserStatus(DataStatus.Y.getCode());
            order.setStatus(DataStatus.Y.getCode());
            order.setModifyTime(datetime);
            order.setCreateTime(datetime);
            orderService.create(order, orderProducts, userCarts);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 用户购买门票
     */
    @RequestMapping(value = "/v1/order/ticket/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> createTicket(@RequestBody OrderQuery request) {
        OrderValidator validator = new OrderValidator();
        if (!validator.onShopId(request.getShopId()).onOrderTicket(request.getOrderTicket()).onOrderTicketDetailList(request.getOrderTicketDetailList()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        // 校验门票的时间和照片
        OrderTicketValidator orderTicketValidator = new OrderTicketValidator();
        if (!orderTicketValidator.onApplyTime(request.getOrderTicket().getApplyTimeStr()).result()) {
            return new Response<>(ERROR, orderTicketValidator.getErrorMessage());
        }
        // 校验是否传入的门票ID和购买数量
        OrderTicketDetailValidator orderTicketDetailValidator = new OrderTicketDetailValidator();
        for (OrderTicketDetailQuery orderTicketDetailQuery : request.getOrderTicketDetailList()) {
            if (!orderTicketDetailValidator.onNumber(orderTicketDetailQuery.getNumber()).onTicketId(orderTicketDetailQuery.getTicketId())
                    .result()) {
                return new Response<>(ERROR, orderTicketValidator.getErrorMessage());
            }
        }
        Long userId = this.getUserToken().getId();
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_ORDER, userId.toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            Map<Long, TicketDTO> ticketDTOMap = new HashMap<>();
            for (OrderTicketDetailQuery orderTicketDetailQuery : request.getOrderTicketDetailList()) {
                TicketDTO ticketDTO = ticketService.findById(orderTicketDetailQuery.getTicketId());
                if (ticketDTO == null) {
                    return new Response<>(ERROR, "【" + orderTicketDetailQuery.getTicketId() + "】门票不存在");
                }
                if (!ProductStage.UP.getCode().equals(ticketDTO.getStage())) {
                    return new Response<>(ERROR, "【" + ticketDTO.getName() + "】门票已下架");
                }
                if (!ticketDTO.getShopId().equals(request.getShopId())) {
                    return new Response<>(ERROR, "【" + ticketDTO.getName() + "】门票不属于该店铺");
                }
                ticketDTOMap.put(ticketDTO.getId(), ticketDTO);
            }
            Date datetime = this.getServerTime();
            Order order = new Order();
            order.setShopId(request.getShopId());
            order.setUserId(userId);
            order.setCode(this.getOrderCode());
            order.setOrderTime(datetime);
            order.setOrderStage(OrderStage.WAIT_PAY.getCode());
            order.setUserComment(request.getUserComment());
            order.setType(OrderType.TICKET.getCode());
            order.setCreateTime(datetime);
            order.setModifyTime(datetime);
            OrderTicket orderTicket = new OrderTicket();
            if (!isEmpty(request.getOrderTicket().getApplyTimeStr())) {
                Date applyTime = DateUtil.parse(request.getOrderTicket().getApplyTimeStr(), DATE_FORMAT);
                if (applyTime.before(DateUtil.getDayStartTime(datetime))) {
                    return new Response<>(ERROR, "报名时间不能小于当前时间");
                }
                orderTicket.setApplyTime(applyTime);
            }
            if (!isEmpty(request.getOrderTicket().getPhotoList()) && !request.getOrderTicket().getPhotoList().isEmpty()) {
                orderTicket.setPhoto(StringUtil.join(request.getOrderTicket().getPhotoList(), App.COMMA));
            }
            orderTicket.setApplyTime(DateUtil.parse(request.getOrderTicket().getApplyTimeStr(), DATE_FORMAT));
            orderTicket.setUserId(userId);
            orderTicket.setStatus(DataStatus.Y.getCode());
            orderTicket.setCreateTime(datetime);
            orderTicket.setModifyTime(datetime);
            // 总价格
            BigDecimal totalPrice = new BigDecimal("0.00");
            List<OrderTicketDetail> orderTicketDetails = new ArrayList<>();
            for (OrderTicketDetailQuery orderTicketDetailQuery : request.getOrderTicketDetailList()) {
                OrderTicketDetail orderTicketDetail = new OrderTicketDetail();
                orderTicketDetail.setNumber(orderTicketDetailQuery.getNumber());
                orderTicketDetail.setTicketId(orderTicketDetailQuery.getTicketId());
                orderTicketDetail.setTicketName(ticketDTOMap.get(orderTicketDetailQuery.getTicketId()).getName());
                orderTicketDetail.setAmount(ticketDTOMap.get(orderTicketDetailQuery.getTicketId()).getPrice().multiply(new BigDecimal(orderTicketDetail.getNumber())));
                totalPrice = totalPrice.add(orderTicketDetail.getAmount());
                orderTicketDetail.setStatus(DataStatus.Y.getCode());
                orderTicketDetail.setModifyTime(datetime);
                orderTicketDetail.setCreateTime(datetime);
                orderTicketDetails.add(orderTicketDetail);
            }
            order.setAmount(totalPrice);
            orderService.create(order, orderTicket, orderTicketDetails);
            // 模拟支付完成，生成待审批的门票申请
            test(order.getId());
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }


    public void test(Long id){
        // 查询这笔订单
        OrderDTO orderDTO = orderService.findById(id);
        if(orderDTO == null){
            return;
        }
        if (OrderType.TICKET.getCode().equals(orderDTO.getType())){
            OrderTicketQuery orderTicketQuery = new OrderTicketQuery();
            orderTicketQuery.setOrderId(id);
            orderTicketQuery.setStatus(DataStatus.Y.getCode());
            List<OrderTicketDTO> orderTicketDTOs = orderTicketService.findAll(orderTicketQuery);
            if(orderTicketDTOs.isEmpty()){
                return;
            }
            OrderTicketDetailQuery orderTicketDetailQuery = new OrderTicketDetailQuery();
            orderTicketDetailQuery.setOrderId(id);
            orderTicketDetailQuery.setStatus(DataStatus.Y.getCode());
            List<OrderTicketDetailDTO> orderTicketDetailDTOS = orderTicketDetailService.findAll(orderTicketDetailQuery);
            if(orderTicketDetailDTOS.isEmpty()){
                return;
            }

            // 生成待审批的门票申请
            UserTicketApply userTicketApply = new UserTicketApply();
            userTicketApply.setUserId(orderDTO.getUserId());
            userTicketApply.setShopId(orderDTO.getShopId());
            userTicketApply.setOrderId(id);
            userTicketApply.setApplyTime(orderTicketDTOs.get(0).getApplyTime());
            userTicketApply.setPhoto(orderTicketDTOs.get(0).getPhoto());
            userTicketApply.setUserComment(orderDTO.getUserComment());
            userTicketApply.setStage(UserTicketApplyStage.PENDING.getCode());
            userTicketApply.setStatus(DataStatus.Y.getCode());
            userTicketApply.setModifyTime(this.getServerTime());
            userTicketApply.setCreateTime(this.getServerTime());
            userTicketApplyService.create(userTicketApply);
        }
    }


    /**
     * 分页查询订单列表
     */
    @RequestMapping(value = "/v1/order/query", method = RequestMethod.POST)
    public Response<PageInfo<OrderDTO>> query(@RequestBody OrderQuery orderQuery) {
        orderQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<OrderDTO> pageInfo = orderService.find(orderQuery);
        List<Long> orderIds = new ArrayList<>();
        List<Long> shopIds = new ArrayList<>();
        // 查询订单下面的产品
        for (OrderDTO orderDTO : pageInfo.getList()) {
            orderDTO.setOrderStageName(OrderPayStage.getName(orderDTO.getOrderStage()));
            orderDTO.setPayStageName(OrderPayStage.getName(orderDTO.getPayStage()));
            orderDTO.setTypeName(OrderType.getName(orderDTO.getType()));
            orderIds.add(orderDTO.getId());
            shopIds.add(orderDTO.getShopId());
        }
        OrderProductQuery orderProductQuery = new OrderProductQuery();
        orderProductQuery.setOrderIds(orderIds);
        orderProductQuery.setStatus(DataStatus.Y.getCode());
        List<OrderProductDTO> orderProductDTOS = orderProductService.findAll(orderProductQuery);
        // 查询门票信息
        OrderTicketQuery orderTicketQuery = new OrderTicketQuery();
        orderTicketQuery.setOrderIds(orderIds);
        orderTicketQuery.setStatus(DataStatus.Y.getCode());
        Map<Long, OrderTicketDTO> orderTicketDTOMap = new HashMap<>();
        List<OrderTicketDTO> orderTicketDTOS = orderTicketService.findAll(orderTicketQuery);
        for (OrderTicketDTO orderTicketDTO : orderTicketDTOS) {
            orderTicketDTOMap.put(orderTicketDTO.getId(), orderTicketDTO);
        }
        // 查询门票详情
        OrderTicketDetailQuery orderTicketDetailQuery = new OrderTicketDetailQuery();
        orderTicketDetailQuery.setOrderIds(orderIds);
        orderTicketDetailQuery.setStatus(DataStatus.Y.getCode());
        List<OrderTicketDetailDTO> orderTicketDetailDTOS = orderTicketDetailService.findAll(orderTicketDetailQuery);
        Map<Long, List<OrderTicketDetailDTO>> orderIdOrderTicketDetailDTOMap = new HashMap<>();
        for (OrderTicketDetailDTO orderTicketDetailDTO : orderTicketDetailDTOS) {
            if (!orderIdOrderTicketDetailDTOMap.containsKey(orderTicketDetailDTO.getOrderId())) {
                orderIdOrderTicketDetailDTOMap.put(orderTicketDetailDTO.getOrderId(), new ArrayList<>());
            }
            orderIdOrderTicketDetailDTOMap.get(orderTicketDetailDTO.getOrderId()).add(orderTicketDetailDTO);
        }
        // 查询店铺配置
        Map<Long,ShopConfigDTO> shopConfigDTOMap = new HashMap<>();
        if (!shopIds.isEmpty()){
            ShopConfigQuery shopConfigQuery = new ShopConfigQuery();
            shopConfigQuery.setShopIds(shopIds);
            List<ShopConfigDTO> shopConfigDTOS = shopConfigService.findAll(shopConfigQuery);
            for (ShopConfigDTO shopConfigDTO : shopConfigDTOS) {
                shopConfigDTOMap.put(shopConfigDTO.getShopId(),shopConfigDTO);
            }
        }
        Map<Long, List<OrderProductDTO>> orderIdOrderProductDTOMap = new HashMap<>();
        for (OrderProductDTO orderProductDTO : orderProductDTOS) {
            if (!orderIdOrderProductDTOMap.containsKey(orderProductDTO.getOrderId())) {
                orderIdOrderProductDTOMap.put(orderProductDTO.getOrderId(), new ArrayList<>());
            }
            orderIdOrderProductDTOMap.get(orderProductDTO.getOrderId()).add(orderProductDTO);
        }
        for (OrderDTO orderDTO : pageInfo.getList()) {
            // 返回产品信息
            if (orderIdOrderProductDTOMap.containsKey(orderDTO.getId())) {
                orderDTO.setOrderProductList(orderIdOrderProductDTOMap.get(orderDTO.getId()));
            }
            orderDTO.setOrderProductList(orderIdOrderProductDTOMap.get(orderDTO.getId()));
            // 返回门票信息
            if (orderTicketDTOMap.containsKey(orderDTO.getId())) {
                OrderTicketDTO orderTicketDTO = orderTicketDTOMap.get(orderDTO.getId());
                if (orderTicketDTO.getApplyTime() != null) {
                    orderTicketDTO.setApplyTimeStr(DateUtil.format(orderTicketDTO.getApplyTime(), DATE_FORMAT));
                }
                orderDTO.setOrderTicket(orderTicketDTO);
            }
            // 返回门票详情信息
            if (orderIdOrderTicketDetailDTOMap.containsKey(orderDTO.getId())) {
                orderDTO.setOrderTicketDetailList(orderIdOrderTicketDetailDTOMap.get(orderDTO.getId()));
            }
            // 返回店铺信息
            if (shopConfigDTOMap.containsKey(orderDTO.getShopId())) {
                orderDTO.setShopConfig(shopConfigDTOMap.get(orderDTO.getShopId()));
            }
        }
        return new Response<>(pageInfo);
    }

    /**
     * 分页查询我的订单列表
     */
    @RequestMapping(value = "/v1/order/my/query", method = RequestMethod.POST)
    public Response<PageInfo<OrderDTO>> queryMy(@RequestBody OrderQuery orderQuery) {
        orderQuery.setStatus(DataStatus.Y.getCode());
        orderQuery.setUserStatus(DataStatus.Y.getCode());
        orderQuery.setUserId(this.getUserToken().getId());
        return this.query(orderQuery);
    }


    /**
     * 全部查询订单列表
     */
    @RequestMapping(value = "/v1/order/all/query", method = RequestMethod.POST)
    public Response<List<OrderDTO>> queryAll(@RequestBody OrderQuery orderQuery) {
        orderQuery.setStatus(DataStatus.Y.getCode());
        List<OrderDTO> orderDTOs = orderService.findAll(orderQuery);
        return new Response<>(orderDTOs);
    }


    /**
     * 修改订单
     */
    @RequestMapping(value = "/v1/order/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Order order) {
        Date datetime = this.getServerTime();
        order.setModifyTime(datetime);
        orderService.modifyById(order);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 用户删除订单
     */
    @RequestMapping(value = "/v1/order/user/remove", method = RequestMethod.POST)
    public Response<?> removeByUser(@RequestBody OrderQuery request) {
        Date datetime = this.getServerTime();
        Order order = new Order();
        order.setId(request.getId());
        order.setUserStatus(DataStatus.N.getCode());
        order.setModifyTime(datetime);
        orderService.modifyById(order);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 管理员删除订单
     */
    @RequestMapping(value = "/v1/order/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody OrderQuery request) {
        Date datetime = this.getServerTime();
        Order order = new Order();
        order.setId(request.getId());
        order.setStatus(DataStatus.N.getCode());
        order.setModifyTime(datetime);
        orderService.modifyById(order);
        return new Response<>(OK, SUCCESS);
    }


}
