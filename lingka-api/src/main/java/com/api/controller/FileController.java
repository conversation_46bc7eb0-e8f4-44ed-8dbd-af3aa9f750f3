package com.api.controller;

import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.util.Base64;
import java.util.Date;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import com.common.bean.ExportMessage;
import com.common.bean.ExportResponse;
import com.common.bean.Response;
import com.common.client.ALiOSSClient;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.ExportStage;
import com.dto.FileDTO;
import com.service.FileService;

/**
 * 文件模块接口
 *
 * <AUTHOR>
 */
@Controller
public class FileController extends BaseController {

    @Value("${spring.file.location}")
    private String location;

    @Autowired
    private FileService fileService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @RequestMapping(value = "/v1/file/upload", method = RequestMethod.POST)
    @ResponseBody
    public Response<?> upload(@RequestParam("file") MultipartFile file) {
        com.domain.File f = new com.domain.File();
        if (!file.isEmpty()) {
            try {
                String originalFilename = file.getOriginalFilename();
                logger.info("file original name {}", originalFilename);
                logger.info("file size {}", file.getSize());
                logger.info("file content type {}", file.getContentType());
                String ossUrl = this.getOssUrl(originalFilename);
                // 存储到阿里云oss
                ALiOSSClient client = new ALiOSSClient();
                client.upload(ossUrl, file.getInputStream());
                f.setName(originalFilename);
                f.setType(file.getContentType());
                f.setUrl(ossUrl);
                f.setSize(file.getSize());
                fileService.create(f);
                return new Response<>(ossUrl);
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                return new Response<String>(ERROR, FAILURE);
            }
        } else {
            logger.info("file is empty");
            return new Response<String>(ERROR, FAILURE);
        }
    }

    @RequestMapping(value = "/v1/file/base64/image/upload", method = RequestMethod.POST)
    public @ResponseBody Response<?> uploadBase64Image(@RequestBody FileDTO request) {
        try {
            File destFile = this.getDestFile(UUID.randomUUID() + request.getType());
            Base64.Decoder decoder = Base64.getDecoder();
            // 去掉base64前缀 data:image/jpeg;base64,
            String data = request.getData().substring(request.getData().indexOf(",", 1) + 1);
            byte[] b = decoder.decode(data);
            // 保存图片
            FileOutputStream out = new FileOutputStream(destFile);
            out.write(b);
            out.flush();
            out.close();
            // 写入成功返回文件路径
            // 保存到数据库
            com.domain.File f = new com.domain.File();
            Date date = this.getServerTime();
            f.setUrl(destFile.getAbsolutePath().replace(location, ""));
            // 存储到阿里云oss
            ALiOSSClient client = new ALiOSSClient();
            String fileName = destFile.getAbsolutePath().replace(location + "/", "");
            String ossUrl = client.upload(fileName, Files.newInputStream(destFile.toPath()));
            f.setOssUrl(ossUrl);
            f.setStatus(DataStatus.Y.getCode());
            f.setModifyTime(date);
            f.setCreateTime(date);
            fileService.create(f);
            return new Response<>(f.getUrl());
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

    @RequestMapping(value = "/v1/file/download")
    public String download(@ModelAttribute com.domain.File file) {
        ALiOSSClient ossClient = new ALiOSSClient();
        String ossUrl = ossClient.generatePresignedUrl(file.getUrl());
        return this.redirect(ossUrl);
    }


    @RequestMapping(value = "/v1/file/export/url/query", method = RequestMethod.POST)
    @ResponseBody
    public Response<?> query(@RequestBody ExportMessage request) {
        try {
            ExportResponse response = new ExportResponse();
            if (isEmpty(request.getCode())) {
                return new Response<>(ERROR, "请输入下载编号");
            }
            String stage = redisTemplate.opsForValue().get(CacheKey.FILE_DOWNLOAD_URL + request.getCode());
            if (isEmpty(stage)) {
                return new Response<>(ERROR, "未查到到相关下载信息");
            }
            response.setStage(stage);
            // 长度大于1 是文件地址
            assert stage != null;
            if (stage.length() > 1) {
                response.setStage(ExportStage.OK.getCode());
                response.setUrl(stage);
            }
            return new Response<>(response);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        }
    }

}
