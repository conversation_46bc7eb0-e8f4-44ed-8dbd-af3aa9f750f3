package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ProductOption;
import com.dto.ProductOptionDTO;
import com.query.ProductOptionQuery;
import com.github.pagehelper.PageInfo;
import com.service.ProductOptionService;

/**
 * 产品选项控制器
 *
 * <AUTHOR>
 * @date 2025-08-28
 */
@RestController
public class ProductOptionController extends BaseController {

    @Autowired
    private ProductOptionService productOptionService;

    /**
     * 增加产品选项
     */
    @RequestMapping(value = "/v1/product/option/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody ProductOption productOption) {
        Date datetime = this.getServerTime();
        productOption.setStatus(DataStatus.Y.getCode());
        productOption.setModifyTime(datetime);
        productOption.setCreateTime(datetime);
        productOptionService.create(productOption);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询产品选项列表
     */
    @RequestMapping(value = "/v1/product/option/query", method = RequestMethod.POST)
    public Response<PageInfo<ProductOptionDTO>> query(@RequestBody ProductOptionQuery productOptionQuery) {
        productOptionQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ProductOptionDTO> pageInfo = productOptionService.find(productOptionQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询产品选项列表
     */
    @RequestMapping(value = "/v1/product/option/all/query", method = RequestMethod.POST)
    public Response<List<ProductOptionDTO>> queryAll(@RequestBody ProductOptionQuery productOptionQuery) {
        productOptionQuery.setStatus(DataStatus.Y.getCode());
        List<ProductOptionDTO> productOptionDTOs = productOptionService.findAll(productOptionQuery);
        return new Response<>(productOptionDTOs);
    }


    /**
     * 修改产品选项
     */
    @RequestMapping(value = "/v1/product/option/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody ProductOption productOption) {
        Date datetime = this.getServerTime();
        productOption.setModifyTime(datetime);
        productOptionService.modifyById(productOption);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除产品选项
     */
    @RequestMapping(value = "/v1/product/option/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ProductOptionQuery request) {
        Date datetime = this.getServerTime();
        ProductOption productOption = new ProductOption();
        productOption.setId(request.getId());
        productOption.setStatus(DataStatus.N.getCode());
        productOption.setModifyTime(datetime);
        productOptionService.modifyById(productOption);
        return new Response<>(OK, SUCCESS);
    }


}
