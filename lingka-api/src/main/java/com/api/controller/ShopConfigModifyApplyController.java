package com.api.controller;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.domain.ShopConfigModifyApply;
import com.dto.ShopConfigModifyApplyDTO;
import com.query.ShopConfigModifyApplyQuery;
import com.github.pagehelper.PageInfo;
import com.service.ShopConfigModifyApplyService;

/**
 * 店铺配置修改申请控制器
 *
 * <AUTHOR>
 */
@RestController
public class ShopConfigModifyApplyController extends BaseController {

    @Autowired
    private ShopConfigModifyApplyService shopConfigModifyApplyService;

    /**
     * 增加店铺配置修改申请
     */
    @RequestMapping(value = "/v1/shop/config/modify/apply/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody ShopConfigModifyApply shopConfigModifyApply) {
        Date datetime = this.getServerTime();
        shopConfigModifyApply.setStatus(DataStatus.Y.getCode());
        shopConfigModifyApply.setModifyTime(datetime);
        shopConfigModifyApply.setCreateTime(datetime);
        shopConfigModifyApplyService.create(shopConfigModifyApply);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询店铺配置修改申请列表
     */
    @RequestMapping(value = "/v1/shop/config/modify/apply/query", method = RequestMethod.POST)
    public Response<PageInfo<ShopConfigModifyApplyDTO>> query(@RequestBody ShopConfigModifyApplyQuery shopConfigModifyApplyQuery) {
        shopConfigModifyApplyQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ShopConfigModifyApplyDTO> pageInfo = shopConfigModifyApplyService.find(shopConfigModifyApplyQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询店铺配置修改申请列表
     */
    @RequestMapping(value = "/v1/shop/config/modify/apply/all/query", method = RequestMethod.POST)
    public Response<List<ShopConfigModifyApplyDTO>> queryAll(@RequestBody ShopConfigModifyApplyQuery shopConfigModifyApplyQuery) {
        shopConfigModifyApplyQuery.setStatus(DataStatus.Y.getCode());
        List<ShopConfigModifyApplyDTO> shopConfigModifyApplyDTOs = shopConfigModifyApplyService.findAll(shopConfigModifyApplyQuery);
        return new Response<>(shopConfigModifyApplyDTOs);
    }


    /**
     * 修改店铺配置修改申请
     */
    @RequestMapping(value = "/v1/shop/config/modify/apply/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody ShopConfigModifyApply shopConfigModifyApply) {
        Date datetime = this.getServerTime();
        shopConfigModifyApply.setModifyTime(datetime);
        shopConfigModifyApplyService.modifyById(shopConfigModifyApply);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除店铺配置修改申请
     */
    @RequestMapping(value = "/v1/shop/config/modify/apply/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody ShopConfigModifyApplyQuery request) {
        Date datetime = this.getServerTime();
        ShopConfigModifyApply shopConfigModifyApply = new ShopConfigModifyApply();
        shopConfigModifyApply.setId(request.getId());
        shopConfigModifyApply.setStatus(DataStatus.N.getCode());
        shopConfigModifyApply.setModifyTime(datetime);
        shopConfigModifyApplyService.modifyById(shopConfigModifyApply);
        return new Response<>(OK, SUCCESS);
    }


}
