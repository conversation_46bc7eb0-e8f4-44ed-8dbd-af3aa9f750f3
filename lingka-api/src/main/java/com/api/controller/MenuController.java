package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.common.bean.Response;
import com.common.constant.DataStatus;
import com.common.constant.TokenType;
import com.domain.Menu;
import com.dto.MenuDTO;
import com.dto.RoleMenuDTO;
import com.dto.UserRoleDTO;
import com.query.MenuQuery;
import com.github.pagehelper.PageInfo;
import com.query.RoleMenuQuery;
import com.query.UserRoleQuery;
import com.service.MenuService;
import com.service.RoleMenuService;
import com.service.UserRoleService;

/**
 * 菜单控制器
 *
 * <AUTHOR>
 * @date 2025-08-18
 */
@RestController
public class MenuController extends BaseController {

    @Autowired
    private MenuService menuService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private RoleMenuService roleMenuService;

    /**
     * 增加菜单
     */
    @RequestMapping(value = "/v1/menu/create", method = RequestMethod.POST)
    public Response<?> create(@RequestBody Menu menu) {
        Date datetime = this.getServerTime();
        menu.setStatus(DataStatus.Y.getCode());
        menu.setModifyTime(datetime);
        menu.setCreateTime(datetime);
        menuService.create(menu);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 分页查询菜单列表
     */
    @RequestMapping(value = "/v1/menu/query", method = RequestMethod.POST)
    public Response<PageInfo<MenuDTO>> query(@RequestBody MenuQuery menuQuery) {
        menuQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<MenuDTO> pageInfo = menuService.find(menuQuery);
        return new Response<>(pageInfo);
    }

    /**
     * 全部查询菜单列表
     */
    @RequestMapping(value = "/v1/menu/all/query", method = RequestMethod.POST)
    public Response<List<MenuDTO>> queryAll(@RequestBody MenuQuery menuQuery) {
        menuQuery.setStatus(DataStatus.Y.getCode());
        List<MenuDTO> menuDTOs = menuService.findAll(menuQuery);
        return new Response<>(menuDTOs);
    }


    /**
     * 修改菜单
     */
    @RequestMapping(value = "/v1/menu/modify", method = RequestMethod.POST)
    public Response<?> modify(@RequestBody Menu menu) {
        Date datetime = this.getServerTime();
        menu.setModifyTime(datetime);
        menuService.modifyById(menu);
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 删除菜单
     */
    @RequestMapping(value = "/v1/menu/remove", method = RequestMethod.POST)
    public Response<?> remove(@RequestBody MenuQuery request) {
        Date datetime = this.getServerTime();
        Menu menu = new Menu();
        menu.setId(request.getId());
        menu.setStatus(DataStatus.N.getCode());
        menu.setModifyTime(datetime);
        menuService.modifyById(menu);
        return new Response<>(OK, SUCCESS);
    }


    /**
     * 查询我的菜单列表
     */
    @RequestMapping(value = "/v1/menu/all/my/query", method = RequestMethod.POST)
    @Token(tokenType = TokenType.C)
    public Response<Set<String>> queryMy(@RequestBody MenuQuery menuQuery) {
        // 存贮当前登录人可以访问的菜单ID
        Set<String> menuNames = new HashSet<>();
        // 查询当前登录人的用户角色
        UserRoleQuery userRoleQuery = new UserRoleQuery();
        userRoleQuery.setUserId(this.getUserToken().getId());
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOS = userRoleService.findAll(userRoleQuery);
        List<Long> roleIds = new ArrayList<>();
        for (UserRoleDTO userRoleDTO : userRoleDTOS) {
            roleIds.add(userRoleDTO.getRoleId());
        }
        if (roleIds.isEmpty()) {
            return new Response<>(menuNames);
        }
        RoleMenuQuery roleMenuQuery = new RoleMenuQuery();
        roleMenuQuery.setRoleIds(roleIds);
        roleMenuQuery.setStatus(DataStatus.Y.getCode());
        List<RoleMenuDTO> roleMenuDTOs = roleMenuService.findAll(roleMenuQuery);
        List<Long> menuIds = new ArrayList<>();
        for (RoleMenuDTO roleMenuDTO : roleMenuDTOs) {
            menuIds.add(roleMenuDTO.getMenuId());
        }
        if (menuIds.isEmpty()){
            return new Response<>(menuNames);
        }
        List<MenuDTO> menuDTOS = menuService.findByIds(menuIds);
        for (MenuDTO menuDTO : menuDTOS) {
            if (DataStatus.Y.getCode().equals(menuDTO.getStatus())){
                menuNames.add(menuDTO.getName());
            }
        }
        return new Response<>(menuNames);
    }

}
