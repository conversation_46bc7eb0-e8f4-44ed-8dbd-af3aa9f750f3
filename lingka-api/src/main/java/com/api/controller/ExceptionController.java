package com.api.controller;

import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.common.bean.Response;

/**
 * <AUTHOR>
 * @date 2023-01-05 14:46
 */
@RestControllerAdvice
public class ExceptionController extends BaseController {

	@ExceptionHandler(RuntimeException.class)
	public Response<?> handleException(Exception e){
		logger.error(e.getMessage(),e);
		return new Response<>(ERROR,FAILURE);
	}
}
