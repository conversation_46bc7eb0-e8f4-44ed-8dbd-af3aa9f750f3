package com.api.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.api.config.Token;
import com.api.validator.ShopValidator;
import com.common.bean.JsonWebToken;
import com.common.bean.Response;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.ShopStatus;
import com.common.constant.TokenType;
import com.common.util.ReflectDiffUtils;
import com.domain.Shop;
import com.domain.ShopConfig;
import com.dto.AdminDTO;
import com.dto.ShopConfigDTO;
import com.dto.ShopConfigWithUserRoleDTO;
import com.dto.ShopDTO;
import com.dto.ShopDetailDTO;
import com.dto.UserDTO;
import com.dto.UserRoleDTO;
import com.github.pagehelper.PageInfo;
import com.query.ShopQuery;
import com.query.UserRoleQuery;
import com.service.AdminService;
import com.service.ShopConfigService;
import com.service.ShopOpenCloseTimeService;
import com.service.ShopService;
import com.service.UserRoleService;
import com.service.UserService;
import com.service.UserShopHistoryService;
import com.service.UserShopService;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 店铺管理模块接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/v1/shop")
public class ShopController extends BaseController {
    @Autowired
    private ShopService shopService;
    @Autowired
    private AdminService adminService;
    @Autowired
    private ShopConfigService shopConfigService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private UserShopService userShopService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private UserService userService;
    @Autowired
    private ShopOpenCloseTimeService shopOpenCloseTimeService;
    @Autowired
    private UserShopHistoryService userShopHistoryService;

    /**
     * 查询店铺列表
     */
    @PostMapping("/list")
    @Token(tokenType = {TokenType.S, TokenType.C})
    @ResponseBody
    public Response<PageInfo<ShopDTO>> list(@RequestBody ShopQuery shopQuery) {
        shopQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<ShopDTO> pageInfo = shopService.selectShopList(shopQuery);
        Set<Long> shopIds = new HashSet<>();
        Set<Long> adminIds = new HashSet<>();
        for (ShopDTO shopDTO : pageInfo.getList()) {
            shopIds.add(shopDTO.getId());
            adminIds.add(shopDTO.getCreateAdminId());
        }
        JsonWebToken token = this.getRawJsonWebToken();
        assert token != null;
        Map<Long, AdminDTO> adminDTOMap = adminService.findMapByIds(new ArrayList<>(adminIds));
        Map<Long, ShopConfigDTO> shopConfigDTOMap = shopConfigService.findMapByShopIds(new ArrayList<>(shopIds));
        for (ShopDTO shopDTO : pageInfo.getList()) {
            if (shopConfigDTOMap.containsKey(shopDTO.getId())) {
                ShopConfigDTO shopConfigDTO = shopConfigDTOMap.get(shopDTO.getId());
                if (TokenType.C.name().equals(token.getType()) && DataStatus.N.getCode().equals(shopConfigDTO.getDisplaySwitch())) {
                    continue;
                }
                shopDTO.setShopName(shopConfigDTO.getName());
            }
            if (adminDTOMap.containsKey(shopDTO.getCreateAdminId())) {
                shopDTO.setCreateAdminName(adminDTOMap.get(shopDTO.getCreateAdminId()).getName());
            }
        }
        return new Response<>(pageInfo);
    }

    /**
     * 查询店铺详情
     */
    @PostMapping("/detail")
    @Token(tokenType = {TokenType.S, TokenType.C})
    @ResponseBody
    public Response<ShopDetailDTO> getDetail(@RequestBody ShopQuery shopQuery) {
        JsonWebToken token = this.getRawJsonWebToken();
        assert token != null;
        Long userId = token.getUserId();
        shopQuery.setStatus(DataStatus.Y.getCode());
        Long shopId = shopQuery.getId();
        if (shopId == null) {
            return new Response<>(ERROR, FAILURE + "：店铺ID不能为空");
        }
        ShopDTO shopDTO = shopService.selectShopById(shopId);
        if (shopDTO == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在或已被删除");
        }
        AdminDTO adminDTO = adminService.findById(shopDTO.getCreateAdminId());
        ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(shopId);
        if (adminDTO == null || shopConfigDTO == null) {
            if (adminDTO == null || Objects.equals(DataStatus.N.getCode(), adminDTO.getStatus())) {
                logger.error("查询店铺详情失败：Admin 不存在或已禁用，店铺ID：{}", shopId);
            }
            if (shopConfigDTO == null) {
                logger.error("查询店铺详情失败：ShopConfig 不存在，店铺ID：{}", shopId);
            }
            return new Response<>(ERROR, FAILURE + "：店铺不存在或已被删除");
        }
        shopDTO.setCreateAdminName(adminDTO.getName());
        shopDTO.setShopName(shopConfigDTO.getName());
        UserRoleQuery userRoleQuery = new UserRoleQuery();
        userRoleQuery.setShopId(shopId);
        userRoleQuery.setStatus(DataStatus.Y.getCode());
        List<UserRoleDTO> userRoleDTOList = userRoleService.findAll(userRoleQuery);
        ShopConfigWithUserRoleDTO shopConfigWithUserRoleDTO = new ShopConfigWithUserRoleDTO(shopConfigDTO, userRoleDTOList);
        ShopDetailDTO shopDetailDTO = new ShopDetailDTO(shopDTO, shopConfigWithUserRoleDTO);
        if (!Objects.equals(userId, shopDetailDTO.getShop().getOwnerUserId()) && !TokenType.S.name().equals(token.getType())) {
            shopDetailDTO.getShop().setOwnerIdNumber(null);
            shopDetailDTO.getShop().setOwnerIdNumberPhoto(null);
            shopDetailDTO.getShop().setOwnerName(null);
        }
        return new Response<>(shopDetailDTO);
    }

    /**
     * 新增店铺
     */
    @PostMapping("/add")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> addSave(@RequestBody ShopQuery request) {
        ShopValidator validator = new ShopValidator();
        if (!validator.onShopName(request.getShopName()).onOwnerMobile(request.getOwnerMobile()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Long shopOwnerUserId = request.getOwnerUserId();
        UserDTO userDTO = userService.findById(shopOwnerUserId);
        if (userDTO == null || DataStatus.N.getCode().equals(userDTO.getStatus())) {
            return new Response<>(ERROR, FAILURE + "：店铺拥有者不存在或已被禁用");
        }
        Date datetime = this.getServerTime();
        Long adminUserId = this.getUserToken().getId();
        Shop shop = new Shop();
        BeanUtils.copyProperties(request, shop);
        shop.setCreateAdminId(adminUserId);
        shop.setStatus(DataStatus.Y.getCode());
        shop.setCreateTime(datetime);
        shop.setModifyTime(datetime);
        shop.setStatus(DataStatus.Y.getCode());
        if (shop.getShopStatus() == null) {
            shop.setShopStatus(ShopStatus.ENABLED.getCode());
        }
        ShopConfig shopConfig = new ShopConfig();
        shopConfig.setName(request.getShopName());
        shopConfig.setStatus(DataStatus.Y.getCode());
        shopConfig.setModifyTime(datetime);
        shopConfig.setCreateTime(datetime);
        shopConfig.setDisplaySwitch(DataStatus.N.getCode());
        int count = shopService.createShopAndShopConfig(shop, shopConfig);
        count += userRoleService.removeByShopId(shop.getId());
        count += userShopService.removeByShopId(shop.getId());
        count += userRoleService.removeByUserIdAndShopIdAndCreate(shopOwnerUserId, shop.getId(), Collections.singletonList(10000000L));
        redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + shopOwnerUserId);
        if (count < 3) {
            logger.error("新增店铺失败，数据插入异常，店铺ID：{}", shop.getId());
            return new Response<>(ERROR, FAILURE + "：数据插入异常");
        }
        return new Response<>(OK, SUCCESS);
    }

    /**
     * 修改店铺
     */
    @PostMapping("/edit")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> editSave(@RequestBody ShopQuery shopQuery) {
        if (shopQuery.getId() == null) {
            return new Response<>(ERROR, FAILURE + "：店铺ID不能为空");
        }
        ShopDTO shopDTOOld = shopService.selectShopById(shopQuery.getId());
        if (shopDTOOld == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在");
        }
        ShopValidator validator = new ShopValidator();
        if (shopQuery.getShopName() != null) {
            if (!validator.onShopName(shopQuery.getShopName()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        if (shopQuery.getOwnerMobile() != null) {
            if (!validator.onOwnerMobile(shopQuery.getOwnerMobile()).result()) {
                return new Response<>(ERROR, validator.getErrorMessage());
            }
        }
        Shop shopOld = new Shop();
        BeanUtils.copyProperties(shopDTOOld, shopOld);
        Shop shopNew = new Shop();
        BeanUtils.copyProperties(shopQuery, shopNew,
                "createAdminId", "createTime", "modifyTime", "status");
        Map<String, Object> changes = ReflectDiffUtils.diff(shopOld, shopNew);
        int count = shopService.updateShop(shopNew);
        int successCount = 1;
        if (changes.containsKey("owner_user_id")) {
            successCount++;
            Long shopOwnerUserId = shopNew.getOwnerUserId();
            UserDTO userDTO = userService.findById(shopOwnerUserId);
            if (userDTO == null || DataStatus.N.getCode().equals(userDTO.getStatus())) {
                return new Response<>(ERROR, FAILURE + "：店铺拥有者不存在或已被禁用");
            }
            count += userRoleService.removeByUserIdAndShopIdAndCreate(shopOwnerUserId, shopNew.getId(), Collections.singletonList(10000000L));
            redisTemplate.delete(CacheKey.USER_TOKEN + TokenType.C.name() + shopOwnerUserId);
        }
        if (count >= successCount)
            return new Response<>(OK, SUCCESS);
        return new Response<>(FAILURE, FAILURE + "：数据更新异常");
    }

    /**
     * 删除店铺
     */
    @PostMapping("/remove")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> remove(@RequestBody Shop request) {
        if (shopService.selectShopById(request.getId()) == null) {
            return new Response<>(ERROR, FAILURE + "：店铺不存在");
        }
        Shop shop = new Shop();
        shop.setId(request.getId());
        shop.setStatus(DataStatus.N.getCode());
        shop.setModifyTime(this.getServerTime());
        int count = shopService.updateShop(shop);
        count += userRoleService.removeByShopId(shop.getId());
        count += userShopService.removeByShopId(shop.getId());
        ShopConfigDTO shopConfigDTO = shopConfigService.findByShopId(shop.getId());
        if (shopConfigDTO != null) {
            count += shopConfigService.removeById(shopConfigDTO.getId());
        }
        count += shopOpenCloseTimeService.removeByShopId(request.getId());
        count += userShopHistoryService.removeByShopId(request.getId());
        if (count > 0) {
            return new Response<>(OK, SUCCESS);
        }
        return new Response<>(ERROR, FAILURE);
    }

    /**
     * 批量删除店铺
     * 这个接口和删除单个店铺的行为不一致，没有保证全部数据都处理
     * 现在需求中没有批量删除功能，暂时不需要处理
     * // TODO：删除或统一行为
     *
     * @param ids 店铺ID列表，逗号分隔
     */
    @PostMapping("/batch/remove")
    @Token(tokenType = {TokenType.S})
    @ResponseBody
    public Response<?> removeBatch(@NonNull String ids) {
        int count = shopService.deleteShopByIds(ids);
        if (count > 0) {
            return new Response<>(OK, SUCCESS);
        }
        return new Response<>(ERROR, FAILURE);
    }
}
