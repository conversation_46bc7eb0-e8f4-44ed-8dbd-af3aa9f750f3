package com.api.controller;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.api.config.Token;
import com.api.validator.UserTicketValidator;
import com.common.bean.Response;
import com.common.constant.App;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.constant.ProductStage;
import com.common.constant.TicketPriceCatalog;
import com.common.constant.TicketStage;
import com.common.constant.TokenType;
import com.common.constant.UserTicketCatalog;
import com.common.constant.UserTicketStage;
import com.common.util.DateUtil;
import com.domain.UserTicket;
import com.dto.ShopConfigDTO;
import com.dto.TicketDTO;
import com.dto.UserDTO;
import com.dto.UserTicketDTO;
import com.github.pagehelper.PageInfo;
import com.query.ShopConfigQuery;
import com.query.UserTicketQuery;
import com.service.ShopConfigService;
import com.service.TicketService;
import com.service.UserService;
import com.service.UserTicketService;

/**
 * 用户门票管理
 *
 * <AUTHOR>
 * @date 2025-09-14
 */
@RestController
public class UserTicketController extends BaseController {

    @Autowired
    private UserTicketService userTicketService;
    @Autowired
    private UserService userService;
    @Autowired
    private TicketService ticketService;
    @Autowired
    private ShopConfigService shopConfigService;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 增加用户门票
     */
    @RequestMapping(value = "/v1/user/ticket/create", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<?> create(@RequestBody UserTicketQuery request) {
        UserTicketValidator validator = new UserTicketValidator();
        if (!validator.onShopId(request.getShopId()).onUserId(request.getUserId())
                .onTicketId(request.getTicketId()).onApplyTime(request.getApplyTimeStr()).result()) {
            return new Response<>(ERROR, validator.getErrorMessage());
        }
        Lock lock = this.getLock(redisTemplate, CacheKey.LOCK_USER_TICKET, this.getUserToken().getId().toString());
        if (!lock.tryLock()) {
            return new Response<>(ERROR, "请勿重复提交");
        }
        try {
            Date datetime = this.getServerTime();
            // 分隔时间节点
            Date splitTime = this.getDatetime(DateUtil.format(datetime, DATE_FORMAT) + " " + App.USER_TICKET_SPLIT_TIME, DATETIME_FORMAT);
            // 以当前时间的中午十二点为界限，当日中午十二点前生成的是前一日，当日中午十二点后生成的是当日
            Date applyTime = DateUtil.parse(request.getApplyTimeStr(), DATE_FORMAT);
            if (datetime.before(splitTime)) {
                // 判断报名时间是否是前一天，如果不是，则不允许添加
                if (!DateUtil.isSameDate(applyTime, DateUtil.getFutureDay(datetime, -1))) {
                    return new Response<>(ERROR, "报名时间请选择前一天");
                }
            } else {
                // 判断报名时间是否是前一天，如果不是，则不允许添加
                if (!DateUtil.isSameDate(applyTime, datetime)) {
                    return new Response<>(ERROR, "报名时间只能是当天");
                }
            }
            UserDTO userDTO = userService.findById(request.getUserId());
            if (userDTO == null || DataStatus.N.getCode().equals(userDTO.getStatus())) {
                return new Response<>(ERROR, "用户不存在,请核对零卡ID是否有误");
            }
            // 检查门票是否可用
            TicketDTO ticketDTO = ticketService.findById(request.getTicketId());
            if (ticketDTO == null || !DataStatus.Y.getCode().equals(ticketDTO.getStatus())) {
                return new Response<>(ERROR, "门票不存在");
            }
            // 判断门票是否属于该店铺
            if (!ticketDTO.getShopId().equals(request.getShopId())) {
                return new Response<>(ERROR, "门票不属于该店铺，无法添加");
            }
            if (!ProductStage.UP.getCode().equals(ticketDTO.getStage())) {
                return new Response<>(ERROR, "门票已下架");
            }
            UserTicket userTicket = new UserTicket();
            userTicket.setUserId(request.getUserId());
            userTicket.setShopId(request.getShopId());
            userTicket.setTicketId(request.getTicketId());
            userTicket.setCatalog(UserTicketCatalog.SELF_BUY.getCode());
            userTicket.setApplyTime(applyTime);
            userTicket.setStage(UserTicketStage.VERIFIED.getCode());
            userTicket.setStatus(DataStatus.Y.getCode());
            userTicket.setModifyTime(datetime);
            userTicket.setCreateTime(datetime);
            userTicketService.create(userTicket);
            return new Response<>(OK, SUCCESS);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new Response<>(ERROR, FAILURE);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 分页查询用户门票列表
     */
    @RequestMapping(value = "/v1/user/ticket/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<PageInfo<UserTicketDTO>> query(@RequestBody UserTicketQuery userTicketQuery) {
        userTicketQuery.setStatus(DataStatus.Y.getCode());
        PageInfo<UserTicketDTO> pageInfo = userTicketService.find(userTicketQuery);
        if (pageInfo.getList() == null || pageInfo.getList().isEmpty()){
            return new Response<>(pageInfo);
        }
        List<Long> ticketIds = new ArrayList<>();
        List<Long> shopIds = new ArrayList<>();
        List<Long> userIds = new ArrayList<>();
        for (UserTicketDTO userTicketDTO : pageInfo.getList()) {
            ticketIds.add(userTicketDTO.getTicketId());
            shopIds.add(userTicketDTO.getShopId());
            userIds.add(userTicketDTO.getUserId());
        }
        Map<Long, TicketDTO> ticketDTOMap = ticketService.findMapByIds(ticketIds);
        Map<Long, UserDTO> userDTOMap = userService.findMapByIds(userIds);
        Map<Long, ShopConfigDTO> shopConfigDTOMap = new HashMap<>();
        if (!shopIds.isEmpty()) {
            ShopConfigQuery shopConfigQuery = new ShopConfigQuery();
            shopConfigQuery.setShopIds(shopIds);
            shopConfigQuery.setStatus(DataStatus.Y.getCode());
            List<ShopConfigDTO> shopConfigDTOS = shopConfigService.findAll(shopConfigQuery);
            for (ShopConfigDTO shopConfigDTO : shopConfigDTOS) {
                shopConfigDTOMap.put(shopConfigDTO.getShopId(),shopConfigDTO);
            }
        }
        // 循环返回处理数据
        for (UserTicketDTO userTicketDTO : pageInfo.getList()) {
            if (ticketDTOMap.containsKey(userTicketDTO.getTicketId())) {
                TicketDTO ticketDTO = ticketDTOMap.get(userTicketDTO.getTicketId());
                ticketDTO.setPriceCatalogName(TicketPriceCatalog.getName(ticketDTO.getPriceCatalog()));
                ticketDTO.setStageName(TicketStage.getName(ticketDTO.getStage()));
                userTicketDTO.setTicket(ticketDTO);
            }
            if (!isEmpty(userTicketDTO.getApplyTime())) {
                userTicketDTO.setApplyTimeStr(DateUtil.format(userTicketDTO.getApplyTime(), DATE_FORMAT));
            }
            // 返回店铺名字
            if (shopConfigDTOMap.containsKey(userTicketDTO.getShopId())) {
                userTicketDTO.setShopName(shopConfigDTOMap.get(userTicketDTO.getShopId()).getName());
            }
            userTicketDTO.setStageName(UserTicketStage.getName(userTicketDTO.getStage()));
            userTicketDTO.setCatalogName(UserTicketCatalog.getName(userTicketDTO.getCatalog()));
            // 返回用户信息
            if (userDTOMap.containsKey(userTicketDTO.getUserId())){
                UserDTO userDTO = userDTOMap.get(userTicketDTO.getUserId());
                userDTO.setOpenid(null);
                userTicketDTO.setUser(userDTO);
            }
        }
        return new Response<>(pageInfo);
    }


    /**
     * 分页查询我的门票列表
     */
    @RequestMapping(value = "/v1/user/ticket/my/query", method = RequestMethod.POST)
    @Token(tokenType = {TokenType.C})
    public Response<PageInfo<UserTicketDTO>> queryMy(@RequestBody UserTicketQuery userTicketQuery) {
        userTicketQuery.setUserId(this.getUserToken().getId());
        return this.query(userTicketQuery);
    }

//    /**
//     * 修改用户门票
//     */
//    @RequestMapping(value = "/v1/user/ticket/modify", method = RequestMethod.POST)
//    public Response<?> modify(@RequestBody UserTicket userTicket) {
//        Date datetime = this.getServerTime();
//        userTicket.setModifyTime(datetime);
//        userTicketService.modifyById(userTicket);
//        return new Response<>(OK, SUCCESS);
//    }
//
//    /**
//     * 删除用户门票
//     */
//    @RequestMapping(value = "/v1/user/ticket/remove", method = RequestMethod.POST)
//    public Response<?> remove(@RequestBody UserTicketQuery request) {
//        Date datetime = this.getServerTime();
//        UserTicket userTicket = new UserTicket();
//        userTicket.setId(request.getId());
//        userTicket.setStatus(DataStatus.N.getCode());
//        userTicket.setModifyTime(datetime);
//        userTicketService.modifyById(userTicket);
//        return new Response<>(OK, SUCCESS);
//    }


}
