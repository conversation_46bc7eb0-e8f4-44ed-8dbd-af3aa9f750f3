# 商品数据库设计

## **基础商品 + 动态属性**

思路是：

* 只有 **核心维度**（如容量）存成 SKU。
* 其他选项（冰度、热/冷、加料）作为 **可选属性**，在点单时存储。

### 数据库设计示例

#### 商品表 (product)

| id | name        | description | photo   | group_id | status      |
|----|-------------|-------------|---------|----------|-------------|
| 1  | 威士忌      | 简短的介绍   | 图片序列 | 1        | available(在售)   |
| 2  | 干邑        | 简短的介绍   | 图片序列 | 1        | sold_out(售罄)    |
| 3  | 冰美式咖啡   | 同上        | 同上     | 2        | removed(下架)     |
| 4  | 蛋糕        | 同上        | 同上     | 3        | available         |

#### SKU表 (product\_sku)

（只放影响价格/库存的重要属性，比如容量）

| id | product_id | size | unit | price |
|----|------------|------|------|-------|
| 1  | 1          | 30   | ml   | 50    |
| 2  | 1          | 60   | ml   | 90    |
| 3  | 2          | 100  | ml   | 20    |
| 4  | 2          | 200  | ml   | 30    |
| 5  | 4          | 100  | g    | 30    |

#### product\_option\_group（选项组表）

| id | product_id | name   | type (单选或多选) | required (是否必选) |
|----|------------|--------|------------------|---------------------|
| 1  | 2          | 冰度   | single           | true                |
| 2  | 2          | 温度   | single           | true                |
| 3  | 1          | 加料   | multi            | false               |

#### product\_option\_value（选项值表）

| id | option_group_id | value     | extra_price |
|----|-----------------|-----------|-------------|
| 1  | 1               | 普通冰    | 0            |
| 2  | 1               | 少冰      | 0            |
| 3  | 1               | 去冰      | 0            |
| 4  | 2               | 冰        | 0            |
| 5  | 2               | 热        | 0            |
| 6  | 3               | 柠檬片    | 2            |
| 7  | 3               | 苏打水    | 5            |

#### 订单项 (order\_item)

下单时，保存用户选择的 SKU + 选项 JSON, 在 `selected_options` 保存 ID

| id | order_id | sku_id | quantity | selected_options                                                        |
|---|----------|--------|----------|--------------------------------------------------------------------------|
| 1  | 101      | 2      | 1        | [{"option_group_id":1,"value_id":2},{"option_group_id":2,"value_id":5}] |
| 2  | 101      | 1      | 2        | [{"option_group_id":1,"value_id":3}]                                    |

* 价格计算时：`sku.price + sum(option.extra_price)`。
* 如果某个选项不影响价格，`extra_price` 就是 0。
* 能支持 **单选、多选**，也能方便扩展。

---

## 3. **类似奶茶点餐的「可配置项系统」**

* 「单选属性」：冰度、温度。
* 「多选属性」：加料（柠檬、苏打水…）。
* 「价格调整」：每个选项可以设置加价/减价。

## 返回给前端的数据

### 商品列表

```json
[
  {
    "product_id": 1,
    "name": "威士忌",
    "price": 50,
    "photo": "",
    "group_id": 1,
    "status": "available"
  },
  {
    "product_id": 2,
    "name": "干邑",
    "price": 60,
    "photo": "",
    "group_id": 1,
    "status": "sold_out"
  }
]
```

`price` 为所有 sku 中最低价，`photo` 为图片组中第一张，`group_id` 为商品分组 ID，未出现在上述数据库设计中。

### 商品详情

```json
{
  "id": 1,
  "name": "威士忌",
  "description": "一种由谷物发酵而成的烈酒",
  "status": "available",
  "photo": [
    "https://example.com/whisky.jpg",
    "https://example.com/whisky2.jpg"
  ],
  "sku": [
    {
      "id": 1,
      "size": "100",
      "unit": "ml",
      "price": 50
    }
  ],
  "option": [
    {
      "group_id": 1,
      "name": "冰",
      "type": "single",
      "required": true,
      "values": [
        {
          "id": 1,
          "value": "普通冰",
          "extra_price": 0
        },
        {
          "id": 2,
          "value": "少冰",
          "extra_price": 0
        },
        {
          "id": 3,
          "value": "去冰",
          "extra_price": 0
        }
      ]
    },{
        "group_id": 2,
        "name": "小料",
        "type": "multiple",
        "required": false,
        "values": [
          {
            "id": 1,
            "value": "柠檬片",
            "extra_price": 5
          },
          {
            "id": 2,
            "value": "薄荷叶",
            "extra_price": 1
          }
        ]
    }
  ]
}
```

### 加入购物车请求

```json
{
  "uid": 1,
  "table_id": 1,
  "item": [
    {
      "sku_id": 1,
      "quantity": 2,
      "options": [
        { "option_group_id": 1, "value_id": 2 },
        { "option_group_id": 2, "value_id": 5 }
      ]
    }
  ]
}
```

需要返回价格以及门票优惠消耗情况（用于最终下单前预览）
