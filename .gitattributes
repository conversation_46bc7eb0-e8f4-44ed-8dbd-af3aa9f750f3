*               text=auto eol=lf

# Java sources
*.java          text diff=java
*.kt            text diff=kotlin
*.groovy        text diff=java
*.scala         text diff=java
*.gradle        text diff=java
*.gradle.kts    text diff=kotlin

# These files are text and should be normalized (Convert crlf => lf)
*.css           text diff=css
*.scss          text diff=css
*.sass          text
*.df            text
*.htm           text diff=html
*.html          text diff=html
*.js            text
*.mjs           text
*.cjs           text
*.jsp           text
*.jspf          text
*.jspx          text
*.properties    text
*.tld           text
*.tag           text
*.tagx          text
*.xml           text

# These files are binary and should be left untouched
# (binary is a macro for -text -diff)
*.class         binary
*.dll           binary
*.ear           binary
*.jar           binary
*.so            binary
*.war           binary
*.jks           binary

# Common build-tool wrapper scripts ('.cmd' versions are handled by 'Common.gitattributes')
mvnw            text eol=lf
gradlew         text eol=lf

# These are explicitly windows files and should use crlf
*.bat           text eol=crlf
