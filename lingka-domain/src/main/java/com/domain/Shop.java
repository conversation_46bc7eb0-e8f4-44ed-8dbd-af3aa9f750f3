package com.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

public class Shop extends Base {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 拥有者用户ID
     */
    private Long ownerUserId;

    /**
     * 店铺拥有者姓名
     */
    private String ownerName;

    /**
     * 店铺拥有者手机号
     */
    private String ownerMobile;

    /**
     * 店铺拥有者身份证号码
     */
    private String ownerIdNumber;

    /**
     * 店铺拥有者身份证正反面照片
     */
    private String ownerIdNumberPhoto;

    /**
     * 责任承诺函文件
     */
    private String responsibilityCommitment;

    /**
     * 管理关系声明文件
     */
    private String manageRelationship;

    /**
     * 营业执照文件
     */
    private String businessLicense;

    /**
     * 食品经营许可证文件
     */
    private String foodBusinessLicense;

    /**
     * 酒类流通许可证文件
     */
    private String liquorLicense;

    /**
     * 健康证文件(多张用,分割)
     */
    private String healthCertificates;

    /**
     * 消防证文件
     */
    private String fireLicense;

    /**
     * 添加人
     */
    private Long createAdminId;

    /**
     * 店铺状态 (0:启用 1:禁用)
     */
    private String shopStatus;

    /**
     * 数据记录状态 (0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public Shop() {
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setOwnerUserId(Long ownerUserId) {
        this.ownerUserId = ownerUserId;
    }

    public Long getOwnerUserId() {
        return ownerUserId;
    }

    public void setOwnerName(String ownerName) {
        this.ownerName = ownerName;
    }

    public String getOwnerName() {
        return ownerName;
    }

    public void setOwnerMobile(String ownerMobile) {
        this.ownerMobile = ownerMobile;
    }

    public String getOwnerMobile() {
        return ownerMobile;
    }

    public void setOwnerIdNumber(String ownerIdNumber) {
        this.ownerIdNumber = ownerIdNumber;
    }

    public String getOwnerIdNumber() {
        return ownerIdNumber;
    }

    public void setOwnerIdNumberPhoto(String ownerIdNumberPhoto) {
        this.ownerIdNumberPhoto = ownerIdNumberPhoto;
    }

    public String getOwnerIdNumberPhoto() {
        return ownerIdNumberPhoto;
    }

    public void setResponsibilityCommitment(String responsibilityCommitment) {
        this.responsibilityCommitment = responsibilityCommitment;
    }

    public String getResponsibilityCommitment() {
        return responsibilityCommitment;
    }

    public void setManageRelationship(String manageRelationship) {
        this.manageRelationship = manageRelationship;
    }

    public String getManageRelationship() {
        return manageRelationship;
    }

    public void setBusinessLicense(String businessLicense) {
        this.businessLicense = businessLicense;
    }

    public String getBusinessLicense() {
        return businessLicense;
    }

    public void setFoodBusinessLicense(String foodBusinessLicense) {
        this.foodBusinessLicense = foodBusinessLicense;
    }

    public String getFoodBusinessLicense() {
        return foodBusinessLicense;
    }

    public void setLiquorLicense(String liquorLicense) {
        this.liquorLicense = liquorLicense;
    }

    public String getLiquorLicense() {
        return liquorLicense;
    }

    public void setHealthCertificates(String healthCertificates) {
        this.healthCertificates = healthCertificates;
    }

    public String getHealthCertificates() {
        return healthCertificates;
    }

    public void setFireLicense(String fireLicense) {
        this.fireLicense = fireLicense;
    }

    public String getFireLicense() {
        return fireLicense;
    }

    public void setCreateAdminId(Long createAdminId) {
        this.createAdminId = createAdminId;
    }

    public Long getCreateAdminId() {
        return createAdminId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(String shopStatus) {
        this.shopStatus = shopStatus;
    }
}
