package com.domain;

import java.util.Date;

import com.common.constant.DataStatus;
import com.common.constant.ShopConfigModifyAudit;
import com.dto.ShopConfigModifyApplyDTO;
import com.query.ShopConfigQuery;

/**
 * 店铺配置修改申请对象 tb_shop_config_modify_apply
 *
 * <AUTHOR>
 */

public class ShopConfigModifyApply extends Base {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 店铺介绍头图文件
     */
    private String photo;

    /**
     * 店铺介绍描述
     */
    private String description;

    /**
     * 审核状态(0:驳回 1:审核中 2:通过 3:修改内容自动作废)
     */
    private String audit;

    /**
     * 审核人
     */
    private Long auditAdminId;

    /**
     * 状态(0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public ShopConfigModifyApply(ShopConfigModifyApplyDTO dto) {
        if (dto != null) {
            this.id = dto.getId();
            this.shopId = dto.getShopId();
            this.name = dto.getName();
            this.photo = dto.getPhoto();
            this.description = dto.getDescription();
            this.audit = dto.getAudit();
            this.auditAdminId = dto.getAuditAdminId();
            this.status = dto.getStatus();
            this.modifyTime = dto.getModifyTime();
            this.createTime = dto.getCreateTime();
        }
    }

    public ShopConfigModifyApply(ShopConfig shopConfig) {
        if (shopConfig != null) {
            this.id = shopConfig.getId();
            this.shopId = shopConfig.getShopId();
            this.name = shopConfig.getName();
            this.photo = shopConfig.getPhoto();
            this.description = shopConfig.getDescription();
            this.audit = ShopConfigModifyAudit.PENDING.getCode();
            this.status = DataStatus.Y.getCode();
        }
    }

    public ShopConfigModifyApply(ShopConfigQuery query) {
        if (query != null) {
            this.id = query.getId();
            this.shopId = query.getShopId();
            this.name = query.getName();
            this.photo = query.getPhoto();
            this.description = query.getDescription();
            this.audit = ShopConfigModifyAudit.PENDING.getCode();
            this.status = DataStatus.Y.getCode();
        }
    }

    public ShopConfigModifyApply() {
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPhoto() {
        return photo;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setAudit(String audit) {
        this.audit = audit;
    }

    public String getAudit() {
        return audit;
    }

    public void setAuditAdminId(Long auditAdminId) {
        this.auditAdminId = auditAdminId;
    }

    public Long getAuditAdminId() {
        return auditAdminId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
