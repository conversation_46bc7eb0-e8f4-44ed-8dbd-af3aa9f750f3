package com.domain;

import java.util.Date;

import com.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.query.UserQuery;

/**
 * 用户对象 tb_user
 *
 * <AUTHOR>
 * @date 2025-08-03
 */

public class User extends Base {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 性别(0:男 1:女)
     */
    private String gender;

    /**
     * 微信小程序openid
     */
    private String openid;

    /**
     * 个人简介
     */
    private String introduce;

    /**
     * 个人照片(多个用逗号分隔)
     */
    private String photos;

    /**
     * 状态(0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public User(UserQuery query) {
        if (query != null) {
            this.id = query.getId();
            this.mobile = query.getMobile();
            this.nickname = query.getNickname();
            this.avatar = query.getAvatar();
            this.gender = query.getGender();
            this.openid = query.getOpenid();
            this.introduce = query.getIntroduce();
            this.photos = query.getPhotos();
            this.status = query.getStatus();
            this.modifyTime = query.getModifyTime();
            this.createTime = query.getCreateTime();
        }
    }

    public User(UserDTO dto) {
        if (dto != null) {
            this.id = dto.getId();
            this.mobile = dto.getMobile();
            this.nickname = dto.getNickname();
            this.avatar = dto.getAvatar();
            this.gender = dto.getGender();
            this.openid = dto.getOpenid();
            this.introduce = dto.getIntroduce();
            this.photos = dto.getPhotos();
            this.status = dto.getStatus();
            this.modifyTime = dto.getModifyTime();
            this.createTime = dto.getCreateTime();
        }
    }

    public User() {
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobile() {
        return mobile;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getNickname() {
        return nickname;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGender() {
        return gender;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getOpenid() {
        return openid;
    }

    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }

    public String getIntroduce() {
        return introduce;
    }

    public void setPhotos(String photos) {
        this.photos = photos;
    }

    public String getPhotos() {
        return photos;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
