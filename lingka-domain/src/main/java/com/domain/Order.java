package com.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 订单对象 tb_order
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */

public class Order extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 用户ID */
    private Long userId;

    /** 桌子ID */
    private Long tableId;

    /** 订单号 */
    private String code;

    /** 类型(ticket:门票 product:普通点单 diy:DIY) */
    private String type;

    /** 金额 */
    private BigDecimal amount;

    /** 订单状态 */
    private String orderStage;

    /** 二级订单状态 */
    private String subOrderStage;

    /** 支付状态 */
    private String payStage;

    /** 下单时间 */
    private Date orderTime;

    /** 支付时间 */
    private Date payTime;

    /** 用户备注 */
    private String userComment;

    /** 用户订单状态(0:正常 1:无效) */
    private String userStatus;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }
    public void setTableId(Long tableId) {
        this.tableId = tableId;
    }

    public Long getTableId() {
        return tableId;
    }
    public void setCode(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getAmount() {
        return amount;
    }
    public void setOrderStage(String orderStage) {
        this.orderStage = orderStage;
    }

    public String getOrderStage() {
        return orderStage;
    }
    public void setPayStage(String payStage) {
        this.payStage = payStage;
    }

    public String getPayStage() {
        return payStage;
    }
    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getOrderTime() {
        return orderTime;
    }
    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Date getPayTime() {
        return payTime;
    }
    public void setUserComment(String userComment) {
        this.userComment = userComment;
    }

    public String getUserComment() {
        return userComment;
    }
    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getUserStatus() {
        return userStatus;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
