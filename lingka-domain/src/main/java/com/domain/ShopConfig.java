package com.domain;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.common.constant.App;
import com.dto.ShopConfigDTO;
import com.query.ShopConfigQuery;

/**
 * 店铺配置对象 tb_shop_config
 *
 * <AUTHOR>
 */

public class ShopConfig extends Base {

    private static final long serialVersionUID = 2181072777359123514L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺 ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String name;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 店铺联系方式
     */
    private String phone;

    /**
     * 店铺介绍头图文件
     */
    private String photo;

    /**
     * 店铺介绍描述
     */
    private String description;

    /**
     * 店铺标签
     */
    private String tags;

    /**
     * 是否对外展示 (0: 展示 1: 隐藏)
     */
    private String displaySwitch;

    /**
     * 营业状态(open:营业 close:暂停营业 close_manual:暂停营业(手动))
     */
    private String state;

    /**
     * 营业时间
     */
    private List<ShopOpenCloseTime> shippingTimeList;

    /**
     * 省编号
     */
    private Long provinceId;

    /**
     * 市编号
     */
    private Long cityId;

    /**
     * 区编号
     */
    private Long distinctId;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 状态(0:正常 1:无效)
     */
    private String status;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建时间
     */
    private Date createTime;

    public ShopConfig(ShopConfigModifyApply apply) {
        if (apply != null) {
            this.shopId = apply.getShopId();
            this.name = apply.getName();
            this.photo = apply.getPhoto();
            this.description = apply.getDescription();
        }
    }

    public ShopConfig() {
    }

    public ShopConfig(ShopConfigDTO dto) {
        if (dto != null) {
            this.id = dto.getId();
            this.shopId = dto.getShopId();
            this.name = dto.getName();
            this.logo = dto.getLogo();
            this.phone = dto.getPhone();
            this.photo = dto.getPhoto() != null ? String.join(App.COMMA, dto.getPhoto()) : null;
            this.description = dto.getDescription();
            this.tags = dto.getTags() != null ? String.join(App.COMMA, dto.getTags()) : null;
            this.displaySwitch = dto.getDisplaySwitch();
            this.state = dto.getState();
            this.shippingTimeList = dto.getShippingTimeList() != null
                    ? dto.getShippingTimeList().stream()
                    .map(d -> new ShopOpenCloseTime(d.getDay(), d.getOpenTime(), d.getCloseTime()))
                    .collect(Collectors.toList())
                    : null;
            this.provinceId = dto.getProvinceId();
            this.cityId = dto.getCityId();
            this.distinctId = dto.getDistinctId();
            this.address = dto.getAddress();
            this.latitude = dto.getLatitude();
            this.longitude = dto.getLongitude();
            this.status = dto.getStatus();
            this.modifyTime = dto.getModifyTime();
            this.createTime = dto.getCreateTime();
        }
    }

    public ShopConfig(ShopConfigQuery query) {
        if (query != null) {
            this.id = query.getId();
            this.shopId = query.getShopId();
            this.name = query.getName();
            this.logo = query.getLogo();
            this.phone = query.getPhone();
            this.photo = query.getPhoto();
            this.description = query.getDescription();
            this.tags = query.getTags();
            this.displaySwitch = query.getDisplaySwitch();
            this.state = query.getState();
            this.shippingTimeList = query.getShippingTimeList() != null
                    ? query.getShippingTimeList().stream().map(d -> new ShopOpenCloseTime(d.getDay(), d.getOpenTime(), d.getCloseTime()))
                    .collect(Collectors.toList())
                    : null;
            this.provinceId = query.getProvinceId();
            this.cityId = query.getCityId();
            this.distinctId = query.getDistinctId();
            this.address = query.getAddress();
            this.latitude = query.getLatitude();
            this.longitude = query.getLongitude();
            this.status = query.getStatus();
            this.modifyTime = query.getModifyTime();
            this.createTime = query.getCreateTime();
        }
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPhoto() {
        return photo;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getTags() {
        return tags;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getState() {
        return state;
    }

    public void setProvinceId(Long provinceId) {
        this.provinceId = provinceId;
    }

    public Long getProvinceId() {
        return provinceId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public Long getCityId() {
        return cityId;
    }

    public void setDistinctId(Long distinctId) {
        this.distinctId = distinctId;
    }

    public Long getDistinctId() {
        return distinctId;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddress() {
        return address;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getDisplaySwitch() {
        return displaySwitch;
    }

    public void setDisplaySwitch(String displaySwitch) {
        this.displaySwitch = displaySwitch;
    }

    public List<ShopOpenCloseTime> getShippingTimeList() {
        return shippingTimeList;
    }

    public void setShippingTimeList(List<ShopOpenCloseTime> shippingTimeList) {
        this.shippingTimeList = shippingTimeList;
    }
}
