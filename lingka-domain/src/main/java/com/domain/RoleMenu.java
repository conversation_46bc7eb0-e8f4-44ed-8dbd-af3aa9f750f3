package com.domain;

import java.util.Date;

/**
 * 角色菜单关联对象 tb_role_menu
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */

public class RoleMenu extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 角色ID */
    private Long roleId;

    /** 菜单ID */
    private Long menuId;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    public Long getRoleId() {
        return roleId;
    }
    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }

    public Long getMenuId() {
        return menuId;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
