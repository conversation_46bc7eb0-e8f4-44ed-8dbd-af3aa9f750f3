package com.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户门票转赠记录对象 tb_user_ticket_gift_record
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */

public class UserTicketGiftRecord extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户门票ID */
    private Long userTicketId;

    /** 转赠人ID */
    private Long sourceUserId;

    /** 被转赠人ID */
    private Long targetUserId;

    /** 领取状态(0:已领取 1:未领取) */
    private String stage;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setUserTicketId(Long userTicketId) {
        this.userTicketId = userTicketId;
    }

    public Long getUserTicketId() {
        return userTicketId;
    }
    public void setSourceUserId(Long sourceUserId) {
        this.sourceUserId = sourceUserId;
    }

    public Long getSourceUserId() {
        return sourceUserId;
    }
    public void setTargetUserId(Long targetUserId) {
        this.targetUserId = targetUserId;
    }

    public Long getTargetUserId() {
        return targetUserId;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public String getStage() {
        return stage;
    }

    public void setStage(String stage) {
        this.stage = stage;
    }
}
