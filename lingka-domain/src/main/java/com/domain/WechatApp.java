package com.domain;

import java.util.Date;

/**
 * 微信应用对象 tb_wechat_app
 * 
 * <AUTHOR>
 * @date 2023-07-21
 */

public class WechatApp extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 应用ID */
    private String appid;

    /** 应用密钥 */
    private String secret;

    /** 应用名称 */
    private String name;

    /** 访问凭证 */
    private String accessToken;

    /** 凭证刷新日期 */
    private Date accessTokenDatetime;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getAppid() {
        return appid;
    }
    public void setSecret(String secret) {
        this.secret = secret;
    }

    public String getSecret() {
        return secret;
    }
    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getAccessToken() {
        return accessToken;
    }
    public void setAccessTokenDatetime(Date accessTokenDatetime) {
        this.accessTokenDatetime = accessTokenDatetime;
    }

    public Date getAccessTokenDatetime() {
        return accessTokenDatetime;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
