package com.domain;

import java.util.Date;

/**
 * 产品小料配置对象 tb_product_ingredient_config
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

public class ProductIngredientConfig extends Base {

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 产品ID */
    private Long productId;

    /** 类型(single:单选 multi:多选) */
    private String type;

    /** 是否必选(0:必选 1:非必选) */
    private String required;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    private Date modifyTime;

    /** 创建时间 */
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductId() {
        return productId;
    }
    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }
    public void setRequired(String required) {
        this.required = required;
    }

    public String getRequired() {
        return required;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
