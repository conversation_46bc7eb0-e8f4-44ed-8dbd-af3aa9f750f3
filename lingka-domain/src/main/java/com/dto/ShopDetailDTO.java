package com.dto;

import java.util.List;

import com.common.bean.Bean;

public class ShopDetailDTO extends Bean {
    private static final long serialVersionUID = -6258165107943754575L;
    private ShopDTO shop;
    private ShopConfigDTO config;
    private List<UserRoleDTO> roleList;

    public ShopDetailDTO(ShopDTO shopDTO, ShopConfigWithUserRoleDTO configDTO) {
        this.shop = shopDTO;
        if (configDTO != null) {
            this.config = configDTO.getShopConfig();
            this.roleList = configDTO.getRoleList();
        }
    }

    public ShopDTO getShop() {
        return shop;
    }

    public void setShop(ShopDTO shop) {
        this.shop = shop;
    }

    public ShopConfigDTO getConfig() {
        return config;
    }

    public void setConfig(ShopConfigDTO config) {
        this.config = config;
    }

    public List<UserRoleDTO> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<UserRoleDTO> roleList) {
        this.roleList = roleList;
    }
}
