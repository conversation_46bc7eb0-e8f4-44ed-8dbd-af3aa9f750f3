package com.dto;

import com.common.bean.Bean;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 产品选项对象 tb_product_option
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

public class ProductOptionDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 产品ID */
    private Long productId;

    /** 键 */
    private String key;

    /** 值 */
    private String value;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductId() {
        return productId;
    }
    public void setKey(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
    public void setValue(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
