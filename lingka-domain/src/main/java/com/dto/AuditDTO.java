package com.dto;

import com.common.bean.Bean;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 审核结果记录对象 tb_audit
 *
 * <AUTHOR>
 */

public class AuditDTO extends Bean {

    private static final long serialVersionUID = 1L;

    /**
     * 审核结果记录 ID，主键
     */
    private Long id;

    /**
     * 修改条目 ID，为其他修改表主键
     */
    private Long modifyId;

    /**
     * 审核任务id
     */
    private String traceId;

    /**
     * 用户 ID
     */
    private Long userId;

    /**
     * 审核类型：shop_text: 店铺文本配置，shop_media: 店铺媒体配置，user_text: 用户文本资料，user_media: 用户媒体资料
     */
    private String type;

    /**
     * 审核建议，risky：风险 pass：通过 review：人工审核
     */
    private String suggest;

    /**
     * 命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他
     */
    private String label;

    /**
     * 记录状态 (0: 正常 1: 无效)
     */
    private String status;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setModifyId(Long modifyId) {
        this.modifyId = modifyId;
    }

    public Long getModifyId() {
        return modifyId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getTraceId() {
        return traceId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setSuggest(String suggest) {
        this.suggest = suggest;
    }

    public String getSuggest() {
        return suggest;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
