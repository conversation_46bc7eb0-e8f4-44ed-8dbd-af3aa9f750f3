package com.dto;

import java.util.List;

import com.common.bean.Bean;

/**
 * 用户购物车对象 tb_user_cart
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */

public class CartDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 总价 */
    private String totalPrice;

    /** 减免价 */
    private String reducedPrice;

    /** 最终价 */
    private String finalPrice;

    /** 列表 */
    private List<UserCartDTO> userCarts;

    public String getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(String totalPrice) {
        this.totalPrice = totalPrice;
    }

    public List<UserCartDTO> getUserCarts() {
        return userCarts;
    }

    public void setUserCarts(List<UserCartDTO> userCarts) {
        this.userCarts = userCarts;
    }

    public String getReducedPrice() {
        return reducedPrice;
    }

    public void setReducedPrice(String reducedPrice) {
        this.reducedPrice = reducedPrice;
    }

    public String getFinalPrice() {
        return finalPrice;
    }

    public void setFinalPrice(String finalPrice) {
        this.finalPrice = finalPrice;
    }
}
