package com.dto;

import com.common.bean.Bean;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户门票报名对象 tb_user_ticket_apply
 * 
 * <AUTHOR>
 * @date 2025-09-16
 */

public class UserTicketApplyDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 用户ID */
    private Long userId;

    /** 店铺ID */
    private Long shopId;

    /** 订单ID */
    private Long orderId;

    private List<OrderTicketDetailDTO> orderTicketDetailList;

    /** 用户信息 */
    private UserDTO user;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date applyTime;

    /** 照片(多张用逗号分隔) */
    private String photo;

    /** 照片 */
    private List<String> photoList;

    /** 用户备注 */
    private String userComment;

    /** 阶段(0:待审核 1:已通过 2:已拒绝 3:已取消) */
    private String stage;

    /** 阶段 */
    private String stageName;

    /** 审核备注 */
    private String comment;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getOrderId() {
        return orderId;
    }
    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() {
        return applyTime;
    }
    public void setPhoto(String photo) {
        this.photo = photo;
    }

    public String getPhoto() {
        return photo;
    }
    public void setUserComment(String userComment) {
        this.userComment = userComment;
    }

    public String getUserComment() {
        return userComment;
    }
    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getStage() {
        return stage;
    }
    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getComment() {
        return comment;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public List<OrderTicketDetailDTO> getOrderTicketDetailList() {
        return orderTicketDetailList;
    }

    public void setOrderTicketDetailList(List<OrderTicketDetailDTO> orderTicketDetailList) {
        this.orderTicketDetailList = orderTicketDetailList;
    }

    public String getStageName() {
        return stageName;
    }

    public void setStageName(String stageName) {
        this.stageName = stageName;
    }

    public List<String> getPhotoList() {
        return photoList;
    }

    public void setPhotoList(List<String> photoList) {
        this.photoList = photoList;
    }

    public UserDTO getUser() {
        return user;
    }

    public void setUser(UserDTO user) {
        this.user = user;
    }
}
