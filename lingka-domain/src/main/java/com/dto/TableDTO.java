package com.dto;

import com.common.bean.Bean;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 桌子对象 tb_table
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */

public class TableDTO extends Bean{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 桌子名称 */
    private String tableName;

    /** 人数 */
    private Long personNumber;

    /** 点单模式 */
    private String orderMode;

    /** 小程序二维码图片地址 */
    private String qrCode;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getTableName() {
        return tableName;
    }
    public void setPersonNumber(Long personNumber) {
        this.personNumber = personNumber;
    }

    public Long getPersonNumber() {
        return personNumber;
    }
    public void setOrderMode(String orderMode) {
        this.orderMode = orderMode;
    }

    public String getOrderMode() {
        return orderMode;
    }
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getQrCode() {
        return qrCode;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
