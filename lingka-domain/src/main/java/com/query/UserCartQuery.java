package com.query;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 用户购物车对象 tb_user_cart
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */

public class UserCartQuery extends Page{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 主键 */
    private List<Long> ids;

    /** 用户ID */
    private Long userId;

    /** 店铺ID */
    private Long shopId;

    /** 产品ID */
    private Long productId;

    /** 产品IDS */
    private List<Long> productIds;

    /** 产品SKU ID */
    private Long productSkuId;

    /** 产品选项IDS */
    private String productOptionIds;

    /** 产品选项IDS */
    private List<Long> productOptionIdList;

    /** 产品小料IDS */
    private String productIngredientIds;

    /** 产品小料IDS */
    private List<Long> productIngredientIdList;

    /** 数量 */
    private Integer number;

    /** 用户购物车状态(0:有效 1:无效) */
    private String userCartStatus;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getUserId() {
        return userId;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getProductId() {
        return productId;
    }
    public void setProductSkuId(Long productSkuId) {
        this.productSkuId = productSkuId;
    }

    public Long getProductSkuId() {
        return productSkuId;
    }
    public void setProductOptionIds(String productOptionIds) {
        this.productOptionIds = productOptionIds;
    }

    public String getProductOptionIds() {
        return productOptionIds;
    }
    public void setProductIngredientIds(String productIngredientIds) {
        this.productIngredientIds = productIngredientIds;
    }

    public String getProductIngredientIds() {
        return productIngredientIds;
    }
    public void setNumber(Integer number) {
        this.number = number;
    }

    public Integer getNumber() {
        return number;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public List<Long> getProductOptionIdList() {
        return productOptionIdList;
    }

    public void setProductOptionIdList(List<Long> productOptionIdList) {
        this.productOptionIdList = productOptionIdList;
    }

    public List<Long> getProductIngredientIdList() {
        return productIngredientIdList;
    }

    public void setProductIngredientIdList(List<Long> productIngredientIdList) {
        this.productIngredientIdList = productIngredientIdList;
    }

    public String getUserCartStatus() {
        return userCartStatus;
    }

    public void setUserCartStatus(String userCartStatus) {
        this.userCartStatus = userCartStatus;
    }

    public List<Long> getProductIds() {
        return productIds;
    }

    public void setProductIds(List<Long> productIds) {
        this.productIds = productIds;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }
}
