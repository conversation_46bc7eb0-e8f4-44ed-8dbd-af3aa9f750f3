package com.query;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 门票对象 tb_ticket
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */

public class TicketQuery extends Page{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 门票名字 */
    private String name;

    /** 价格属性(all:通用价格 man:男士价格 woman:女生价格) */
    private String priceCatalog;

    /** 价格 */
    private BigDecimal price;

    /** 酒水数量 */
    private Integer productNumber;

    /** 门票描述 */
    private String description;

    /** 上下架状态(up:上架 down:下架) */
    private String stage;

    /** 状态(0:正常 1:无效) */
    private String status;

    private List<TicketProductQuery> ticketProducts;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }
    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public void setPriceCatalog(String priceCatalog) {
        this.priceCatalog = priceCatalog;
    }

    public String getPriceCatalog() {
        return priceCatalog;
    }
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice() {
        return price;
    }
    public void setProductNumber(Integer productNumber) {
        this.productNumber = productNumber;
    }

    public Integer getProductNumber() {
        return productNumber;
    }
    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    public void setStage(String stage) {
        this.stage = stage;
    }

    public String getStage() {
        return stage;
    }
    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public List<TicketProductQuery> getTicketProducts() {
        return ticketProducts;
    }

    public void setTicketProducts(List<TicketProductQuery> ticketProducts) {
        this.ticketProducts = ticketProducts;
    }

    public void setStatus(String status) {
        this.status = status;
    }
}
