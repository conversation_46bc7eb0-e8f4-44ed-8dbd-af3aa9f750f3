package com.query;

import java.util.Date;

import com.common.bean.Bean;

public class SequenceQuery extends Bean {
	private static final long serialVersionUID = 1L;

	/** 主键 */
	private Long id;


	/** 状态(0:正常 1:无效) */
	private String status;

	/** 修改时间 */
	private Date modifyTime;

	/** 创建时间 */
	private Date createTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Date getModifyTime() {
		return modifyTime;
	}

	public void setModifyTime(Date modifyTime) {
		this.modifyTime = modifyTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
}
