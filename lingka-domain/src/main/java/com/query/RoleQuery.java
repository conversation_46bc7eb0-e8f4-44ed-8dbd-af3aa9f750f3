package com.query;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 角色对象 tb_role
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */

public class RoleQuery extends Page{

    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 角色名字 */
    private String name;

    /** 描述 */
    private String description;

    /** 是否对外展示(0:展示 1:隐藏) */
    private String displaySwitch;

    /** 状态(0:正常 1:无效) */
    private String status;

    /** 修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date modifyTime;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }
    public void setName(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
    public void setDisplaySwitch(String displaySwitch) {
        this.displaySwitch = displaySwitch;
    }

    public String getDisplaySwitch() {
        return displaySwitch;
    }
    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
