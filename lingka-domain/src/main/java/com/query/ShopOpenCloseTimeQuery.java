package com.query;

import java.time.LocalTime;
import java.util.Date;

import com.common.constant.DataStatus;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 店铺营业时间对象 tb_shop_open_close_time
 *
 */

public class ShopOpenCloseTimeQuery extends Page {

    private static final long serialVersionUID = 4880997142940629828L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺 ID
     */
    private Long shopId;

    /**
     * 星期 (0: 统一营业时间, 1-7 星期一~日)
     */
    private String day;

    /**
     * 营业开始时间
     */
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private LocalTime openTime;

    /**
     * 营业结束时间
     */
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private LocalTime closeTime;

    /**
     * 数据状态 (0: 正常, 1: 删除)
     */
    private String status = DataStatus.Y.getCode();

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifyTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public ShopOpenCloseTimeQuery(String day, LocalTime openTime, LocalTime closeTime) {
        this.day = day;
        this.openTime = openTime;
        this.closeTime = closeTime;
    }

    public ShopOpenCloseTimeQuery() {
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getDay() {
        return day;
    }

    public void setOpenTime(LocalTime openTime) {
        this.openTime = openTime;
    }

    public LocalTime getOpenTime() {
        return openTime;
    }

    public void setCloseTime(LocalTime closeTime) {
        this.closeTime = closeTime;
    }

    public LocalTime getCloseTime() {
        return closeTime;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

}
