<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.local</groupId>
        <artifactId>lingka</artifactId>
        <version>dev</version>
    </parent>
    <groupId>com.local.lingka.domain</groupId>
    <artifactId>lingka-domain</artifactId>
    <packaging>jar</packaging>
    <name>lingka-domain</name>
    <url>https://maven.apache.org</url>
    <dependencies>
        <dependency>
            <groupId>com.local.lingka.common</groupId>
            <artifactId>lingka-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>
