package com.common.constant;

public enum UserTicketGiftRecordStage {
	Y("0","已领取"),
	N("1","未领取");
	private String code;
	private String name;
	private UserTicketGiftRecordStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserTicketGiftRecordStage value : UserTicketGiftRecordStage.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserTicketGiftRecordStage value : UserTicketGiftRecordStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
