package com.common.constant;

public enum AuditSuggest {
    RISKY("risky", "风险"),
    PASS("pass", "通过"),
    REVIEW("review", "人工审核");

    private final String code;
    private final String desc;

    AuditSuggest(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
