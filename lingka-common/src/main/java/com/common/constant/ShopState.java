package com.common.constant;

public enum ShopState {
    OPEN("open", "营业"),
    CLOSE("close", "暂停营业"),
    CLOSE_MANUAL("close_manual", "暂停营业(手动)");

    private final String code;
    private final String description;

    ShopState(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
