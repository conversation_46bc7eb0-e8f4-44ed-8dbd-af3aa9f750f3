package com.common.constant;

public enum OrderType {
	TICKET("ticket","门票"),
	PRODUCT("product","点单"),
	DIY("diy","单点制");
	private String code;
	private String name;
	OrderType(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public static String getCode(String name){
		for (OrderType gender : OrderType.values()) {
            if (gender.getName().equals(name)) {
                return gender.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (OrderType gender : OrderType.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
}
