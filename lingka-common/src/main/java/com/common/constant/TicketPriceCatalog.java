package com.common.constant;

public enum TicketPriceCatalog {
	ALL("all","通用价格"),
	<PERSON><PERSON>("man","男生价格"),
	<PERSON><PERSON><PERSON>("woman","女生价格");

	private String code;
	private String name;
	TicketPriceCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (TicketPriceCatalog value : TicketPriceCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
