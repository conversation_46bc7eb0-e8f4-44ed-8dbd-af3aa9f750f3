package com.common.constant;

public enum UserCartStatus {
    Y("0", "有效"),
    N("1", "无效");

    private final String code;
    private final String description;

    UserCartStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
