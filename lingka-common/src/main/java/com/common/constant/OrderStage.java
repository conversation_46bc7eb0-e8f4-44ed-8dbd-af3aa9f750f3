package com.common.constant;

public enum OrderStage {
	WAIT_PAY("0","待付款"),
	PRODUCTION("1","制作中/待使用"),
	FINISH("2","已完成"),
	REFUND("3","已退款"),
	CANCEL("4","已取消");
	private String code;
	private String name;
	OrderStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}

	public static String getCode(String name){
		for (OrderStage gender : OrderStage.values()) {
            if (gender.getName().equals(name)) {
                return gender.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (OrderStage gender : OrderStage.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
}
