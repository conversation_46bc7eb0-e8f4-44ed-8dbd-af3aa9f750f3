package com.common.constant;

public enum UserTicketCatalog {
	SELF_BUY("0","自己购买"),
	OTHER_GIFT("1","他人转赠");
	private String code;
	private String name;
	private UserTicketCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserTicketCatalog value : UserTicketCatalog.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserTicketCatalog value : UserTicketCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
