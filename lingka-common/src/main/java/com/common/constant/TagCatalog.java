package com.common.constant;

public enum TagCatalog {
	SHOP("shop","店铺标签"),
	USER("user","用户标签");
	private String code;
	private String name;
	TagCatalog(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (TagCatalog value : TagCatalog.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
