package com.common.constant;

/**
 * 产品上下架枚举
 */
public enum TicketStage {
	UP("up","上架"),
	DOWN("down","下架");
	private String code;
	private String name;
	private TicketStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (TicketStage value : TicketStage.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (TicketStage value : TicketStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
