package com.common.constant;

public enum AuditType {
    SHOP_TEXT("shop_text", "店铺文本配置"),
    SHOP_MEDIA("shop_media", "店铺媒体配置"),
    USER_TEXT("user_text", "用户文本资料"),
    USER_MEDIA("user_media", "用户媒体资料");

    private final String code;
    private final String desc;

    AuditType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
