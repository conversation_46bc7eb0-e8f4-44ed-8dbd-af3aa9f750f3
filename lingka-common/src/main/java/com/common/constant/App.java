package com.common.constant;

public class App {

	public static final boolean PRODUCTION = true;
	public static final String COMMA = ",";
	public static final String DOT = ".";
	public static final String SEMICOLON = ";";
	public static final String PLUS = "+";
	public static final String MINUS = "-";
	public static final String MULTI = "*";
	public static final String PAUSE = "、";
	public static final String TILDE = "~";
	// 手机号隐藏
	public static String HIDDEN_RULES = "(\\d{3})\\d{4}(\\d{4})";
	// 手机号中间隐藏显示符号
	public static String SHOW_SYMBOL = "$1****$2";

	public static String STOCK_TIME = "9999-01-01 00:00:00";

	public static final String APPLICATION_JSON_UTF8_VALUE = "application/json;charset=UTF-8";

	/**
	 * 阿里云keyId
	 */
	public static String ALI_CLOUD_KEY_ID = null;

	/**
	 * 阿里云keySecret
	 */
	public static String ALI_CLOUD_KEY_SECRET = null;


	/**
	 * oss Bucket 名称
	 */
	public static String FILE_BUCKET_NAME = null;

	/**
	 * oss外网访问节点
	 */
	public static String FILE_ENDPOINT = null;

	/**
	 * oss内网访问节点 (oss 和 ecs 在同一地域)
	 */
	public static String FILE_LAN_ENDPOINT = null;

	/**
	 * 去除空格正则
	 */
	public static String REMOVE_THE_BLANK_SPACE = "^[　*| *| *|\t|\\s*]*|[　*| *| *|\t|\\s*]*$";

	/**
	 *  默认密码   000000 MD5加密后
	 */
	public static String PASSWORD = "670B14728AD9902AECBA32E22FA4F6BD";

	/**
	 * 短信验证码的过期时间  单位:秒
	 */
	public static final int USER_SMS_CAPTCHA_EXPIRE_TIME = 120;


	/**
	 * 用户订单过期时间  单位:分钟
	 */
	public static final int USER_ORDER_EXPIRE_TIME = 10;

	/**
	 * 邮箱验证码的过期时间  单位:秒
	 */
	public static final int USER_EMAIL_CAPTCHA_EXPIRE_TIME = 600;

	/**
	 * 微信小程序的appid
	 */
	public static String WEHCAT_APP_ID = null;

	/**
	 * 微信小程序的appSecret
	 */
	public static String WEHCAT_APP_SECRET = null;

	/**
	 * 汇付支付应用id
	 */
	public static final String HUIFU_APP_ID = "app_3bc56b56-d638-4f81-b9ec-3b50fb07a66c";

	/**
	 * 微信小程序通知的路径
	 */
	public static final String WEHCAT_INFORM_PAGE_PATH = "wxa02acc2254442db1";


	/**
	 * 邮箱验证码模版
	 */
	public static final String EMAIL_CAPTCHA_TEMPLATE = "欢迎同学注册清北商学院，本次验证码为 %s";

	/**
	 * 通知通过微信公众号推送的模版编号
	 */
	public static final String USER_WECHAT_INFORM_TEMPLATE_ID = "xxx";
	
	/**
	 * 流水单号前缀
	 */
	public static final String USER_FLOW_PREFIX = "QB";

	/**
	 * 用户门票分隔时间节点(已每天的12点为准)
	 */
	public static final String USER_TICKET_SPLIT_TIME = "12:00:00";
}
