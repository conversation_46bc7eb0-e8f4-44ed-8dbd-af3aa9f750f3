package com.common.constant;

/**
 * 公共上下架枚举
 */
public enum TableFlag {
	Y("0","是"),
	N("1","否");
	private String code;
	private String name;
	TableFlag(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (TableFlag value : TableFlag.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (TableFlag value : TableFlag.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
