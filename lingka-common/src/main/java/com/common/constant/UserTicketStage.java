package com.common.constant;

public enum UserTicketStage {
	VERIFIED("0","已核销"),
	REFUND("1","已退款");
	private String code;
	private String name;
	private UserTicketStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserTicketStage value : UserTicketStage.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserTicketStage value : UserTicketStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
