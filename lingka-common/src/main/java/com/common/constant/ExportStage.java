package com.common.constant;

public enum ExportStage {
	WAIT("0","排队中"),
	ING("1","进行中"),
	OK("2","成功"),
	REJECT("3","无法进行"),
	FAIL("4","失败");
	private String code;
	private String name;
	ExportStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (ExportStage gender : ExportStage.values()) {
            if (gender.getCode().equals(code)) {
                return gender.name;
            }
        }
		return null;
	}
	public static ExportStage get(String code){
		for (ExportStage value : ExportStage.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
		return null;
	}
}
