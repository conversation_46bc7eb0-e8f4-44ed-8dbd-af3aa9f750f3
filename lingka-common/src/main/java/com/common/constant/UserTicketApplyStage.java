package com.common.constant;

public enum UserTicketApplyStage {
	PENDING("0","待审核"),
	PASS("1","已通过"),
	REJECT("2","已拒绝"),
	CANCEL("3","已取消");
	private String code;
	private String name;
	private UserTicketApplyStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (UserTicketApplyStage value : UserTicketApplyStage.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (UserTicketApplyStage value : UserTicketApplyStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
