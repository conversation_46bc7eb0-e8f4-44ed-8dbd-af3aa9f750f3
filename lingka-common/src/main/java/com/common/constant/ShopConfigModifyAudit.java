package com.common.constant;

public enum ShopConfigModifyAudit {
    REJECTED(0, "驳回"),
    PENDING(1, "审核中"),
    APPROVED(2, "通过"),
    AUTO_INVALID(3, "修改内容自动作废");

    private final int code;
    private final String desc;

    ShopConfigModifyAudit(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return String.valueOf(code);
    }

    public String getDesc() {
        return desc;
    }
}
