package com.common.constant;

/**
 * 产品上下架枚举
 */
public enum ProductStage {
	UP("up","上架"),
	DOWN("down","下架");
	private String code;
	private String name;
	private ProductStage(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getCode(String name){
		for (ProductStage value : ProductStage.values()) {
            if (value.getName().equals(name)) {
                return value.code;
            }
        }
		return null;
	}
	public static String getName(String code){
		for (ProductStage value : ProductStage.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
