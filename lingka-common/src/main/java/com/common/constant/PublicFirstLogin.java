package com.common.constant;

public enum PublicFirstLogin {
	Y("0","首次登录"),
	N("1","非首次登录");
	private String code;
	private String name;
	PublicFirstLogin(String code, String name){
		this.code = code;
		this.name = name;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public static String getName(String code){
		for (PublicFirstLogin value : PublicFirstLogin.values()) {
            if (value.getCode().equals(code)) {
                return value.name;
            }
        }
		return null;
	}
}
