package com.common.util;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class ReflectDiffUtils {

    /**
     * 对比两个对象，返回有变化的字段 -> 新值
     * key 已经是下划线格式（数据库列名）
     */
    public static Map<String, Object> diff(Object oldObj, Object newObj) {
        Map<String, Object> changes = new HashMap<>();
        if (oldObj == null || newObj == null) {
            return changes;
        }
        Class<?> clazz = newObj.getClass();
        try {
            for (Field field : clazz.getDeclaredFields()) {
                field.setAccessible(true);
                Object oldVal = field.get(oldObj);
                Object newVal = field.get(newObj);

                if (newVal != null && !Objects.equals(oldVal, newVal)) {
                    String columnName = camelToSnake(field.getName());
                    changes.put(columnName, newVal);
                }
            }
        } catch (IllegalAccessException e) {
            throw new RuntimeException("Diff failed", e);
        }
        return changes;
    }

    /**
     * 驼峰转下划线，例如 userName -> user_name
     */
    private static String camelToSnake(String str) {
        StringBuilder result = new StringBuilder();
        for (char c : str.toCharArray()) {
            if (Character.isUpperCase(c)) {
                result.append("_").append(Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }
}
