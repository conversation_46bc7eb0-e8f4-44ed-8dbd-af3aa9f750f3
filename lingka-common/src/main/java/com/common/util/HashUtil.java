package com.common.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class HashUtil {
    /**
     * 计算字符串的md5值
     */
    public static String md5(String src) {
        return digest(src, "MD5");
    }

    /**
     * 计算文件的md5值
     *
     * @return
     */
    public static String md5(File file) {

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            FileInputStream fis = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int length;
            while ((length = fis.read(buffer)) != -1) {
                md.update(buffer, 0, length);
            }
            fis.close();
            return byte2hex(md.digest());
        } catch (IOException | NoSuchAlgorithmException e){
            return null;
        }
    }

    /**
     * 计算字符串的sha1值
     *
     * @param src
     * @return
     */
    public static String sha1(String src) {
        return digest(src, "SHA-1");
    }


    private static String digest(String src, String name){
        try {
            MessageDigest alg = MessageDigest.getInstance(name);
            byte[] result = alg.digest(src.getBytes());
            return byte2hex(result);
        } catch (NoSuchAlgorithmException ex) {
            return null;
        }
    }

    /**
     * 将byte数组转换成十六进制可读字符串。
     */
    public static String byte2hex(byte[] b) {

        if (b == null){
            return null;
        }

        StringBuilder hs = new StringBuilder();
        String stmp;
        for (byte value : b) {
            stmp = (Integer.toHexString(value & 0XFF));
            if (stmp.length() == 1)
                hs.append("0").append(stmp);
            else
                hs.append(stmp);
        }
        return hs.toString().toUpperCase();
    }

    /**
     * 将十六进制可读字符串转换成byte数组。

     */
    public static byte[] hex2byte(String str) {

        if (str == null){
            return null;
        }

        if (str.isEmpty()){
            return new byte[0];
        }

        // 判断是否为合法的十六进制表示字符串
        Pattern pattern = Pattern.compile("[0-9a-fA-F]+");
        Matcher match = pattern.matcher(str);
        if (!match.matches()) {
            return null;
        }

        // 判断长度是否为偶数
        if ((str.length() % 2) != 0) {
            return null;
        }

        // 进行正常的转换流程
        byte[] b = str.getBytes();
        byte[] ret = new byte[b.length / 2];
        for (int n = 0; n < b.length; n += 2) {
            String item = new String(b, n, 2);
            ret[n / 2] = (byte) Integer.parseInt(item, 16);
        }
        return ret;
    }
}
