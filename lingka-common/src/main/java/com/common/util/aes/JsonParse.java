/**
 *
 * @copyright Copyright (c) 1998-2020 Tencent Inc.
 * @copyright Copyright (c) 2025 Howard Wu.
 */

package com.common.util.aes;

import org.json.JSONObject;

/**
 * JsonParse class
 * <p>
 * 提供提取消息格式中的密文及生成回复消息格式的接口.
 */
class JsonParse {

    /**
     * 提取出 JSON 包中的加密消息
     *
     * @param jsontext 待提取的json字符串
     * @return 提取出的加密消息字符串
     * @throws AesException 如果解析失败则抛出异常
     */
    public static Object[] extract(String jsontext) throws AesException {
        Object[] result = new Object[2];
        try {

            JSONObject json = new JSONObject(jsontext);
            String encrypt_msg = json.getString("Encrypt");
            String tousername = json.getString("ToUserName");

            result[0] = tousername;
            result[1] = encrypt_msg;
            return result;
        } catch (Exception e) {
            throw new AesException(AesException.ParseJsonError , e);
        }
    }

    /**
     * 生成json消息
     *
     * @param encrypt   加密后的消息密文
     * @param signature 安全签名
     * @param timestamp 时间戳
     * @param nonce     随机字符串
     * @return 生成的json字符串
     */
    public static String generate(String encrypt, String signature, String timestamp, String nonce) {
        String format = "{\"Encrypt\":\"%1$s\",\"MsgSignature\":\"%2$s\",\"TimeStamp\":\"%3$s\",\"Nonce\":\"%4$s\"}";
        return String.format(format, encrypt, signature, timestamp, nonce);
    }
}
