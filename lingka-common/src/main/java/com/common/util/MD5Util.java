package com.common.util;

import java.security.MessageDigest;

public class MD5Util {
	public static void main(String[] args){
		System.out.println(toMD5("9858390" + "$" + "38659215534F141B5496BDF477D909A0" + "$" + "1590370541"));
	}
	private final static String[] hexDigits = { "0", "1", "2", "3", "4", "5","6", "7", "8", "9", "A", "B", "C", "D", "E", "F" };
	public static String toMD5(String origin) {
		String resultString;
		try {
			resultString = origin;
			MessageDigest md = MessageDigest.getInstance("MD5");
			resultString = byteArrayToHexString(md.digest(resultString.getBytes()));
		} catch (Exception ex) {
			ex.printStackTrace();
			throw new RuntimeException("MD5加密过程异常", ex);
		}
		return resultString;
	}
	private static String byteArrayToHexString(byte[] b) {
		StringBuilder resultSb = new StringBuilder();
        for (byte value : b) {
            resultSb.append(byteToHexString(value));
        }
		return resultSb.toString();
	}

	private static String byteToHexString(byte b) {
		int n = b;
		if (n < 0)
			n = 256 + n;
		int d1 = n / 16;
		int d2 = n % 16;
		return hexDigits[d1] + hexDigits[d2];
	}
}
