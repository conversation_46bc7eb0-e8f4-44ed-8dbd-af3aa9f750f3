package com.common.util;

import org.springframework.http.HttpMethod;

import com.common.bean.TiandituGeocoderLocation;
import com.common.bean.TiandituGeocoderRequest;
import com.common.bean.TiandituGeocoderResponse;
import com.common.bean.TiandituGeocoderToAddressRequest;
import com.common.bean.TiandituGeocoderToAddressResponse;
import com.common.bean.TiandituGeocoderToAddressResult;
import com.common.client.TiandituClient;

/**
 * LbsUtil - 地理位置服务工具类
 *
 * <AUTHOR>
 */
public class LbsUtil {
    // 2 * 地球半径，预计算
    private static final double EARTH_RADIUSx2 = 12756274.0;
    // PI/180，预计算
    private static final double DEG_TO_RAD = 0.017453292519943295;

    /**
     * 使用 Haversine 公式计算两点间的距离（GCJ02坐标系）
     *
     * @param latA 点A的纬度
     * @param lonA 点A的经度
     * @param latB 点B的纬度
     * @param lonB 点B的经度
     * @return 距离（米）
     */
    public static double distanceGCJ02(double latA, double lonA, double latB, double lonB) {
        // Haversine 公式
        double sinDLat = Math.sin((latB - latA) * DEG_TO_RAD * 0.5);
        double sinDLon = Math.sin((lonB - lonA) * DEG_TO_RAD * 0.5);
        double a = sinDLat * sinDLat +
                Math.cos(latA * DEG_TO_RAD) * Math.cos(latB * DEG_TO_RAD) * sinDLon * sinDLon;
        return Math.round(EARTH_RADIUSx2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a)));
    }

    /**
     * 使用 Haversine 公式计算两点间的距离（GCJ02坐标系）
     *
     * @param a 点A
     * @param b 点B
     * @return 距离（米）
     */
    public static double distanceGCJ02(Point a, Point b) {
        return distanceGCJ02(a.lat, a.lon, b.lat, b.lon);
    }

    public static TiandituGeocoderLocation addressToLocation(String address) {
        TiandituGeocoderRequest request = new TiandituGeocoderRequest();
        request.setKeyWord(address);
        TiandituClient<TiandituGeocoderResponse> client = new TiandituClient<>();
        client.setMethod(HttpMethod.POST);
        TiandituGeocoderResponse response = client.execute("/geocoder", request, TiandituGeocoderResponse.class);
        if ("0".equals(response.getStatus())) {
            return response.getLocation();
        }
        return null;
    }

    public static Point addressToPoint(String address) {
        TiandituGeocoderLocation location = addressToLocation(address);
        if (location != null) {
            return new Point(Double.parseDouble(location.getLat()), Double.parseDouble(location.getLon()));
        }
        return null;
    }

    public static TiandituGeocoderToAddressResult pointToLocation(Point point) {
        TiandituGeocoderToAddressRequest request = new TiandituGeocoderToAddressRequest(point);
        TiandituClient<TiandituGeocoderToAddressResponse> client = new TiandituClient<>();
        client.setMethod(HttpMethod.POST);
        TiandituGeocoderToAddressResponse response = client.execute("/geocoder?postStr", request, TiandituGeocoderToAddressResponse.class);
        if ("0".equals(response.getStatus())) {
            return response.getResult();
        }
        return null;
    }

    public static class Point {
        public final double lat;
        public final double lon;

        /**
         * 坐标点
         *
         * @param lat 纬度
         * @param lon 经度
         */
        public Point(double lat, double lon) {
            this.lat = lat;
            this.lon = lon;
        }
    }
}
