package com.common.util;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.common.bean.NameValuePair;
import com.common.constant.Charset;


public class StringUtil {
	public static String nvl(String str,String replaceChar){
		if(isEmpty(str)){
			return replaceChar;
		}
		return str.trim();
	}
	public static boolean isEmpty(String str){
        return str == null || str.trim().isEmpty() || "null".equalsIgnoreCase(str) || "undefined".equalsIgnoreCase(str);
	}
	public static String join(String[] strArray, String delimiter){
		if(strArray.length == 0){
			return "";
		}
		StringBuilder sb = new StringBuilder();
		int i;
		for(i = 0; i < strArray.length - 1; i++){
			sb.append(strArray[i]).append(delimiter);
		}
		return sb.append(strArray[i]).toString();
	}
	/**
	 * 字符串去空格/回车/换行/制表符
	 * \n 回车(\u000a) 
	 * \t 水平制表符(\u0009) 
	 * \s 空格(\u0008) 
	 * \r 换行(\u000d)
	 * @param str
	 * @return
	 */
	public static String replaceBlank(String str){
		String dest = "";
		if (str != null) {
			Pattern p = Pattern.compile("\\s*|\t|\r|\n");
			Matcher m = p.matcher(str);
			dest = m.replaceAll("");
		}
		return dest;
	}
	public static String replaceSymbol(String str) {
		if(isEmpty(str)){
			return "";
		}
		str = str.trim().replaceAll("[\\pP‘’“”]", "");
		return str;
	}
	public static String subString(String str,int length){
		if(str.length() > length){
			return str.substring(0,length);
		}
		return str;
	}
	public static String join(List<String> list, String delimiter){
		int size = list.size();
		if(size == 0){
			return "";
		}
		StringBuilder sb = new StringBuilder();
		int i;
		for(i = 0; i < size - 1; i++){
			sb.append(list.get(i)).append(delimiter);
		}
		return sb.append(list.get(i)).toString();
	}

	public static String joinWithLong(List<Long> list, String delimiter){
		int size = list.size();
		if(size == 0){
			return "";
		}
		StringBuilder sb = new StringBuilder();
		int i;
		for(i = 0; i < size - 1; i++){
			sb.append(list.get(i)).append(delimiter);
		}
		return sb.append(list.get(i)).toString();
	}

	public static String join(Set<String> set, String delimiter){
		int size = set.size();
		if(size == 0){
			return "";
		}
		StringBuilder sb = new StringBuilder();
		boolean isFirst = true;
		for(String str : set){
			if(isFirst){
				sb.append(str);
				isFirst = false;
			}else{
				sb.append(delimiter).append(str);
			}
		}
		return sb.toString();
	}


	public static List<String> split(String str,String delimiter){
		List<String> list = new ArrayList<>();
		if (isEmpty(str)){
			return list;
		}
		if (str.contains(delimiter)){
			String[] strArr = str.split(delimiter);
            list.addAll(Arrays.asList(strArr));
		}else {
			list.add(str);
		}
		return list;
	}

	public static List<Long> splitWithLong(String str,String delimiter){
		List<Long> list = new ArrayList<>();
		if (isEmpty(str)){
			return list;
		}
		if (str.contains(delimiter)){
			String[] strArr = str.split(delimiter);
			for (String s : strArr) {
				list.add(Long.valueOf(s));
			}
		}else {
			list.add(Long.valueOf(str));
		}
		return list;
	}

	private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS;
    }
  
    public static boolean hasChinese(String str) {
        char[] ch = str.toCharArray();
        for (char c : ch) {
            if (isChinese(c)) {
                return true;
            }
        }
        return false;
    }
    public static String getRequestParameters(String url, NameValuePair... pairs){
    	StringBuilder buffer = new StringBuilder(url).append("?");
		int counter = 0;
		if(pairs != null){
			for(NameValuePair pair : pairs){
				String name = pair.getName();
				Object value = pair.getValue();
				try{
					if(counter > 0){
						if(value != null){
							if(value instanceof String){
								buffer.append("&").append(name).append("=").append(URLEncoder.encode((String)value, Charset.UTF_8));
							}else if(value instanceof Integer){
								buffer.append("&").append(name).append("=").append(URLEncoder.encode(String.valueOf(value), Charset.UTF_8));
							}
						}else{
							buffer.append("&").append(name).append("=");
						}
					}else{
						if(value != null){
							if(value instanceof String){
								buffer.append(name).append("=").append(URLEncoder.encode((String)value, Charset.UTF_8));
							}else if(value instanceof Integer){
								buffer.append(name).append("=").append(URLEncoder.encode(String.valueOf(value), Charset.UTF_8));
							}
						}else{
							buffer.append(name).append("=");
						}
					}
				}catch (Exception e) {
					throw new RuntimeException(e);
				}
				counter++;
			}
		}
//		if(buffer.toString().endsWith("?")){
//				
//		}else{
//			buffer.append("&");
//		}
		return buffer.toString();
    }


	public static int calculateCharacter(String str, char character){
		int count = 0;
		for (int i = 0; i < str.length(); i++){
			if (str.charAt(i) == character){
				count ++;
			}
		}
		return count;
	}

}