package com.common.util;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * http://blog.csdn.net/flueky/article/details/77099454
 * <AUTHOR>
 *
 */
public class CalcUtil {
	
	/**
     * 计算等额本息还款
     *
     * @param principal 贷款总额
     * @param months    贷款期限
     * @param rate      贷款利率
     * @return
     */
    public static BigDecimal[] calculateEqualPrincipalAndInterest(BigDecimal principal, BigDecimal months, BigDecimal rate) {
        List<BigDecimal> data = new ArrayList<>();
        BigDecimal monthRate = rate.divide(new BigDecimal(100).multiply(new BigDecimal(12)));//月利率
        BigDecimal pow = new BigDecimal(1).add(monthRate).pow(months.intValue());
        BigDecimal preLoan = principal.multiply(monthRate).multiply(pow).divide(pow.subtract(new BigDecimal(1)));//每月还款金额
        BigDecimal totalMoney = preLoan.multiply(months);//还款总额
        BigDecimal interest = totalMoney.subtract(principal);//还款总利息
        data.add(totalMoney);//还款总额
        data.add(principal);//贷款总额
        data.add(interest);//还款总利息
        data.add(preLoan);//每月还款金额
        data.add(months);//还款期限
        return data.toArray(new BigDecimal[data.size()]);
    }
    
	/**
     * 计算等额本息还款
     *
     * @param principal 贷款总额
     * @param months    贷款期限
     * @param rate      贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipalAndInterest(double principal, int months, double rate) {
        List<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double preLoan = (principal * monthRate * Math.pow((1 + monthRate), months)) / (Math.pow((1 + monthRate), months) - 1);//每月还款金额
        double totalMoney = preLoan * months;//还款总额
        double interest = totalMoney - principal;//还款总利息
        data.add(Double.toString(totalMoney));//还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//还款总利息
        data.add(Double.toString(preLoan));//每月还款金额
        data.add(Integer.toString(months));//还款期限
        return data.toArray(new String[data.size()]);
    }
    
    /**
     * 计算等额本金还款
     *
     * @param principal 贷款总额
     * @param months    贷款期限
     * @param rate      贷款利率
     * @return
     */
    public static BigDecimal[] calculateEqualPrincipal(BigDecimal principal, BigDecimal months, BigDecimal rate) {
        ArrayList<BigDecimal> data = new ArrayList<>();
        BigDecimal monthRate = rate.divide(new BigDecimal(100).multiply(new BigDecimal(12)));//月利率
        BigDecimal prePrincipal = principal.divide(months);//每月还款本金
        BigDecimal firstMonth = prePrincipal.add(principal.multiply(monthRate));//第一个月还款金额
        BigDecimal decreaseMonth = prePrincipal.multiply(monthRate);//每月利息递减
        BigDecimal interest = months.add(new BigDecimal(1)).multiply(principal).multiply(monthRate).divide(new BigDecimal(2));//还款总利息
        BigDecimal totalMoney = principal.add(interest);//还款总额
        data.add(totalMoney);//还款总额
        data.add(principal);//贷款总额
        data.add(interest);//还款总利息
        data.add(firstMonth);//首月还款金额
        data.add(decreaseMonth);//每月递减利息
        data.add(months);//还款期限
        return data.toArray(new BigDecimal[data.size()]);
    }
    
    /**
     * 计算等额本金还款
     *
     * @param principal 贷款总额
     * @param months    贷款期限
     * @param rate      贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipal(double principal, int months, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double prePrincipal = principal / months;//每月还款本金
        double firstMonth = prePrincipal + principal * monthRate;//第一个月还款金额
        double decreaseMonth = prePrincipal * monthRate;//每月利息递减
        double interest = (months + 1) * principal * monthRate / 2;//还款总利息
        double totalMoney = principal + interest;//还款总额
        data.add(Double.toString(totalMoney));//还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//还款总利息
        data.add(Double.toString(firstMonth));//首月还款金额
        data.add(Double.toString(decreaseMonth));//每月递减利息
        data.add(Integer.toString(months));//还款期限
        data.add(Double.toString(prePrincipal));//每月还款金额
        return data.toArray(new String[data.size()]);
    }
    
    /**
     * 一次性提前还款计算（等额本息）
     *
     * @param principal 贷款总额
     * @param months    贷款期限
     * @param payTimes  已还次数
     * @param rate      贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipalAndInterest(double principal, int months, int payTimes, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double preLoan = (principal * monthRate * Math.pow((1 + monthRate), months)) / (Math.pow((1 + monthRate), months) - 1);//每月还款金额
        double totalMoney = preLoan * months;//还款总额
        double interest = totalMoney - principal;//还款总利息
        double leftLoan = principal * Math.pow(1 + monthRate, payTimes) - preLoan * (Math.pow(1 + monthRate, payTimes) - 1) / monthRate;//n个月后欠银行的钱
        double payLoan = principal - leftLoan;//已还本金
        double payTotal = preLoan * payTimes;//已还总金额
        double payInterest = payTotal - payLoan;//已还利息
        double totalPayAhead = leftLoan * (1 + monthRate);//剩余一次还清
        double saveInterest = totalMoney - payTotal - totalPayAhead;
        data.add(Double.toString(totalMoney));//原还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//原还款总利息
        data.add(Double.toString(preLoan));//原还每月还款金额
        data.add(Double.toString(payTotal));//已还总金额
        data.add(Double.toString(payLoan));//已还本金
        data.add(Double.toString(payInterest));//已还利息
        data.add(Double.toString(totalPayAhead));//一次还清支付金额
        data.add(Double.toString(saveInterest));//节省利息
        data.add(String.valueOf(0));//剩余还款期限
        return data.toArray(new String[data.size()]);
    }
    
    /**
     * 一次性提前还款计算(等额本金)
     *
     * @param principal 贷款总额
     * @param months    贷款期限
     * @param payTimes  已还次数
     * @param rate      贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipal(double principal, int months, int payTimes, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double prePrincipal = principal / months;//每月还款本金
        double firstMonth = prePrincipal + principal * monthRate;//第一个月还款金额
        double decreaseMonth = prePrincipal * monthRate;//每月利息递减
        double interest = (months + 1) * principal * monthRate / 2;//还款总利息
        double totalMoney = principal + interest;//还款总额
        double payLoan = prePrincipal * payTimes;//已还本金
        double payInterest = (principal * payTimes - prePrincipal * (payTimes - 1) * payTimes / 2) * monthRate;//已还利息
        double payTotal = payLoan + payInterest;//已还总额
        double totalPayAhead = (principal - payLoan) * (1 + monthRate);//提前还款金额（剩余本金加上剩余本金当月利息）
        double saveInterest = totalMoney - payTotal - totalPayAhead;
        data.add(Double.toString(totalMoney));//原还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//原还款总利息
        data.add(Double.toString(firstMonth));//原首月还款金额
        data.add(Double.toString(decreaseMonth));//原每月递减利息
        data.add(Double.toString(payTotal));//已还总金额
        data.add(Double.toString(payLoan));//已还本金
        data.add(Double.toString(payInterest));//已还利息
        data.add(Double.toString(totalPayAhead));//一次还清支付金额
        data.add(Double.toString(saveInterest));//节省利息
        data.add(String.valueOf(0));//剩余还款期限
        return data.toArray(new String[data.size()]);
    }
    
    /**
     * 部分提前还款计算（等额本息、月供不变）
     *
     * @param principal      贷款总额
     * @param months         贷款期限
     * @param aheadPrincipal 提前还款金额
     * @param payTimes       已还次数
     * @param rate           贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipalAndInterestApart(double principal, int months, double aheadPrincipal, int payTimes, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double preLoan = (principal * monthRate * Math.pow((1 + monthRate), months)) / (Math.pow((1 + monthRate), months) - 1);//每月还款金额
        double totalMoney = preLoan * months;//还款总额
        double interest = totalMoney - principal;//还款总利息
        double leftLoanBefore = principal * Math.pow(1 + monthRate, payTimes) - preLoan * (Math.pow(1 + monthRate, payTimes) - 1) / monthRate;//提前还款前欠银行的钱
        double leftLoan = principal * Math.pow(1 + monthRate, payTimes + 1) - preLoan * (Math.pow(1 + monthRate, payTimes + 1) - 1) / monthRate-aheadPrincipal;//提前还款后欠银行的钱
        double payLoan = principal - leftLoanBefore;//已还本金
        double payTotal = preLoan * payTimes ;//已还总金额
        double payInterest = payTotal - payLoan;//已还利息
        double aheadTotalMoney = aheadPrincipal + preLoan;//提前还款总额
        //计算剩余还款期限
        int leftMonth = (int) Math.floor(Math.log(preLoan / (preLoan - leftLoan * monthRate)) / Math.log(1 + monthRate));
        double newPreLoan = (leftLoan * monthRate * Math.pow((1 + monthRate), leftMonth)) / (Math.pow((1 + monthRate), leftMonth) - 1);//剩余贷款每月还款金额
        double leftTotalMoney = newPreLoan * leftMonth;//剩余还款总额
        double leftInterest = leftTotalMoney - (leftLoan-aheadPrincipal);
        double saveInterest = totalMoney - aheadTotalMoney - leftTotalMoney-payTotal;
        data.add(Double.toString(totalMoney));//原还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//原还款总利息
        data.add(Double.toString(preLoan));//原还每月还款金额
        data.add(Double.toString(payTotal));//已还总金额
        data.add(Double.toString(payLoan));//已还本金
        data.add(Double.toString(payInterest));//已还利息
        data.add(Double.toString(aheadTotalMoney));//提前还款总额
        data.add(Double.toString(leftTotalMoney));//剩余还款总额
        data.add(Double.toString(leftInterest));//剩余还款总利息
        data.add(Double.toString(newPreLoan));//剩余每月还款金额
        data.add(Double.toString(saveInterest));//节省利息
        data.add(String.valueOf(leftMonth));//剩余还款期限
        return data.toArray(new String[data.size()]);
    }
    
    /**
     *  部分提前还款计算(等额本金、月供不变)
     * @param principal      贷款总额
     * @param months         贷款期限
     * @param aheadPrincipal 提前还款金额
     * @param payTimes       已还次数
     * @param rate           贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipalApart(double principal, int months, double aheadPrincipal, int payTimes, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double prePrincipal = principal / months;//每月还款本金
        double firstMonth = prePrincipal + principal * monthRate;//第一个月还款金额
        double decreaseMonth = prePrincipal * monthRate;//每月利息递减
        double interest = (months + 1) * principal * monthRate / 2;//还款总利息
        double totalMoney = principal + interest;//还款总额
        double payLoan = prePrincipal * payTimes;//已还本金
        double payInterest = (principal * payTimes - prePrincipal * (payTimes - 1) * payTimes / 2) * monthRate;//已还利息
        double payTotal = payLoan + payInterest;//已还总额
        double aheadTotalMoney = (principal - payLoan) *  monthRate+aheadPrincipal+prePrincipal;//提前还款金额
        double leftLoan = principal - aheadPrincipal - payLoan-prePrincipal;//剩余金额
        int leftMonth = (int) Math.floor(leftLoan / prePrincipal);
        double newPrePrincipal = leftLoan / leftMonth;//新的每月还款本金
        double newFirstMonth = newPrePrincipal + leftLoan * monthRate;//新的第一个月还款金额
        double newDecreaseMonth = newPrePrincipal * monthRate;//新的每月利息递减
        double leftInterest = (leftMonth + 1) * leftLoan * monthRate / 2;//还款总利息
        double leftTotalMoney = leftLoan + leftInterest;//还款总额
        double saveInterest = totalMoney-payTotal-aheadTotalMoney-leftTotalMoney;
        data.add(Double.toString(totalMoney));//原还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//原还款总利息
        data.add(Double.toString(firstMonth));//原还首月还款金额
        data.add(Double.toString(decreaseMonth));//原每月递减利息
        data.add(Double.toString(payTotal));//已还总金额
        data.add(Double.toString(payLoan));//已还本金
        data.add(Double.toString(payInterest));//已还利息
        data.add(Double.toString(aheadTotalMoney));//提前还款总额
        data.add(Double.toString(leftTotalMoney));//剩余还款总额
        data.add(Double.toString(leftInterest));//剩余还款总利息
        data.add(Double.toString(newFirstMonth));//剩余首月还款金额
        data.add(Double.toString(newDecreaseMonth));//剩余月递减利息
        data.add(Double.toString(saveInterest));//节省利息
        data.add(String.valueOf(leftMonth));//剩余还款期限
        return data.toArray(new String[data.size()]);
    }
    
    /**
     * 部分提前还款计算（等额本息、期限不变）
     * @param principal      贷款总额
     * @param months         贷款期限
     * @param aheadPrincipal 提前还款金额
     * @param payTimes       已还次数
     * @param rate           贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipalAndInterestApart2(double principal, int months, double aheadPrincipal, int payTimes, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double preLoan = (principal * monthRate * Math.pow((1 + monthRate), months)) / (Math.pow((1 + monthRate), months) - 1);//每月还款金额
        double totalMoney = preLoan * months;//还款总额
        double interest = totalMoney - principal;//还款总利息
        double leftLoanBefore = principal * Math.pow(1 + monthRate, payTimes ) - preLoan * (Math.pow(1 + monthRate, payTimes ) - 1) / monthRate;//提前还款前欠银行的钱
        double leftLoan = principal * Math.pow(1 + monthRate, payTimes + 1) - preLoan * (Math.pow(1 + monthRate, payTimes + 1) - 1) / monthRate;//提前还款后银行的钱
        double payLoan = principal - leftLoanBefore;//已还本金
        double payTotal = preLoan * payTimes;//已还总金额
        double payInterest = payTotal - payLoan;//已还利息
        double aheadTotalMoney = preLoan + aheadPrincipal;//下个月还款金额
        double newPreLoan = ((leftLoan - aheadPrincipal) * monthRate * Math.pow((1 + monthRate), months - payTimes - 1)) / (Math.pow((1 + monthRate), months - payTimes - 1) - 1);//下个月起每月还款金额
        double leftTotalMoney = newPreLoan*(months-payTimes);
        double leftInterest =leftTotalMoney -(leftLoan - aheadPrincipal);
        double saveInterest = totalMoney-payTotal-aheadTotalMoney-leftTotalMoney;
        data.add(Double.toString(totalMoney));//原还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//原还款总利息
        data.add(Double.toString(preLoan));//原还每月还款金额
        data.add(Double.toString(payTotal));//已还总金额
        data.add(Double.toString(payLoan));//已还本金
        data.add(Double.toString(payInterest));//已还利息
        data.add(Double.toString(aheadTotalMoney));//提前还款总额
        data.add(Double.toString(leftTotalMoney));//剩余还款总额
        data.add(Double.toString(leftInterest));//剩余还款总利息
        data.add(Double.toString(newPreLoan));//剩余每月还款金额
        data.add(Double.toString(saveInterest));//节省利息
        data.add(String.valueOf(months));//剩余还款期限
        return data.toArray(new String[data.size()]);
    }
    
    /**
     * 部分提前还款计算(等额本金、期限不变)
     * @param principal      贷款总额
     * @param months         贷款期限
     * @param aheadPrincipal 提前还款金额
     * @param payTimes       已还次数
     * @param rate           贷款利率
     * @return
     */
    public static String[] calculateEqualPrincipalApart2(double principal, int months, double aheadPrincipal, int payTimes, double rate) {
        ArrayList<String> data = new ArrayList<>();
        double monthRate = rate / 12;//月利率
        double prePrincipal = principal / months;//每月还款本金
        double firstMonth = prePrincipal + principal * monthRate;//第一个月还款金额
        double decreaseMonth = prePrincipal * monthRate;//每月利息递减
        double interest = (months + 1) * principal * monthRate / 2;//还款总利息
        double totalMoney = principal + interest;//还款总额
        double payLoan = prePrincipal * payTimes;//已还本金
        double payInterest = (principal * payTimes - prePrincipal * (payTimes - 1) * payTimes / 2) * monthRate;//已还利息
        double payTotal = payLoan + payInterest;//已还总额
        double aheadTotalMoney = (principal - payLoan) *  monthRate+aheadPrincipal+prePrincipal;//提前还款金额
        int leftMonth = months - payTimes-1;
        double leftLoan = principal - aheadPrincipal - payLoan-prePrincipal;
        double newPrePrincipal = leftLoan / leftMonth;//新的每月还款本金
        double newFirstMonth = newPrePrincipal + leftLoan * monthRate;//新的第一个月还款金额
        double newDecreaseMonth = newPrePrincipal * monthRate;//新的每月利息递减
        double leftInterest = (leftMonth + 1) * leftLoan * monthRate / 2;//还款总利息
        double leftTotalMoney = leftLoan + leftInterest;//还款总额
        double saveInterest = totalMoney-payTotal-aheadTotalMoney-leftTotalMoney;
        data.add(Double.toString(totalMoney));//原还款总额
        data.add(Double.toString(principal));//贷款总额
        data.add(Double.toString(interest));//原还款总利息
        data.add(Double.toString(firstMonth));//原还首月还款金额
        data.add(Double.toString(decreaseMonth));//原每月递减利息
        data.add(Double.toString(payTotal));//已还总金额
        data.add(Double.toString(payLoan));//已还本金
        data.add(Double.toString(payInterest));//已还利息
        data.add(Double.toString(aheadTotalMoney));//提前还款总额
        data.add(Double.toString(leftTotalMoney));//剩余还款总额
        data.add(Double.toString(leftInterest));//剩余还款总利息
        data.add(Double.toString(newFirstMonth));//剩余首月还款金额
        data.add(Double.toString(newDecreaseMonth));//剩余月递减利息
        data.add(Double.toString(saveInterest));//节省利息
        data.add(String.valueOf(months));//剩余还款期限
        return data.toArray(new String[data.size()]);
    }
    
    public static void main(String[] args) {
		String[] strs = calculateEqualPrincipalAndInterest(400000,15 * 12, 4.9);
		for(String str : strs){
			System.out.println(str);
		}
	}
}
