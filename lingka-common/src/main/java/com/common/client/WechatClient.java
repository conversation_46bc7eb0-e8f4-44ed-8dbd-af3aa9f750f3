package com.common.client;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.RequestEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import com.common.bean.WechatAccessTokenResponse;
import com.common.constant.App;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class WechatClient<T> {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private HttpMethod method = HttpMethod.POST;
    private final int timeout = 30 * 1000;

    private static final String SERVICE_URL = "https://api.weixin.qq.com";

    public T execute(String url, Object request, Class<?> clazz) {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);
            requestFactory.setConnectionRequestTimeout(timeout);
            requestFactory.setConnectTimeout(timeout);
            requestFactory.setReadTimeout(timeout);
            RequestEntity<?> requestEntity;
            switch (method) {
                case GET:
                    requestEntity = RequestEntity.get(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .build();
                    break;
                case PUT:
                    requestEntity = RequestEntity.put(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .body(request);
                    break;
                case DELETE:
                    requestEntity = RequestEntity.delete(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .build();
                    break;
                case POST:
                default:
                    requestEntity = RequestEntity.post(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .body(request);
                    break;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
            logger.info("wechat service request : {}", objectMapper.writeValueAsString(requestEntity));
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            restTemplate.setMessageConverters(this.getMessageConverters(restTemplate.getMessageConverters()));
            ResponseEntity<String> responseEntity = restTemplate.exchange(requestEntity, String.class);
            logger.info("wechat service response : {}", responseEntity);
            return objectMapper.readValue(responseEntity.getBody(), objectMapper.getTypeFactory().constructType(clazz));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            throw new RuntimeException(e);
        }
    }


    public ResponseEntity<byte[]> execute(String url, Object request) {
        try (CloseableHttpClient httpClient = HttpClients.custom().build()) {
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setHttpClient(httpClient);
            requestFactory.setConnectionRequestTimeout(timeout);
            requestFactory.setConnectTimeout(timeout);
            requestFactory.setReadTimeout(timeout);
            RequestEntity<?> requestEntity;
            switch (method) {
                case GET:
                    requestEntity = RequestEntity.get(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .build();
                    break;
                case PUT:
                    requestEntity = RequestEntity.put(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .body(request);
                    break;
                case DELETE:
                    requestEntity = RequestEntity.delete(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .build();
                    break;
                case POST:
                default:
                    requestEntity = RequestEntity.post(new URI(SERVICE_URL + url))
                            .header(HttpHeaders.CONTENT_TYPE, App.APPLICATION_JSON_UTF8_VALUE)
                            .body(request);
                    break;
            }
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
            logger.info("wechat service request : {}", objectMapper.writeValueAsString(requestEntity));
            RestTemplate restTemplate = new RestTemplate(requestFactory);
            restTemplate.setMessageConverters(this.getMessageConverters(restTemplate.getMessageConverters()));
            ResponseEntity<byte[]> responseEntity = restTemplate.exchange(requestEntity, byte[].class);
            logger.info("wechat service response : {}", responseEntity);
            return responseEntity;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            throw new RuntimeException(e);
        }
    }


    public HttpMethod getMethod() {
        return method;
    }

    public void setMethod(HttpMethod method) {
        this.method = method;
    }

    /**
     * 转换StringHttpMessageConverter默认编码问题
     *
     * @param converterList 要转换的 HttpMessageConverter 列表
     * @return 转换后的 HttpMessageConverter 列表
     */
    private List<HttpMessageConverter<?>> getMessageConverters(List<HttpMessageConverter<?>> converterList) {
        List<HttpMessageConverter<?>> list = new ArrayList<>();
        for (HttpMessageConverter<?> element : converterList) {
            if (element instanceof StringHttpMessageConverter) {
                list.add(new StringHttpMessageConverter(StandardCharsets.UTF_8));
            } else {
                list.add(element);
            }
        }
        return list;
    }


    /**
     * 获取微信小程序的 access_token
     *
     * @return access_token
     */
    public String getAccessToken() {
        String url = "/cgi-bin/token?grant_type=client_credential&appid=" + App.WEHCAT_APP_ID + "&secret=" + App.WEHCAT_APP_SECRET;
        WechatAccessTokenResponse response = (WechatAccessTokenResponse) this.execute(url, null, WechatAccessTokenResponse.class);
        if (response.getErrcode() == 0) {
            return response.getAccessToken();
        }
        return null;
    }
}
