package com.common.client;

import java.io.File;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.common.constant.App;

/**
 * <AUTHOR>
 * @date 2021/9/6 14:20
 */
public class ALiOSSClient {

	public final Logger logger = LoggerFactory.getLogger(this.getClass());

	public final String BUCKET_NAME = App.FILE_BUCKET_NAME;
	// yourEndpoint填写Bucket所在地域对应的Endpoint。以华东1（杭州）为例，Endpoint填写为https://oss-cn-hangzhou.aliyuncs.com。
	public static final String LAN_ENDPOINT = App.FILE_LAN_ENDPOINT;
	public static final String ENDPOINT = App.FILE_ENDPOINT;
	// 阿里云账号AccessKey拥有所有API的访问权限，风险很高。强烈建议您创建并使用RAM用户进行API访问或日常运维，请登录RAM控制台创建RAM用户。
	private final String accessKeyId = App.ALI_CLOUD_KEY_ID;
	private final String accessKeySecret = App.ALI_CLOUD_KEY_SECRET;
	// 临时url有效期
	private final Integer day = 30;


	/**
	 *  文件上传
	 * @param fileName Object 完整路径，包含后缀名
	 * @param inputStream 输入流
	 * <AUTHOR>
	 */
	public String upload(String fileName,InputStream inputStream){
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(LAN_ENDPOINT, accessKeyId, accessKeySecret);
		// 依次填写Bucket名称（例如examplebucket）和Object完整路径（例如exampledir/exampleobject.txt）。Object完整路径中不能包含Bucket名称。
		ossClient.putObject(BUCKET_NAME, fileName, inputStream);
		// 关闭OSSClient。
		ossClient.shutdown();
		return "https://" + BUCKET_NAME + "." + ENDPOINT + "/" + fileName;
	}

	/**
	 * 文件下载到本地
	 * @param fileName
	 * @param file
	 */
	public void download(String fileName,File file){
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(LAN_ENDPOINT, accessKeyId, accessKeySecret);
		// 下载Object到本地文件，并保存到指定的本地路径中。如果指定的本地文件存在会覆盖，不存在则新建。
		// 如果未指定本地路径，则下载后的文件默认保存到示例程序所属项目对应本地路径中。
		ossClient.getObject(new GetObjectRequest(BUCKET_NAME, fileName),file);
		ossClient.shutdown();
	}


	/**
	 * 生成预签名
	 * @param fileName
	 */
	public String generatePresignedUrl(String fileName){
		return generatePresignedUrl(fileName,60 * 1000L);
	}

	/**
	 * 生成预签名
	 * @param fileName object名字
	 * @param duration 有效时长，单位毫秒
	 */
	public String generatePresignedUrl(String fileName,long duration){
		com.aliyun.oss.ClientBuilderConfiguration clientBuilderConfiguration = new com.aliyun.oss.ClientBuilderConfiguration();
		clientBuilderConfiguration.setSignatureVersion(com.aliyun.oss.common.comm.SignVersion.V2);
		// 强制使用HTTPS协议
		clientBuilderConfiguration.setProtocol(com.aliyun.oss.common.comm.Protocol.HTTPS);
		// 创建OSSClient实例。
		OSS ossClient = new OSSClientBuilder().build(ENDPOINT,accessKeyId, accessKeySecret,clientBuilderConfiguration);
		// 设置预签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
		Date expiration = new Date(new Date().getTime() + duration);
		// 生成以GET方法访问的预签名URL。本示例没有额外请求头，其他人可以直接通过浏览器访问相关内容。
		URL url = ossClient.generatePresignedUrl(BUCKET_NAME, fileName, expiration);
		ossClient.shutdown();
		return url.toString();
	}


}
