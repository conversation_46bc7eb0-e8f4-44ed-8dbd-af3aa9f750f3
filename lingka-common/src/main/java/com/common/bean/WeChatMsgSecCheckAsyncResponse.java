package com.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 微信消息安全异步检测响应
 */
public class WeChatMsgSecCheckAsyncResponse implements Serializable {
    private static final long serialVersionUID = -4774078717683336885L;

    /**
     * 错误码
     *
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/sec-center/sec-check/mediaCheckAsync.html#%E9%94%99%E8%AF%AF%E7%A0%81">小程序安全/内容安全/多媒体内容安全识别#错误码</a>
     */
    private Integer errcode;

    /**
     * 错误信息
     */
    private String errmsg;

    /**
     * 唯一请求标识，标记单次请求，用于匹配异步推送结果
     */
    @JsonProperty("trace_id")
    private String traceId;

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }
}
