package com.common.bean;

public class TiandituGeocoderLocation extends Bean {

    private static final long serialVersionUID = -715594607467474668L;

    private String score;

    /**
     * 类别名称 非必须返回
     */
    private String level;

    /**
     * 坐标点显示经度 必须返回
     */
    private String lon;
    /**
     * 坐标点显示纬度 必须返回
     */
    private String lat;


    private String keyWord;

    public String getScore() {
        return score;
    }

    public void setScore(String score) {
        this.score = score;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getKeyWord() {
        return keyWord;
    }

    public void setKeyWord(String keyWord) {
        this.keyWord = keyWord;
    }
}
