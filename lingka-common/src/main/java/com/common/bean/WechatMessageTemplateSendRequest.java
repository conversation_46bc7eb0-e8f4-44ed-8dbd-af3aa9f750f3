package com.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WechatMessageTemplateSendRequest extends Bean{

    private static final long serialVersionUID = 2428038068058184466L;
    private String touser;

    @JsonProperty("template_id")
    private String templateId;

    private String url;

    @JsonProperty("miniprogram")
    private WechatMiniProgram miniProgram;

    @JsonProperty("client_msg_id")
    private String clientMsgId;

    private WechatMessageTemplateData data;

    public String getTouser() {
        return touser;
    }

    public void setTouser(String touser) {
        this.touser = touser;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public WechatMiniProgram getMiniProgram() {
        return miniProgram;
    }

    public void setMiniProgram(WechatMiniProgram miniProgram) {
        this.miniProgram = miniProgram;
    }

    public String getClientMsgId() {
        return clientMsgId;
    }

    public void setClientMsgId(String clientMsgId) {
        this.clientMsgId = clientMsgId;
    }

    public WechatMessageTemplateData getData() {
        return data;
    }

    public void setData(WechatMessageTemplateData data) {
        this.data = data;
    }
}
