package com.common.bean;

import java.io.Serializable;
import java.util.List;

public class UserMessagePayload implements Serializable {
    private static final long serialVersionUID = 1L;

    private String catalog;

    private List<Long> departmentIds;

    private List<Long> userIds;

    private Long departmentId;

    //允许亲友参加(0:允许 1:不允许)
    private String enterFamily;

    private Long jumpId;

    //标题
    private String title;

    public List<Long> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Long> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public String getEnterFamily() {
        return enterFamily;
    }

    public void setEnterFamily(String enterFamily) {
        this.enterFamily = enterFamily;
    }

    public Long getJumpId() {
        return jumpId;
    }

    public void setJumpId(Long jumpId) {
        this.jumpId = jumpId;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
