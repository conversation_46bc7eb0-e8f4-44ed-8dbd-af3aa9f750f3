package com.common.bean;

import java.util.Date;

public class JsonWebToken {
	private String id;
	/**
	 * 签发时间
	 */
	private Date issuedAt;
	/**
	 * 主题
	 */
	private String subject;
	/**
	 * 签发人
	 */
	private String issuer;
	/**
	 * 过期时间
	 */
	private Date expirationTime;
	/**
	 * 用户编号
	 */
	private Long userId;
	/**
	 * 类型
	 */
	private String type;
	/**
	 * 过期时间
	 */
	private Date expiration;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Date getIssuedAt() {
		return issuedAt;
	}

	public void setIssuedAt(Date issuedAt) {
		this.issuedAt = issuedAt;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getIssuer() {
		return issuer;
	}

	public void setIssuer(String issuer) {
		this.issuer = issuer;
	}

	public Date getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(Date expirationTime) {
		this.expirationTime = expirationTime;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public Date getExpiration() {
		return expiration;
	}

	public void setExpiration(Date expiration) {
		this.expiration = expiration;
	}

}
