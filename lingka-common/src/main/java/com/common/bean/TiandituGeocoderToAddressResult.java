package com.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TiandituGeocoderToAddressResult extends Bean {
    private static final long serialVersionUID = -6299302375371511656L;

    /**
     * 详细地址
     */
    @JsonProperty("formatted_address")
    private String formattedAddress;

    /**
     * 此点坐标
     */
    private TiandituGeocoderLocation location;

    /**
     * 此点的具体信息（分类）
     */
    private TiandituGeocoderAddressComponent addressComponent;

    public String getFormattedAddress() {
        return formattedAddress;
    }

    public void setFormattedAddress(String formattedAddress) {
        this.formattedAddress = formattedAddress;
    }

    public TiandituGeocoderLocation getLocation() {
        return location;
    }

    public void setLocation(TiandituGeocoderLocation location) {
        this.location = location;
    }

    public TiandituGeocoderAddressComponent getAddressComponent() {
        return addressComponent;
    }

    public void setAddressComponent(TiandituGeocoderAddressComponent addressComponent) {
        this.addressComponent = addressComponent;
    }
}
