package com.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;

/**
 * 微信消息安全异步检测请求
 */
public class WeChatMsgSecCheckAsyncRequest implements Serializable {
    private static final long serialVersionUID = 7760966286565720314L;

    /**
     * 要检测的图片或音频的url，支持图片格式包括jpg, jpeg, png, bmp, gif（取首帧），支持的音频格式包括mp3, aac, ac3, wma, flac, vorbis, opus, wav
     */
    @JsonProperty("media_url")
    private String mediaUrl;

    /**
     * 1:音频;2:图片
     */
    @JsonProperty("media_type")
    private Integer mediaType;

    /**
     * 接口版本号，2.0版本为固定值2
     */
    private Integer version = 2;

    /**
     * 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
     */
    private Integer scene;

    /**
     * 用户的openid（用户需在近两小时访问过小程序）
     */
    private String openid;

    public String getMediaUrl() {
        return mediaUrl;
    }

    public void setMediaUrl(String mediaUrl) {
        this.mediaUrl = mediaUrl;
    }

    public Integer getMediaType() {
        return mediaType;
    }

    public void setMediaType(Integer mediaType) {
        this.mediaType = mediaType;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }
}
