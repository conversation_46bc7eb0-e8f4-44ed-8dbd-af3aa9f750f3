package com.common.bean;

public interface ResponseHandler {
	String OK = "0";
	String SUCCESS = "操作成功";
	String ERROR = "1";
	String FAILURE = "操作失败";
	String FORBIDDEN = "1000";
	String FORBIDDEN_MESSAGE = "无权限";
	String TOKEN_TIMEOUT = "1001";
	String TOKEN_TIMEOUT_MESSAGE = "用户令牌过期";
	String LOGOUT = "1002";
	String LOGOUT_MESSAGE = "请重新登录";
	String FLUSH = "1003";
	String FLUSH_MESSAGE = "请按 Ctrl + F5 刷新页面";
	String USER_NOT_EXIST = "1004";
	String USER_NOT_EXIST_MESSAGE = "用户不存在，请先注册";
}
