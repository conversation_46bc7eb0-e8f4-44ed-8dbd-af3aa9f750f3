package com.common.bean;

public class TiandituGeocoderToAddressResponse extends Bean {
    private static final long serialVersionUID = -6926789977881438087L;

    /**
     * 状态（0：正确，1：错误，404：出错。）
     */
    private String status;

    /**
     * 响应信息是否有（OK：有信息）
     */
    private String msg;

    /**
     * 响应的具体信息
     */
    private TiandituGeocoderToAddressResult result;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public TiandituGeocoderToAddressResult getResult() {
        return result;
    }

    public void setResult(TiandituGeocoderToAddressResult result) {
        this.result = result;
    }
}
