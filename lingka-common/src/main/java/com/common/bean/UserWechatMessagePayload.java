package com.common.bean;

import java.io.Serializable;
import java.util.List;

public class UserWechatMessagePayload implements Serializable {
    private static final long serialVersionUID = 1L;

    private String catalog;

    private List<Long> departmentIds;

    private List<Long> userIds;

    public String getCatalog() {
        return catalog;
    }

    public void setCatalog(String catalog) {
        this.catalog = catalog;
    }

    public List<Long> getDepartmentIds() {
        return departmentIds;
    }

    public void setDepartmentIds(List<Long> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }
}
