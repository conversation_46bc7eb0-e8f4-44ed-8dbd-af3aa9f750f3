package com.common.bean;


public class Response<T> extends <PERSON> implements ResponseHandler {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 响应编码
	 */
	private String code = OK;

	/**
	 * 响应消息
	 */
	private String message = SUCCESS;

	/**
	 * 响应数据
	 */
	private T data;

	/**
	 * 请求编号
	 */
	private String requestId;
	
	public Response(){
	}
	public Response(String code, String message){
		this.code = code;
		this.message = message;
	}
	public Response(T data){
		this.data = data;
	}
	public Response(String code, String message, T data){
		this.data = data;
		this.code = code;
		this.message = message;
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getMessage() {
		return message;
	}
	public void setMessage(String message) {
		this.message = message;
	}
	public T getData() {
		return data;
	}
	public void setData(T data) {
		this.data = data;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}
}
