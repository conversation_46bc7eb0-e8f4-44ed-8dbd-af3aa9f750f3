package com.common.bean;

import java.io.Serializable;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WeChatMsgSecCheckResponse implements Serializable {
    private static final long serialVersionUID = 787877611187562333L;

    /**
     * 错误码
     *
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/sec-center/sec-check/msgSecCheck.html#%E9%94%99%E8%AF%AF%E7%A0%81">小程序安全/内容安全/文本内容安全识别#错误码</a>
     */
    private Integer errcode;

    /**
     * 错误信息
     *
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/sec-center/sec-check/msgSecCheck.html#%E9%94%99%E8%AF%AF%E7%A0%81">小程序安全/内容安全/文本内容安全识别#错误码</a>
     */
    private String errmsg;

    /**
     * 详细检测结果
     */
    private List<MsgSecCheckDetail> detail;

    /**
     * 唯一请求标识，标记单次请求
     */
    @JsonProperty("trace_id")
    private String traceId;

    /**
     * 综合结果
     */
    private MsgSecCheckResult result;

    public Integer getErrcode() {
        return errcode;
    }

    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public List<MsgSecCheckDetail> getDetail() {
        return detail;
    }

    public void setDetail(List<MsgSecCheckDetail> detail) {
        this.detail = detail;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public MsgSecCheckResult getResult() {
        return result;
    }

    public void setResult(MsgSecCheckResult result) {
        this.result = result;
    }
}
