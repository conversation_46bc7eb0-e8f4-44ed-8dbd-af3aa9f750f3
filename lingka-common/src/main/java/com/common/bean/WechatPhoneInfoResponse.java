package com.common.bean;

import java.io.Serializable;

public class WechatPhoneInfoResponse implements Serializable {

    private static final long serialVersionUID = 7856808800231019463L;

    /**
     * 用户绑定的手机号（国外手机号会有区号）
     */
    private String phoneNumber;

    /**
     * 	没有区号的手机号
     */
    private String purePhoneNumber;

    /**
     * 区号
     */
    private String countryCode;

    /**
     * 数据水印
     */
    private WechatPhoneInfoWatermarkResponse watermark;

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPurePhoneNumber() {
        return purePhoneNumber;
    }

    public void setPurePhoneNumber(String purePhoneNumber) {
        this.purePhoneNumber = purePhoneNumber;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public WechatPhoneInfoWatermarkResponse getWatermark() {
        return watermark;
    }

    public void setWatermark(WechatPhoneInfoWatermarkResponse watermark) {
        this.watermark = watermark;
    }
}
