package com.common.bean;

import java.io.Serializable;

/**
 * 微信内容安全检测请求参数
 */
public class WeChatMsgSecCheckRequest implements Serializable {
    private static final long serialVersionUID = -7478517492369548006L;

    /**
     * 需检测的文本内容，UTF-8编码，最多2500字
     */
    private String content;

    /**
     * 接口版本号，固定为2
     */
    private Integer version = 2;

    /**
     * 场景枚举值（1资料；2评论；3论坛；4社交日志）
     */
    private Integer scene;

    /**
     * 用户openid（需在近两小时访问过小程序）
     */
    private String openid;

    /**
     * 文本标题，UTF-8编码
     */
    private String title;

    /**
     * 用户昵称，UTF-8编码
     */
    private String nickname;

    /**
     * 个性签名，仅scene=1时有效，UTF-8编码
     */
    private String signature;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }

    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }
}
