package com.common.bean;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TiandituGeocoderAddressComponent extends Bean {
    private static final long serialVersionUID = 4214009645347173045L;

    /**
     * 此点最近地点信息
     */
    private String address;

    /**
     * 此点距离最近地点信息距离
     */
    @JsonProperty("address_distance")
    private Integer addressDistance;

    /**
     * 此点在最近地点信息方向
     */
    @JsonProperty("address_position")
    private String addressPosition;

    /**
     * 此点所在国家或城市或区县
     */
    private String city;

    /**
     * 此点所在国家或城市或区县的编码
     */
    @JsonProperty("city_code")
    private String cityCode;

    /**
     * 距离此点最近poi点
     */
    private String poi;

    /**
     * 距离此点最近poi点的距离
     */
    @JsonProperty("poi_distance")
    private Integer poiDistance;

    /**
     * 此点在最近poi点的方向
     */
    @JsonProperty("poi_position")
    private String poiPosition;

    /**
     * 距离此点最近的路
     */
    private String road;

    /**
     * 此点距离此路的距离
     */
    @JsonProperty("road_distance")
    private Integer roadDistance;

    /**
     * 国家
     */
    private String nation;

    /**
     * 省份
     */
    private String province;

    /**
     * 省份编码
     */
    @JsonProperty("province_code")
    private String provinceCode;

    /**
     * 区县
     */
    private String county;

    /**
     * 区县编码
     */
    @JsonProperty("county_code")
    private String countyCode;

    /**
     * 乡镇
     */
    private String town;

    /**
     * 乡镇编码
     */
    @JsonProperty("town_code")
    private String townCode;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getAddressDistance() {
        return addressDistance;
    }

    public void setAddressDistance(Integer addressDistance) {
        this.addressDistance = addressDistance;
    }

    public String getAddressPosition() {
        return addressPosition;
    }

    public void setAddressPosition(String addressPosition) {
        this.addressPosition = addressPosition;
    }

    public String getCity() {
        return city.isEmpty() ? province : city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getPoi() {
        return poi;
    }

    public void setPoi(String poi) {
        this.poi = poi;
    }

    public Integer getPoiDistance() {
        return poiDistance;
    }

    public void setPoiDistance(Integer poiDistance) {
        this.poiDistance = poiDistance;
    }

    public String getPoiPosition() {
        return poiPosition;
    }

    public void setPoiPosition(String poiPosition) {
        this.poiPosition = poiPosition;
    }

    public String getRoad() {
        return road;
    }

    public void setRoad(String road) {
        this.road = road;
    }

    public Integer getRoadDistance() {
        return roadDistance;
    }

    public void setRoadDistance(Integer roadDistance) {
        this.roadDistance = roadDistance;
    }

    public String getCityCode() {
        return cityCode.isEmpty() ? provinceCode : cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCounty() {
        return county;
    }

    public void setCounty(String county) {
        this.county = county;
    }

    public String getCountyCode() {
        return countyCode;
    }

    public void setCountyCode(String countyCode) {
        this.countyCode = countyCode;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getTownCode() {
        return townCode;
    }

    public void setTownCode(String townCode) {
        this.townCode = townCode;
    }
}
