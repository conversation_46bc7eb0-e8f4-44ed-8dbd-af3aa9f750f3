package com.common.bean;

import com.common.util.LbsUtil;

public class TiandituGeocoderToAddressRequest extends Bean {
    private static final long serialVersionUID = -6696358565488528018L;
    /**
     * 坐标的x值
     */
    private String lon;

    /**
     * 坐标的y值
     */
    private String lat;

    /**
     * 接口版本
     */
    private String ver = "1";

    public TiandituGeocoderToAddressRequest(LbsUtil.Point point) {
        this.lon = String.valueOf(point.lon);
        this.lat = String.valueOf(point.lat);
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getVer() {
        return ver;
    }

    public void setVer(String ver) {
        this.ver = ver;
    }
}
