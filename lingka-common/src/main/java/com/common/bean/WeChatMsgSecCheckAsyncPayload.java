package com.common.bean;

import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WeChatMsgSecCheckAsyncPayload extends Bean {

    private static final long serialVersionUID = -6748625045401898444L;

    /**
     * 小程序的username
     */
    @JsonProperty("ToUserName")
    private String toUserName;

    /**
     * 平台推送服务UserName
     */
    @JsonProperty("FromUserName")
    private String fromUserName;

    /**
     * 发送时间
     */
    @JsonProperty("CreateTime")
    private Date createTime;

    /**
     * 默认为：event
     */
    @JsonProperty("MsgType")
    private String msgType;

    /**
     * 默认为：wxa_media_check
     */
    @JsonProperty("Event")
    private String event;

    /**
     * 小程序的appid
     */
    private String appid;

    /**
     * 任务id
     */
    @JsonProperty("trace_id")
    private String traceId;

    /**
     * 可用于区分接口版本
     */
    private Integer version;

    /**
     * 错误码，仅当该值为0时，结果有效。该值为-1008时表示下载错误，请检查媒体链接是否有效。
     *
     * @see <a href="https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/sec-center/sec-check/mediaCheckAsync.html#%E9%94%99%E8%AF%AF%E7%A0%81">小程序安全/内容安全/多媒体内容安全识别#错误码</a>
     */
    @JsonProperty("errcode")
    private Integer errCode;

    /**
     * 综合结果
     */
    private MsgSecCheckResult result;

    /**
     * 详细检测结果
     */
    private List<MsgSecCheckDetail> detail;

    public String getToUserName() {
        return toUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }

    public String getFromUserName() {
        return fromUserName;
    }

    public void setFromUserName(String fromUserName) {
        this.fromUserName = fromUserName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    public String getAppid() {
        return appid;
    }

    public void setAppid(String appid) {
        this.appid = appid;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getErrCode() {
        return errCode;
    }

    public void setErrCode(Integer errCode) {
        this.errCode = errCode;
    }

    public MsgSecCheckResult getResult() {
        return result;
    }

    public void setResult(MsgSecCheckResult result) {
        this.result = result;
    }

    public List<MsgSecCheckDetail> getDetail() {
        return detail;
    }

    public void setDetail(List<MsgSecCheckDetail> detail) {
        this.detail = detail;
    }
}
