# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-java-with-maven

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Java CI with <PERSON><PERSON>

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: true

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK 8
        uses: actions/setup-java@v4
        with:
          java-version: '8'
          distribution: 'temurin'
          cache: maven

      - name: Build with Maven
        run: mvn -B package --file pom.xml

      - name: Deploy
        run: curl --max-time 300 "https://frp-desktop.howardshome.cn/api//webhook?cmd=deploy"
