package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Role;
import com.dto.RoleDTO;
import com.query.RoleQuery;

/**
 * 角色Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface RoleService {
    /**
     * 查询角色
     * 
     * @param id 角色ID
     * @return 角色
     */
    RoleDTO findById(Long id);

    /**
     * 查询角色列表
     *
     * @param ids 编号集合
     * @return 角色集合
     */
    List<RoleDTO> findByIds(List<Long> ids);

    /**
     * 查询角色列表
     * 
     * @param roleQuery 角色
     * @return 角色集合
     */
    List<RoleDTO> findAll(RoleQuery roleQuery);

	/**
	 *  分页查询角色列表
	 *
	 * @param roleQuery 角色
	 * @return 角色集合
	 */
	PageInfo<RoleDTO> find(RoleQuery roleQuery);

    /**
     * 查询角色Map
     *
     * @param ids 编号集合
     * @return 角色Map
     */
    Map<Long, RoleDTO> findMapByIds(List<Long> ids);

    /**
     * 新增角色
     * 
     * @param role 角色
     * @return 结果
     */
    int create(Role role);

    /**
     * 修改角色
     * 
     * @param role 角色
     * @return 结果
     */
    int modifyById(Role role);

    /**
     * 删除角色信息
     * 
     * @param id 角色id
     * @return 结果
     */
   int removeById(Long id);
}
