package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.OrderProduct;
import com.dto.OrderProductDTO;
import com.query.OrderProductQuery;

/**
 * 订单产品Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */
public interface OrderProductService {
    /**
     * 查询订单产品
     * 
     * @param id 订单产品ID
     * @return 订单产品
     */
    OrderProductDTO findById(Long id);

    /**
     * 查询订单产品列表
     *
     * @param ids 编号集合
     * @return 订单产品集合
     */
    List<OrderProductDTO> findByIds(List<Long> ids);

    /**
     * 查询订单产品列表
     * 
     * @param orderProductQuery 订单产品
     * @return 订单产品集合
     */
    List<OrderProductDTO> findAll(OrderProductQuery orderProductQuery);

	/**
	 *  分页查询订单产品列表
	 *
	 * @param orderProductQuery 订单产品
	 * @return 订单产品集合
	 */
	PageInfo<OrderProductDTO> find(OrderProductQuery orderProductQuery);

    /**
     * 查询订单产品Map
     *
     * @param ids 编号集合
     * @return 订单产品Map
     */
    Map<Long, OrderProductDTO> findMapByIds(List<Long> ids);

    /**
     * 新增订单产品
     * 
     * @param orderProduct 订单产品
     * @return 结果
     */
    int create(OrderProduct orderProduct);

    /**
     * 修改订单产品
     * 
     * @param orderProduct 订单产品
     * @return 结果
     */
    int modifyById(OrderProduct orderProduct);

    /**
     * 删除订单产品信息
     * 
     * @param id 订单产品id
     * @return 结果
     */
   int removeById(Long id);
}
