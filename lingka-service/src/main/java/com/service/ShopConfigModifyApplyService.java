package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ShopConfigModifyApply;
import com.dto.ShopConfigModifyApplyDTO;
import com.query.ShopConfigModifyApplyQuery;

/**
 * 店铺配置修改申请Service接口
 *
 * <AUTHOR>
 */
public interface ShopConfigModifyApplyService {
    /**
     * 查询店铺配置修改申请
     *
     * @param id 店铺配置修改申请ID
     * @return 店铺配置修改申请
     */
    ShopConfigModifyApplyDTO findById(Long id);

    /**
     * 查询店铺配置修改申请列表
     *
     * @param ids 编号集合
     * @return 店铺配置修改申请集合
     */
    List<ShopConfigModifyApplyDTO> findByIds(List<Long> ids);

    /**
     * 通过店铺 ID 查询店铺配置修改申请列表
     *
     * @param id 店铺 ID
     * @return 店铺配置修改申请集合
     */
    List<ShopConfigModifyApplyDTO> findByShopId(Long id);

    /**
     * 查询店铺配置修改申请列表
     *
     * @param shopConfigModifyApplyQuery 店铺配置修改申请
     * @return 店铺配置修改申请集合
     */
    List<ShopConfigModifyApplyDTO> findAll(ShopConfigModifyApplyQuery shopConfigModifyApplyQuery);

    /**
     * 分页查询店铺配置修改申请列表
     *
     * @param shopConfigModifyApplyQuery 店铺配置修改申请
     * @return 店铺配置修改申请集合
     */
    PageInfo<ShopConfigModifyApplyDTO> find(ShopConfigModifyApplyQuery shopConfigModifyApplyQuery);

    /**
     * 查询店铺配置修改申请Map
     *
     * @param ids 编号集合
     * @return 店铺配置修改申请Map
     */
    Map<Long, ShopConfigModifyApplyDTO> findMapByIds(List<Long> ids);

    /**
     * 新增店铺配置修改申请
     *
     * @param shopConfigModifyApply 店铺配置修改申请
     * @return 结果
     */
    int create(ShopConfigModifyApply shopConfigModifyApply);

    /**
     * 修改店铺配置修改申请
     *
     * @param shopConfigModifyApply 店铺配置修改申请
     * @return 结果
     */
    int modifyById(ShopConfigModifyApply shopConfigModifyApply);

    /**
     * 删除店铺配置修改申请信息
     *
     * @param id 店铺配置修改申请id
     * @return 结果
     */
    int removeById(Long id);

    /**
     * 删除店铺配置修改申请信息
     *
     * @param ids 店铺配置修改申请id集合
     * @return 结果
     */
    int removeByIds(List<Long> ids);
}
