package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ProductOption;
import com.dto.ProductOptionDTO;
import com.query.ProductOptionQuery;

/**
 * 产品选项Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ProductOptionService {
    /**
     * 查询产品选项
     * 
     * @param id 产品选项ID
     * @return 产品选项
     */
    ProductOptionDTO findById(Long id);

    /**
     * 查询产品选项列表
     *
     * @param ids 编号集合
     * @return 产品选项集合
     */
    List<ProductOptionDTO> findByIds(List<Long> ids);

    /**
     * 查询产品选项列表
     * 
     * @param productOptionQuery 产品选项
     * @return 产品选项集合
     */
    List<ProductOptionDTO> findAll(ProductOptionQuery productOptionQuery);

	/**
	 *  分页查询产品选项列表
	 *
	 * @param productOptionQuery 产品选项
	 * @return 产品选项集合
	 */
	PageInfo<ProductOptionDTO> find(ProductOptionQuery productOptionQuery);

    /**
     * 查询产品选项Map
     *
     * @param ids 编号集合
     * @return 产品选项Map
     */
    Map<Long, ProductOptionDTO> findMapByIds(List<Long> ids);

    /**
     * 新增产品选项
     * 
     * @param productOption 产品选项
     * @return 结果
     */
    int create(ProductOption productOption);

    /**
     * 修改产品选项
     * 
     * @param productOption 产品选项
     * @return 结果
     */
    int modifyById(ProductOption productOption);

    /**
     * 删除产品选项信息
     * 
     * @param id 产品选项id
     * @return 结果
     */
   int removeById(Long id);
}
