package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ProductIngredient;
import com.dto.ProductIngredientDTO;
import com.query.ProductIngredientQuery;

/**
 * 产品小料Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ProductIngredientService {
    /**
     * 查询产品小料
     * 
     * @param id 产品小料ID
     * @return 产品小料
     */
    ProductIngredientDTO findById(Long id);

    /**
     * 查询产品小料列表
     *
     * @param ids 编号集合
     * @return 产品小料集合
     */
    List<ProductIngredientDTO> findByIds(List<Long> ids);

    /**
     * 查询产品小料列表
     * 
     * @param productIngredientQuery 产品小料
     * @return 产品小料集合
     */
    List<ProductIngredientDTO> findAll(ProductIngredientQuery productIngredientQuery);

	/**
	 *  分页查询产品小料列表
	 *
	 * @param productIngredientQuery 产品小料
	 * @return 产品小料集合
	 */
	PageInfo<ProductIngredientDTO> find(ProductIngredientQuery productIngredientQuery);

    /**
     * 查询产品小料Map
     *
     * @param ids 编号集合
     * @return 产品小料Map
     */
    Map<Long, ProductIngredientDTO> findMapByIds(List<Long> ids);

    /**
     * 新增产品小料
     * 
     * @param productIngredient 产品小料
     * @return 结果
     */
    int create(ProductIngredient productIngredient);

    /**
     * 修改产品小料
     * 
     * @param productIngredient 产品小料
     * @return 结果
     */
    int modifyById(ProductIngredient productIngredient);

    /**
     * 删除产品小料信息
     * 
     * @param id 产品小料id
     * @return 结果
     */
   int removeById(Long id);
}
