package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Province;
import com.dto.ProvinceDTO;
import com.query.ProvinceQuery;

/**
 * 省份Service接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface ProvinceService {
    /**
     * 查询省份
     *
     * @param id 省份ID
     * @return 省份
     */
    ProvinceDTO findById(Long id);

    /**
     * 查询省份列表
     *
     * @param ids 编号集合
     * @return 省份集合
     */
    List<ProvinceDTO> findByIds(List<Long> ids);

    /**
     * 查询省份列表
     *
     * @param provinceQuery 省份
     * @return 省份集合
     */
    List<ProvinceDTO> findAll(ProvinceQuery provinceQuery);

    /**
     * 分页查询省份列表
     *
     * @param provinceQuery 省份
     * @return 省份集合
     */
    PageInfo<ProvinceDTO> find(ProvinceQuery provinceQuery);

    /**
     * 查询省份Map
     *
     * @param ids 编号集合
     * @return 省份Map
     */
    Map<Long, ProvinceDTO> findMapByIds(List<Long> ids);

    /**
     * 新增省份
     *
     * @param province 省份
     * @return 结果
     */
    int create(Province province);

    /**
     * 修改省份
     *
     * @param province 省份
     * @return 结果
     */
    int modifyById(Province province);

    /**
     * 删除省份信息
     *
     * @param id 省份id
     * @return 结果
     */
    int removeById(Long id);
}
