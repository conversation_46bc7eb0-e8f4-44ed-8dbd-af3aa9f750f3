package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserTicket;
import com.github.pagehelper.PageInfo;
import com.domain.UserTicketGiftRecord;
import com.dto.UserTicketGiftRecordDTO;
import com.query.UserTicketGiftRecordQuery;

/**
 * 用户门票转赠记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
public interface UserTicketGiftRecordService {
    /**
     * 查询用户门票转赠记录
     * 
     * @param id 用户门票转赠记录ID
     * @return 用户门票转赠记录
     */
    UserTicketGiftRecordDTO findById(Long id);

    /**
     * 查询用户门票转赠记录列表
     *
     * @param ids 编号集合
     * @return 用户门票转赠记录集合
     */
    List<UserTicketGiftRecordDTO> findByIds(List<Long> ids);

    /**
     * 查询用户门票转赠记录列表
     * 
     * @param userTicketGiftRecordQuery 用户门票转赠记录
     * @return 用户门票转赠记录集合
     */
    List<UserTicketGiftRecordDTO> findAll(UserTicketGiftRecordQuery userTicketGiftRecordQuery);

	/**
	 *  分页查询用户门票转赠记录列表
	 *
	 * @param userTicketGiftRecordQuery 用户门票转赠记录
	 * @return 用户门票转赠记录集合
	 */
	PageInfo<UserTicketGiftRecordDTO> find(UserTicketGiftRecordQuery userTicketGiftRecordQuery);

    /**
     * 查询用户门票转赠记录Map
     *
     * @param ids 编号集合
     * @return 用户门票转赠记录Map
     */
    Map<Long, UserTicketGiftRecordDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户门票转赠记录
     * 
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    int create(UserTicketGiftRecord userTicketGiftRecord);

    /**
     * 修改用户门票转赠记录
     * 
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    int modifyById(UserTicketGiftRecord userTicketGiftRecord);

    /**
     * 修改用户门票转赠记录
     *
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    int modifyById(UserTicketGiftRecord userTicketGiftRecord, UserTicket userTicket);

    /**
     * 删除用户门票转赠记录信息
     * 
     * @param id 用户门票转赠记录id
     * @return 结果
     */
   int removeById(Long id);
}
