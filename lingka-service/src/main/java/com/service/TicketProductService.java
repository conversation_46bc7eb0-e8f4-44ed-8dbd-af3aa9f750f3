package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.TicketProduct;
import com.dto.TicketProductDTO;
import com.query.TicketProductQuery;

/**
 * 门票产品Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
public interface TicketProductService {
    /**
     * 查询门票产品
     * 
     * @param id 门票产品ID
     * @return 门票产品
     */
    TicketProductDTO findById(Long id);

    /**
     * 查询门票产品列表
     *
     * @param ids 编号集合
     * @return 门票产品集合
     */
    List<TicketProductDTO> findByIds(List<Long> ids);

    /**
     * 查询门票产品列表
     * 
     * @param ticketProductQuery 门票产品
     * @return 门票产品集合
     */
    List<TicketProductDTO> findAll(TicketProductQuery ticketProductQuery);

	/**
	 *  分页查询门票产品列表
	 *
	 * @param ticketProductQuery 门票产品
	 * @return 门票产品集合
	 */
	PageInfo<TicketProductDTO> find(TicketProductQuery ticketProductQuery);

    /**
     * 查询门票产品Map
     *
     * @param ids 编号集合
     * @return 门票产品Map
     */
    Map<Long, TicketProductDTO> findMapByIds(List<Long> ids);

    /**
     * 新增门票产品
     * 
     * @param ticketProduct 门票产品
     * @return 结果
     */
    int create(TicketProduct ticketProduct);

    /**
     * 修改门票产品
     * 
     * @param ticketProduct 门票产品
     * @return 结果
     */
    int modifyById(TicketProduct ticketProduct);

    /**
     * 删除门票产品信息
     * 
     * @param id 门票产品id
     * @return 结果
     */
   int removeById(Long id);
}
