package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ProductSku;
import com.dto.ProductSkuDTO;
import com.query.ProductSkuQuery;

/**
 * 产品SKUService接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ProductSkuService {
    /**
     * 查询产品SKU
     * 
     * @param id 产品SKUID
     * @return 产品SKU
     */
    ProductSkuDTO findById(Long id);

    /**
     * 查询产品SKU列表
     *
     * @param ids 编号集合
     * @return 产品SKU集合
     */
    List<ProductSkuDTO> findByIds(List<Long> ids);

    /**
     * 查询产品SKU列表
     * 
     * @param productSkuQuery 产品SKU
     * @return 产品SKU集合
     */
    List<ProductSkuDTO> findAll(ProductSkuQuery productSkuQuery);

	/**
	 *  分页查询产品SKU列表
	 *
	 * @param productSkuQuery 产品SKU
	 * @return 产品SKU集合
	 */
	PageInfo<ProductSkuDTO> find(ProductSkuQuery productSkuQuery);

    /**
     * 查询产品SKUMap
     *
     * @param ids 编号集合
     * @return 产品SKUMap
     */
    Map<Long, ProductSkuDTO> findMapByIds(List<Long> ids);

    /**
     * 新增产品SKU
     * 
     * @param productSku 产品SKU
     * @return 结果
     */
    int create(ProductSku productSku);

    /**
     * 修改产品SKU
     * 
     * @param productSku 产品SKU
     * @return 结果
     */
    int modifyById(ProductSku productSku);

    /**
     * 删除产品SKU信息
     * 
     * @param id 产品SKUid
     * @return 结果
     */
   int removeById(Long id);
}
