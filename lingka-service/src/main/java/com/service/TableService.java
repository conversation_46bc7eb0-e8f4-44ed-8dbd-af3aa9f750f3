package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Table;
import com.dto.TableDTO;
import com.query.TableQuery;

/**
 * 桌子Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface TableService {
    /**
     * 查询桌子
     * 
     * @param id 桌子ID
     * @return 桌子
     */
    TableDTO findById(Long id);

    /**
     * 查询桌子列表
     *
     * @param ids 编号集合
     * @return 桌子集合
     */
    List<TableDTO> findByIds(List<Long> ids);

    /**
     * 查询桌子列表
     * 
     * @param tableQuery 桌子
     * @return 桌子集合
     */
    List<TableDTO> findAll(TableQuery tableQuery);

	/**
	 *  分页查询桌子列表
	 *
	 * @param tableQuery 桌子
	 * @return 桌子集合
	 */
	PageInfo<TableDTO> find(TableQuery tableQuery);

    /**
     * 查询桌子Map
     *
     * @param ids 编号集合
     * @return 桌子Map
     */
    Map<Long, TableDTO> findMapByIds(List<Long> ids);

    /**
     * 新增桌子
     * 
     * @param table 桌子
     * @return 结果
     */
    int create(Table table);

    /**
     * 修改桌子
     * 
     * @param table 桌子
     * @return 结果
     */
    int modifyById(Table table);

    /**
     * 删除桌子信息
     * 
     * @param id 桌子id
     * @return 结果
     */
   int removeById(Long id);
}
