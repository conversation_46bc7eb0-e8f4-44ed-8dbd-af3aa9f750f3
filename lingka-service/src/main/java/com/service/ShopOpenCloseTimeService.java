package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ShopOpenCloseTime;
import com.dto.ShopOpenCloseTimeDTO;
import com.query.ShopOpenCloseTimeQuery;

/**
 * 店铺营业时间Service接口
 *
 */
public interface ShopOpenCloseTimeService {
    /**
     * 查询店铺营业时间
     *
     * @param id 店铺营业时间ID
     * @return 店铺营业时间
     */
    ShopOpenCloseTimeDTO findById(Long id);

    /**
     * 查询店铺营业时间列表
     *
     * @param ids 编号集合
     * @return 店铺营业时间集合
     */
    List<ShopOpenCloseTimeDTO> findByIds(List<Long> ids);

    /**
     * 查询店铺营业时间列表
     *
     * @param shopOpenCloseTimeQuery 店铺营业时间
     * @return 店铺营业时间集合
     */
    List<ShopOpenCloseTimeDTO> findAll(ShopOpenCloseTimeQuery shopOpenCloseTimeQuery);

    /**
     * 分页查询店铺营业时间列表
     *
     * @param shopOpenCloseTimeQuery 店铺营业时间
     * @return 店铺营业时间集合
     */
    PageInfo<ShopOpenCloseTimeDTO> find(ShopOpenCloseTimeQuery shopOpenCloseTimeQuery);

    /**
     * 查询店铺营业时间Map
     *
     * @param ids 编号集合
     * @return 店铺营业时间Map
     */
    Map<Long, ShopOpenCloseTimeDTO> findMapByIds(List<Long> ids);

    /**
     * 新增店铺营业时间
     *
     * @param shopOpenCloseTime 店铺营业时间
     * @return 结果
     */
    int create(ShopOpenCloseTime shopOpenCloseTime);

    /**
     * 修改店铺营业时间
     *
     * @param shopOpenCloseTime 店铺营业时间
     * @return 结果
     */
    int modifyById(ShopOpenCloseTime shopOpenCloseTime);

    /**
     * 删除店铺营业时间信息
     *
     * @param id 店铺营业时间id
     * @return 删除计数
     */
    int removeById(Long id);

    /**
     * 删除店铺营业时间信息
     *
     * @param shopId 店铺 id
     * @return 删除计数
     */
    int removeByShopId(Long shopId);
}
