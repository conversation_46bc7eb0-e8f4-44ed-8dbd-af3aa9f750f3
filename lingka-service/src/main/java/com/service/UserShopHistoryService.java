package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.UserShopHistory;
import com.dto.UserShopHistoryDTO;
import com.query.UserShopHistoryQuery;

/**
 * 用户浏览店铺历史记录Service接口
 *
 */
public interface UserShopHistoryService {
    /**
     * 查询用户浏览店铺历史记录
     *
     * @param id 用户浏览店铺历史记录ID
     * @return 用户浏览店铺历史记录
     */
    UserShopHistoryDTO findById(Long id);

    /**
     * 查询用户浏览店铺历史记录列表
     *
     * @param ids 编号集合
     * @return 用户浏览店铺历史记录集合
     */
    List<UserShopHistoryDTO> findByIds(List<Long> ids);

    /**
     * 查询用户浏览店铺历史记录列表
     *
     * @param userShopHistoryQuery 用户浏览店铺历史记录
     * @return 用户浏览店铺历史记录集合
     */
    List<UserShopHistoryDTO> findAll(UserShopHistoryQuery userShopHistoryQuery);

    /**
     * 分页查询用户浏览店铺历史记录列表
     *
     * @param userShopHistoryQuery 用户浏览店铺历史记录
     * @return 用户浏览店铺历史记录集合
     */
    PageInfo<UserShopHistoryDTO> find(UserShopHistoryQuery userShopHistoryQuery);

    /**
     * 查询用户浏览店铺历史记录Map
     *
     * @param ids 编号集合
     * @return 用户浏览店铺历史记录Map
     */
    Map<Long, UserShopHistoryDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户浏览店铺历史记录
     *
     * @param userShopHistory 用户浏览店铺历史记录
     * @return 结果
     */
    int create(UserShopHistory userShopHistory);

    /**
     * 修改用户浏览店铺历史记录
     *
     * @param userShopHistory 用户浏览店铺历史记录
     * @return 结果
     */
    int modifyById(UserShopHistory userShopHistory);

    /**
     * 删除用户浏览店铺历史记录信息
     *
     * @param id 用户浏览店铺历史记录id
     * @return 结果
     */
    int removeById(Long id);

    /**
     * 删除用户浏览店铺历史记录信息
     *
     * @param shopId 店铺id
     * @return 删除计数
     */
    int removeByShopId(Long shopId);

    /**
     * 删除用户浏览店铺历史记录信息
     *
     * @param userId 用户 id
     * @return 删除计数
     */
    int removeByUserId(Long userId);
}
