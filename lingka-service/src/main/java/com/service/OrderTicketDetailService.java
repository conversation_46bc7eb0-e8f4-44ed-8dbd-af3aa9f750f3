package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.OrderTicketDetail;
import com.dto.OrderTicketDetailDTO;
import com.query.OrderTicketDetailQuery;

/**
 * 订单门票详情Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
public interface OrderTicketDetailService {
    /**
     * 查询订单门票详情
     * 
     * @param id 订单门票详情ID
     * @return 订单门票详情
     */
    OrderTicketDetailDTO findById(Long id);

    /**
     * 查询订单门票详情列表
     *
     * @param ids 编号集合
     * @return 订单门票详情集合
     */
    List<OrderTicketDetailDTO> findByIds(List<Long> ids);

    /**
     * 查询订单门票详情列表
     * 
     * @param orderTicketDetailQuery 订单门票详情
     * @return 订单门票详情集合
     */
    List<OrderTicketDetailDTO> findAll(OrderTicketDetailQuery orderTicketDetailQuery);

	/**
	 *  分页查询订单门票详情列表
	 *
	 * @param orderTicketDetailQuery 订单门票详情
	 * @return 订单门票详情集合
	 */
	PageInfo<OrderTicketDetailDTO> find(OrderTicketDetailQuery orderTicketDetailQuery);

    /**
     * 查询订单门票详情Map
     *
     * @param ids 编号集合
     * @return 订单门票详情Map
     */
    Map<Long, OrderTicketDetailDTO> findMapByIds(List<Long> ids);

    /**
     * 新增订单门票详情
     * 
     * @param orderTicketDetail 订单门票详情
     * @return 结果
     */
    int create(OrderTicketDetail orderTicketDetail);

    /**
     * 修改订单门票详情
     * 
     * @param orderTicketDetail 订单门票详情
     * @return 结果
     */
    int modifyById(OrderTicketDetail orderTicketDetail);

    /**
     * 删除订单门票详情信息
     * 
     * @param id 订单门票详情id
     * @return 结果
     */
   int removeById(Long id);
}
