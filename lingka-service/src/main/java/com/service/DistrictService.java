package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.District;
import com.dto.DistrictDTO;
import com.query.DistrictQuery;

/**
 * 区(县)Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface DistrictService {
    /**
     * 查询区(县)
     * 
     * @param id 区(县)ID
     * @return 区(县)
     */
    DistrictDTO findById(Long id);

    /**
     * 查询区(县)列表
     *
     * @param ids 编号集合
     * @return 区(县)集合
     */
    List<DistrictDTO> findByIds(List<Long> ids);

    /**
     * 查询区(县)列表
     * 
     * @param districtQuery 区(县)
     * @return 区(县)集合
     */
    List<DistrictDTO> findAll(DistrictQuery districtQuery);

	/**
	 *  分页查询区(县)列表
	 *
	 * @param districtQuery 区(县)
	 * @return 区(县)集合
	 */
	PageInfo<DistrictDTO> find(DistrictQuery districtQuery);

    /**
     * 查询区(县)Map
     *
     * @param ids 编号集合
     * @return 区(县)Map
     */
    Map<Long, DistrictDTO> findMapByIds(List<Long> ids);

    /**
     * 新增区(县)
     * 
     * @param district 区(县)
     * @return 结果
     */
    int create(District district);

    /**
     * 修改区(县)
     * 
     * @param district 区(县)
     * @return 结果
     */
    int modifyById(District district);

    /**
     * 删除区(县)信息
     * 
     * @param id 区(县)id
     * @return 结果
     */
   int removeById(Long id);
}
