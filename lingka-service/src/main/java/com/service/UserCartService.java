package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.UserCart;
import com.dto.UserCartDTO;
import com.query.UserCartQuery;

/**
 * 用户购物车Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
public interface UserCartService {
    /**
     * 查询用户购物车
     * 
     * @param id 用户购物车ID
     * @return 用户购物车
     */
    UserCartDTO findById(Long id);

    /**
     * 查询用户购物车列表
     *
     * @param ids 编号集合
     * @return 用户购物车集合
     */
    List<UserCartDTO> findByIds(List<Long> ids);

    /**
     * 查询用户购物车列表
     * 
     * @param userCartQuery 用户购物车
     * @return 用户购物车集合
     */
    List<UserCartDTO> findAll(UserCartQuery userCartQuery);

	/**
	 *  分页查询用户购物车列表
	 *
	 * @param userCartQuery 用户购物车
	 * @return 用户购物车集合
	 */
	PageInfo<UserCartDTO> find(UserCartQuery userCartQuery);

    /**
     * 查询用户购物车Map
     *
     * @param ids 编号集合
     * @return 用户购物车Map
     */
    Map<Long, UserCartDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户购物车
     * 
     * @param userCart 用户购物车
     * @return 结果
     */
    int create(UserCart userCart);

    /**
     * 修改用户购物车
     * 
     * @param userCart 用户购物车
     * @return 结果
     */
    int modifyById(UserCart userCart);

    /**
     * 批量修改用户购物车
     *
     * @param userCarts 用户购物车
     * @return 结果
     */
    int batchModifyById(List<UserCart> userCarts);

    /**
     * 删除用户购物车信息
     * 
     * @param id 用户购物车id
     * @return 结果
     */
   int removeById(Long id);
}
