package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Audit;
import com.dto.AuditDTO;
import com.query.AuditQuery;

/**
 * 审核结果记录Service接口
 *
 * <AUTHOR>
 */
public interface AuditService {
    /**
     * 查询审核结果记录
     *
     * @param id 审核结果记录ID
     * @return 审核结果记录
     */
    AuditDTO findById(Long id);

    /**
     * 查询审核结果记录列表
     *
     * @param ids 编号集合
     * @return 审核结果记录集合
     */
    List<AuditDTO> findByIds(List<Long> ids);

    /**
     * 查询审核结果记录列表
     *
     * @param auditQuery 审核结果记录
     * @return 审核结果记录集合
     */
    List<AuditDTO> findAll(AuditQuery auditQuery);

    /**
     * 分页查询审核结果记录列表
     *
     * @param auditQuery 审核结果记录
     * @return 审核结果记录集合
     */
    PageInfo<AuditDTO> find(AuditQuery auditQuery);

    /**
     * 查询审核结果记录Map
     *
     * @param ids 编号集合
     * @return 审核结果记录Map
     */
    Map<Long, AuditDTO> findMapByIds(List<Long> ids);

    /**
     * 新增审核结果记录
     *
     * @param audit 审核结果记录
     * @return 结果
     */
    int create(Audit audit);

    /**
     * 修改审核结果记录
     *
     * @param audit 审核结果记录
     * @return 结果
     */
    int modifyById(Audit audit);

    /**
     * 删除审核结果记录信息
     *
     * @param id 审核结果记录id
     * @return 结果
     */
    int removeById(Long id);
}
