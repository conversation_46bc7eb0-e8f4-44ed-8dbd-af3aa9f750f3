package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.UserTicket;
import com.dto.UserTicketDTO;
import com.query.UserTicketQuery;

/**
 * 用户门票Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
public interface UserTicketService {
    /**
     * 查询用户门票
     * 
     * @param id 用户门票ID
     * @return 用户门票
     */
    UserTicketDTO findById(Long id);

    /**
     * 查询用户门票列表
     *
     * @param ids 编号集合
     * @return 用户门票集合
     */
    List<UserTicketDTO> findByIds(List<Long> ids);

    /**
     * 查询用户门票列表
     * 
     * @param userTicketQuery 用户门票
     * @return 用户门票集合
     */
    List<UserTicketDTO> findAll(UserTicketQuery userTicketQuery);

	/**
	 *  分页查询用户门票列表
	 *
	 * @param userTicketQuery 用户门票
	 * @return 用户门票集合
	 */
	PageInfo<UserTicketDTO> find(UserTicketQuery userTicketQuery);

    /**
     * 查询用户门票Map
     *
     * @param ids 编号集合
     * @return 用户门票Map
     */
    Map<Long, UserTicketDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户门票
     * 
     * @param userTicket 用户门票
     * @return 结果
     */
    int create(UserTicket userTicket);

    /**
     * 修改用户门票
     * 
     * @param userTicket 用户门票
     * @return 结果
     */
    int modifyById(UserTicket userTicket);

    /**
     * 删除用户门票信息
     * 
     * @param id 用户门票id
     * @return 结果
     */
   int removeById(Long id);
}
