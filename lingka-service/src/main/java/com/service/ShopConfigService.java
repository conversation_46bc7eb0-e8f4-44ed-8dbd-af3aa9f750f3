package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ShopConfig;
import com.dto.ShopConfigDTO;
import com.query.ShopConfigQuery;

/**
 * 店铺配置Service接口
 *
 * <AUTHOR>
 */
public interface ShopConfigService {
    /**
     * 查询店铺配置
     *
     * @param id 店铺配置ID
     * @return 店铺配置
     */
    ShopConfigDTO findById(Long id);

    /**
     * 查询店铺配置列表
     *
     * @param ids 编号集合
     * @return 店铺配置集合
     */
    List<ShopConfigDTO> findByIds(List<Long> ids);

    /**
     * 查询店铺配置列表
     *
     * @param shopConfigQuery 店铺配置
     * @return 店铺配置集合
     */
    List<ShopConfigDTO> findAll(ShopConfigQuery shopConfigQuery);

    /**
     * 查询店铺配置列表
     *
     * @param shopIds 店铺 ID 集合
     * @return 店铺配置集合
     */
    List<ShopConfigDTO> findAllByShopIds(List<Long> shopIds);

    /**
     * 分页查询店铺配置列表
     *
     * @param shopConfigQuery 店铺配置
     * @return 店铺配置集合
     */
    PageInfo<ShopConfigDTO> find(ShopConfigQuery shopConfigQuery);

    /**
     * 查询店铺配置Map
     *
     * @param ids 编号集合
     * @return 店铺配置Map
     */
    Map<Long, ShopConfigDTO> findMapByIds(List<Long> ids);

    /**
     * 查询店铺配置Map
     *
     * @param shopIds 店铺编号集合
     * @return 店铺配置Map
     */
    Map<Long, ShopConfigDTO> findMapByShopIds(List<Long> shopIds);

    /**
     * 查询店铺配置
     *
     * @param shopId 店铺编号
     * @return 店铺配置
     */
    ShopConfigDTO findByShopId(Long shopId);

    /**
     * 新增店铺配置
     *
     * @param shopConfig 店铺配置
     * @return 结果
     */
    int create(ShopConfig shopConfig);

    /**
     * 修改店铺配置
     *
     * @param shopConfig 店铺配置
     * @return 结果
     */
    int modifyById(ShopConfig shopConfig);


    /**
     * 修改店铺配置
     *
     * @param params 店铺配置
     * @return 更新数量
     */
    int updatePartialById(Map<String,Object> params);

    /**
     * 删除店铺配置信息
     *
     * @param id 店铺配置id
     * @return 结果
     */
    int removeById(Long id);
}
