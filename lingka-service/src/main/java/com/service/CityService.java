package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.City;
import com.dto.CityDTO;
import com.query.CityQuery;

/**
 * 城市Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
public interface CityService {
    /**
     * 查询城市
     * 
     * @param id 城市ID
     * @return 城市
     */
    CityDTO findById(Long id);

    /**
     * 查询城市列表
     *
     * @param ids 编号集合
     * @return 城市集合
     */
    List<CityDTO> findByIds(List<Long> ids);

    /**
     * 查询城市列表
     * 
     * @param cityQuery 城市
     * @return 城市集合
     */
    List<CityDTO> findAll(CityQuery cityQuery);

	/**
	 *  分页查询城市列表
	 *
	 * @param cityQuery 城市
	 * @return 城市集合
	 */
	PageInfo<CityDTO> find(CityQuery cityQuery);

    /**
     * 查询城市Map
     *
     * @param ids 编号集合
     * @return 城市Map
     */
    Map<Long, CityDTO> findMapByIds(List<Long> ids);

    /**
     * 新增城市
     * 
     * @param city 城市
     * @return 结果
     */
    int create(City city);

    /**
     * 修改城市
     * 
     * @param city 城市
     * @return 结果
     */
    int modifyById(City city);

    /**
     * 删除城市信息
     * 
     * @param id 城市id
     * @return 结果
     */
   int removeById(Long id);
}
