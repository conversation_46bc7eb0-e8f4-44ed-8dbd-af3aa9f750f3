package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Menu;
import com.dto.MenuDTO;
import com.query.MenuQuery;

/**
 * 菜单Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public interface MenuService {
    /**
     * 查询菜单
     * 
     * @param id 菜单ID
     * @return 菜单
     */
    MenuDTO findById(Long id);

    /**
     * 查询菜单列表
     *
     * @param ids 编号集合
     * @return 菜单集合
     */
    List<MenuDTO> findByIds(List<Long> ids);

    /**
     * 查询菜单列表
     * 
     * @param menuQuery 菜单
     * @return 菜单集合
     */
    List<MenuDTO> findAll(MenuQuery menuQuery);

	/**
	 *  分页查询菜单列表
	 *
	 * @param menuQuery 菜单
	 * @return 菜单集合
	 */
	PageInfo<MenuDTO> find(MenuQuery menuQuery);

    /**
     * 查询菜单Map
     *
     * @param ids 编号集合
     * @return 菜单Map
     */
    Map<Long, MenuDTO> findMapByIds(List<Long> ids);

    /**
     * 新增菜单
     * 
     * @param menu 菜单
     * @return 结果
     */
    int create(Menu menu);

    /**
     * 修改菜单
     * 
     * @param menu 菜单
     * @return 结果
     */
    int modifyById(Menu menu);

    /**
     * 删除菜单信息
     * 
     * @param id 菜单id
     * @return 结果
     */
   int removeById(Long id);
}
