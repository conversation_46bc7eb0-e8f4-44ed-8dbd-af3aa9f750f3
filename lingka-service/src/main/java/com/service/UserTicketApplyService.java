package com.service;

import java.util.List;
import java.util.Map;

import com.domain.UserTicket;
import com.github.pagehelper.PageInfo;
import com.domain.UserTicketApply;
import com.dto.UserTicketApplyDTO;
import com.query.UserTicketApplyQuery;

/**
 * 用户门票报名Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
public interface UserTicketApplyService {
    /**
     * 查询用户门票报名
     * 
     * @param id 用户门票报名ID
     * @return 用户门票报名
     */
    UserTicketApplyDTO findById(Long id);

    /**
     * 查询用户门票报名列表
     *
     * @param ids 编号集合
     * @return 用户门票报名集合
     */
    List<UserTicketApplyDTO> findByIds(List<Long> ids);

    /**
     * 查询用户门票报名列表
     * 
     * @param userTicketApplyQuery 用户门票报名
     * @return 用户门票报名集合
     */
    List<UserTicketApplyDTO> findAll(UserTicketApplyQuery userTicketApplyQuery);

	/**
	 *  分页查询用户门票报名列表
	 *
	 * @param userTicketApplyQuery 用户门票报名
	 * @return 用户门票报名集合
	 */
	PageInfo<UserTicketApplyDTO> find(UserTicketApplyQuery userTicketApplyQuery);

    /**
     * 查询用户门票报名Map
     *
     * @param ids 编号集合
     * @return 用户门票报名Map
     */
    Map<Long, UserTicketApplyDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户门票报名
     * 
     * @param userTicketApply 用户门票报名
     * @return 结果
     */
    int create(UserTicketApply userTicketApply);

    /**
     * 修改用户门票报名
     * 
     * @param userTicketApply 用户门票报名
     * @return 结果
     */
    int modifyById(UserTicketApply userTicketApply);

    /**
     * 通过
     *
     * @param userTicketApply 用户门票报名
     * @param userTickets 用户门票
     * @return 结果
     */
    int pass(UserTicketApply userTicketApply,List<UserTicket> userTickets);

    /**
     * 删除用户门票报名信息
     * 
     * @param id 用户门票报名id
     * @return 结果
     */
   int removeById(Long id);
}
