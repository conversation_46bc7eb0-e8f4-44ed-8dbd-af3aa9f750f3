package com.service;

import java.util.List;

import com.github.pagehelper.PageInfo;
import com.domain.WechatApp;
import com.dto.WechatAppDTO;
import com.query.WechatAppQuery;

/**
 * 微信应用Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-21
 */
public interface WechatAppService {
    /**
     * 查询微信应用
     * 
     * @param id 微信应用ID
     * @return 微信应用
     */
    WechatAppDTO findById(Long id);

    /**
     * 查询微信应用列表
     * 
     * @param wechatAppQuery 微信应用
     * @return 微信应用集合
     */
    List<WechatAppDTO> findAll(WechatAppQuery wechatAppQuery);


	/**
	 *  分页查询微信应用列表
	 *
	 * @param wechatAppQuery 微信应用
	 * @return 微信应用集合
	 */
	PageInfo<WechatAppDTO> find(WechatAppQuery wechatAppQuery);

    /**
     * 新增微信应用
     * 
     * @param wechatApp 微信应用
     * @return 结果
     */
    int create(WechatApp wechatApp);

    /**
     * 修改微信应用
     * 
     * @param wechatApp 微信应用
     * @return 结果
     */
    int modifyById(WechatApp wechatApp);

    /**
     * 删除微信应用信息
     * 
     * @param id 微信应用id
     * @return 结果
     */
   int removeById(Long id);
}
