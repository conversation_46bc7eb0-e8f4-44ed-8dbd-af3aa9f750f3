package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.RoleMenu;
import com.dto.RoleMenuDTO;
import com.query.RoleMenuQuery;

/**
 * 角色菜单关联Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public interface RoleMenuService {
    /**
     * 查询角色菜单关联
     * 
     * @param id 角色菜单关联ID
     * @return 角色菜单关联
     */
    RoleMenuDTO findById(Long id);

    /**
     * 查询角色菜单关联列表
     *
     * @param ids 编号集合
     * @return 角色菜单关联集合
     */
    List<RoleMenuDTO> findByIds(List<Long> ids);

    /**
     * 查询角色菜单关联列表
     * 
     * @param roleMenuQuery 角色菜单关联
     * @return 角色菜单关联集合
     */
    List<RoleMenuDTO> findAll(RoleMenuQuery roleMenuQuery);

	/**
	 *  分页查询角色菜单关联列表
	 *
	 * @param roleMenuQuery 角色菜单关联
	 * @return 角色菜单关联集合
	 */
	PageInfo<RoleMenuDTO> find(RoleMenuQuery roleMenuQuery);

    /**
     * 查询角色菜单关联Map
     *
     * @param ids 编号集合
     * @return 角色菜单关联Map
     */
    Map<Long, RoleMenuDTO> findMapByIds(List<Long> ids);

    /**
     * 新增角色菜单关联
     * 
     * @param roleMenu 角色菜单关联
     * @return 结果
     */
    int create(RoleMenu roleMenu);

    /**
     * 修改角色菜单关联
     * 
     * @param roleMenu 角色菜单关联
     * @return 结果
     */
    int modifyById(RoleMenu roleMenu);

    /**
     * 删除角色菜单关联信息
     * 
     * @param id 角色菜单关联id
     * @return 结果
     */
   int removeById(Long id);
}
