package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Tag;
import com.dto.TagDTO;
import com.query.TagQuery;

/**
 * 标签Service接口
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface TagService {
    /**
     * 查询标签
     *
     * @param id 标签ID
     * @return 标签
     */
    TagDTO findById(Long id);

    /**
     * 查询标签列表
     *
     * @param ids 编号集合
     * @return 标签集合
     */
    List<TagDTO> findByIds(List<Long> ids);

    /**
     * 查询标签列表
     *
     * @param tagQuery 标签
     * @return 标签集合
     */
    List<TagDTO> findAll(TagQuery tagQuery);

    /**
     * 分页查询标签列表
     *
     * @param tagQuery 标签
     * @return 标签集合
     */
    PageInfo<TagDTO> find(TagQuery tagQuery);

    /**
     * 查询标签Map
     *
     * @param ids 编号集合
     * @return 标签Map
     */
    Map<Long, TagDTO> findMapByIds(List<Long> ids);

    /**
     * 新增标签
     *
     * @param tag 标签
     * @return 结果
     */
    int create(Tag tag);

    /**
     * 修改标签
     *
     * @param tag 标签
     * @return 结果
     */
    int modifyById(Tag tag);

    /**
     * 删除标签信息
     *
     * @param id 标签id
     * @return 结果
     */
    int removeById(Long id);
}
