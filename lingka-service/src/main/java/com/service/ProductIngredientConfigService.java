package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ProductIngredientConfig;
import com.dto.ProductIngredientConfigDTO;
import com.query.ProductIngredientConfigQuery;

/**
 * 产品小料配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ProductIngredientConfigService {
    /**
     * 查询产品小料配置
     * 
     * @param id 产品小料配置ID
     * @return 产品小料配置
     */
    ProductIngredientConfigDTO findById(Long id);

    /**
     * 查询产品小料配置列表
     *
     * @param ids 编号集合
     * @return 产品小料配置集合
     */
    List<ProductIngredientConfigDTO> findByIds(List<Long> ids);

    /**
     * 查询产品小料配置列表
     * 
     * @param productIngredientConfigQuery 产品小料配置
     * @return 产品小料配置集合
     */
    List<ProductIngredientConfigDTO> findAll(ProductIngredientConfigQuery productIngredientConfigQuery);

	/**
	 *  分页查询产品小料配置列表
	 *
	 * @param productIngredientConfigQuery 产品小料配置
	 * @return 产品小料配置集合
	 */
	PageInfo<ProductIngredientConfigDTO> find(ProductIngredientConfigQuery productIngredientConfigQuery);

    /**
     * 查询产品小料配置Map
     *
     * @param ids 编号集合
     * @return 产品小料配置Map
     */
    Map<Long, ProductIngredientConfigDTO> findMapByIds(List<Long> ids);

    /**
     * 新增产品小料配置
     * 
     * @param productIngredientConfig 产品小料配置
     * @return 结果
     */
    int create(ProductIngredientConfig productIngredientConfig);

    /**
     * 修改产品小料配置
     * 
     * @param productIngredientConfig 产品小料配置
     * @return 结果
     */
    int modifyById(ProductIngredientConfig productIngredientConfig);

    /**
     * 删除产品小料配置信息
     * 
     * @param id 产品小料配置id
     * @return 结果
     */
   int removeById(Long id);
}
