package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.OrderTicket;
import com.dto.OrderTicketDTO;
import com.query.OrderTicketQuery;

/**
 * 订单门票Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
public interface OrderTicketService {
    /**
     * 查询订单门票
     * 
     * @param id 订单门票ID
     * @return 订单门票
     */
    OrderTicketDTO findById(Long id);

    /**
     * 查询订单门票列表
     *
     * @param ids 编号集合
     * @return 订单门票集合
     */
    List<OrderTicketDTO> findByIds(List<Long> ids);

    /**
     * 查询订单门票列表
     * 
     * @param orderTicketQuery 订单门票
     * @return 订单门票集合
     */
    List<OrderTicketDTO> findAll(OrderTicketQuery orderTicketQuery);

	/**
	 *  分页查询订单门票列表
	 *
	 * @param orderTicketQuery 订单门票
	 * @return 订单门票集合
	 */
	PageInfo<OrderTicketDTO> find(OrderTicketQuery orderTicketQuery);

    /**
     * 查询订单门票Map
     *
     * @param ids 编号集合
     * @return 订单门票Map
     */
    Map<Long, OrderTicketDTO> findMapByIds(List<Long> ids);

    /**
     * 新增订单门票
     * 
     * @param orderTicket 订单门票
     * @return 结果
     */
    int create(OrderTicket orderTicket);

    /**
     * 修改订单门票
     * 
     * @param orderTicket 订单门票
     * @return 结果
     */
    int modifyById(OrderTicket orderTicket);

    /**
     * 删除订单门票信息
     * 
     * @param id 订单门票id
     * @return 结果
     */
   int removeById(Long id);
}
