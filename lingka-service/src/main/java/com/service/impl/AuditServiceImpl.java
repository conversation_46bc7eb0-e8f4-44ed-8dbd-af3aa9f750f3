package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constant.DataStatus;
import com.dao.AuditDao;
import com.domain.Audit;
import com.dto.AuditDTO;
import com.github.pagehelper.Page;
import com.query.AuditQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.AuditService;

/**
 * 审核结果记录Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class AuditServiceImpl extends BaseService implements AuditService {

    @Autowired
    private AuditDao auditDao;

    /**
     * 查询审核结果记录
     *
     * @param id 审核结果记录ID
     * @return 审核结果记录
     */
    @Override
    public AuditDTO findById(Long id) {
        return auditDao.selectById(id);
    }

    /**
     * 查询审核结果记录列表
     *
     * @param ids 编号集合
     * @return 审核结果记录集合
     */
    @Override
    public List<AuditDTO> findByIds(List<Long> ids) {
        return auditDao.selectByIds(ids);
    }

    /**
     * 查询审核结果记录列表
     *
     * @param auditQuery 审核结果记录
     * @return 审核结果记录
     */
    @Override
    public List<AuditDTO> findAll(AuditQuery auditQuery) {
        return auditDao.select(auditQuery);
    }

    /**
     * 分页查询审核结果记录列表
     *
     * @param auditQuery 审核结果记录
     * @return 审核结果记录
     */
    @Override
    public PageInfo<AuditDTO> find(AuditQuery auditQuery) {
        try (Page<AuditDTO> page = PageHelper.startPage(auditQuery.getPageNum(), auditQuery.getPageSize())) {
            auditDao.select(auditQuery);
            return page.toPageInfo();
        }
    }

    /**
     * 查询审核结果记录Map
     *
     * @param ids 编号集合
     * @return 审核结果记录Map
     */
    @Override
    public Map<Long, AuditDTO> findMapByIds(List<Long> ids) {
        Map<Long, AuditDTO> auditDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<AuditDTO> auditDTOList = auditDao.selectByIds(ids);
            for (AuditDTO auditDTO : auditDTOList) {
                auditDTOMap.put(auditDTO.getId(), auditDTO);
            }
        }
        return auditDTOMap;
    }

    /**
     * 新增审核结果记录
     *
     * @param audit 审核结果记录
     * @return 结果
     */
    @Override
    public int create(Audit audit) {
        audit.setStatus(DataStatus.Y.getCode());
        return auditDao.insert(audit);
    }

    /**
     * 修改审核结果记录
     *
     * @param audit 审核结果记录
     * @return 结果
     */
    @Override
    public int modifyById(Audit audit) {
        return auditDao.updateById(audit);
    }


    /**
     * 删除审核结果记录信息
     *
     * @param id 审核结果记录ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return auditDao.deleteById(id);
    }

}
