package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.TableDao;
import com.domain.Table;
import com.dto.TableDTO;
import com.query.TableQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.TableService;

/**
 * 桌子Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
@Service
public class TableServiceImpl extends BaseService implements TableService {

    @Autowired
    private TableDao tableDao;

    /**
     * 查询桌子
     * 
     * @param id 桌子ID
     * @return 桌子
     */
    @Override
    public TableDTO findById(Long id) {
        return tableDao.selectById(id);
    }

    /**
     * 查询桌子列表
     *
     * @param ids 编号集合
     * @return 桌子集合
     */
    @Override
    public List<TableDTO> findByIds(List<Long> ids) {
        return tableDao.selectByIds(ids);
    }

    /**
     * 查询桌子列表
     *
     * @param tableQuery 桌子
     * @return 桌子
     */
    @Override
    public List<TableDTO> findAll(TableQuery tableQuery) {
        return tableDao.select(tableQuery);
    }

	/**
	 * 分页查询桌子列表
	 *
	 * @param tableQuery 桌子
	 * @return 桌子
	 */
	@Override
	public PageInfo<TableDTO> find(TableQuery tableQuery) {
        PageHelper.startPage(tableQuery.getPageNum(),tableQuery.getPageSize());
		List<TableDTO> tableDTOList = tableDao.select(tableQuery);
		return new PageInfo<>(tableDTOList);
	}

    /**
     * 查询桌子Map
     *
     * @param ids 编号集合
     * @return 桌子Map
     */
    @Override
    public Map<Long, TableDTO> findMapByIds(List<Long> ids) {
        Map<Long, TableDTO> tableDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<TableDTO> tableDTOList =  tableDao.selectByIds(ids);
            for (TableDTO tableDTO : tableDTOList) {
                    tableDTOMap.put(tableDTO.getId(),tableDTO);
            }
        }
        return tableDTOMap;
    }

    /**
     * 新增桌子
     *
     * @param table 桌子
     * @return 结果
     */
    @Override
    public int create(Table table) {
        return tableDao.insert(table);
    }

    /**
     * 修改桌子
     *
     * @param table 桌子
     * @return 结果
     */
    @Override
    public int modifyById(Table table) {
        return tableDao.updateById(table);
    }


    /**
     * 删除桌子信息
     *
     * @param id 桌子ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return tableDao.deleteById(id);
    }

}
