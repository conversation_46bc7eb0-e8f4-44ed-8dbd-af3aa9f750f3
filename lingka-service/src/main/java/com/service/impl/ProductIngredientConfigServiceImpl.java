package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.ProductIngredientConfigDao;
import com.domain.ProductIngredientConfig;
import com.dto.ProductIngredientConfigDTO;
import com.query.ProductIngredientConfigQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ProductIngredientConfigService;

/**
 * 产品小料配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class ProductIngredientConfigServiceImpl extends BaseService implements ProductIngredientConfigService {

    @Autowired
    private ProductIngredientConfigDao productIngredientConfigDao;

    /**
     * 查询产品小料配置
     * 
     * @param id 产品小料配置ID
     * @return 产品小料配置
     */
    @Override
    public ProductIngredientConfigDTO findById(Long id) {
        return productIngredientConfigDao.selectById(id);
    }

    /**
     * 查询产品小料配置列表
     *
     * @param ids 编号集合
     * @return 产品小料配置集合
     */
    @Override
    public List<ProductIngredientConfigDTO> findByIds(List<Long> ids) {
        return productIngredientConfigDao.selectByIds(ids);
    }

    /**
     * 查询产品小料配置列表
     *
     * @param productIngredientConfigQuery 产品小料配置
     * @return 产品小料配置
     */
    @Override
    public List<ProductIngredientConfigDTO> findAll(ProductIngredientConfigQuery productIngredientConfigQuery) {
        return productIngredientConfigDao.select(productIngredientConfigQuery);
    }

	/**
	 * 分页查询产品小料配置列表
	 *
	 * @param productIngredientConfigQuery 产品小料配置
	 * @return 产品小料配置
	 */
	@Override
	public PageInfo<ProductIngredientConfigDTO> find(ProductIngredientConfigQuery productIngredientConfigQuery) {
        PageHelper.startPage(productIngredientConfigQuery.getPageNum(),productIngredientConfigQuery.getPageSize());
		List<ProductIngredientConfigDTO> productIngredientConfigDTOList = productIngredientConfigDao.select(productIngredientConfigQuery);
		return new PageInfo<>(productIngredientConfigDTOList);
	}

    /**
     * 查询产品小料配置Map
     *
     * @param ids 编号集合
     * @return 产品小料配置Map
     */
    @Override
    public Map<Long, ProductIngredientConfigDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProductIngredientConfigDTO> productIngredientConfigDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductIngredientConfigDTO> productIngredientConfigDTOList =  productIngredientConfigDao.selectByIds(ids);
            for (ProductIngredientConfigDTO productIngredientConfigDTO : productIngredientConfigDTOList) {
                    productIngredientConfigDTOMap.put(productIngredientConfigDTO.getId(),productIngredientConfigDTO);
            }
        }
        return productIngredientConfigDTOMap;
    }

    /**
     * 新增产品小料配置
     *
     * @param productIngredientConfig 产品小料配置
     * @return 结果
     */
    @Override
    public int create(ProductIngredientConfig productIngredientConfig) {
        return productIngredientConfigDao.insert(productIngredientConfig);
    }

    /**
     * 修改产品小料配置
     *
     * @param productIngredientConfig 产品小料配置
     * @return 结果
     */
    @Override
    public int modifyById(ProductIngredientConfig productIngredientConfig) {
        return productIngredientConfigDao.updateById(productIngredientConfig);
    }


    /**
     * 删除产品小料配置信息
     *
     * @param id 产品小料配置ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return productIngredientConfigDao.deleteById(id);
    }

}
