package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserRoleDao;
import com.domain.UserRole;
import com.dto.UserRoleDTO;
import com.query.UserRoleQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserRoleService;

/**
 * 用户角色关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class UserRoleServiceImpl extends BaseService implements UserRoleService {

    @Autowired
    private UserRoleDao userRoleDao;

    /**
     * 查询用户角色关联
     *
     * @param id 用户角色关联ID
     * @return 用户角色关联
     */
    @Override
    public UserRoleDTO findById(Long id) {
        return userRoleDao.selectById(id);
    }

    /**
     * 查询用户角色关联列表
     *
     * @param ids 编号集合
     * @return 用户角色关联集合
     */
    @Override
    public List<UserRoleDTO> findByIds(List<Long> ids) {
        return userRoleDao.selectByIds(ids);
    }

    /**
     * 查询用户角色关联列表
     *
     * @param userRoleQuery 用户角色关联
     * @return 用户角色关联
     */
    @Override
    public List<UserRoleDTO> findAll(UserRoleQuery userRoleQuery) {
        return userRoleDao.select(userRoleQuery);
    }

    /**
     * 分页查询用户角色关联列表
     *
     * @param userRoleQuery 用户角色关联
     * @return 用户角色关联
     */
    @Override
    public PageInfo<UserRoleDTO> find(UserRoleQuery userRoleQuery) {
        PageHelper.startPage(userRoleQuery.getPageNum(), userRoleQuery.getPageSize());
        List<UserRoleDTO> userRoleDTOList = userRoleDao.select(userRoleQuery);
        return new PageInfo<>(userRoleDTOList);
    }

    /**
     * 查询用户角色关联Map
     *
     * @param ids 编号集合
     * @return 用户角色关联Map
     */
    @Override
    public Map<Long, UserRoleDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserRoleDTO> userRoleDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<UserRoleDTO> userRoleDTOList = userRoleDao.selectByIds(ids);
            for (UserRoleDTO userRoleDTO : userRoleDTOList) {
                userRoleDTOMap.put(userRoleDTO.getId(), userRoleDTO);
            }
        }
        return userRoleDTOMap;
    }

    /**
     * 新增用户角色关联
     *
     * @param userRole 用户角色关联
     * @return 结果
     */
    @Override
    public int create(UserRole userRole) {
        return userRoleDao.insert(userRole);
    }

    /**
     * 修改用户角色关联
     *
     * @param userRole 用户角色关联
     * @return 结果
     */
    @Override
    public int modifyById(UserRole userRole) {
        return userRoleDao.updateById(userRole);
    }


    /**
     * 删除用户角色关联信息
     *
     * @param id 用户角色关联ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userRoleDao.deleteById(id);
    }

    @Override
    @Transactional
    public int removeByUserIdAndShopIdAndCreate(Long userId, Long shopId, List<Long> roleIds) {
        int count = userRoleDao.deleteByUserIdAndShopId(userId, shopId);
        for (Long roleId : roleIds) {
            UserRole userRole = new UserRole();
            userRole.setUserId(userId);
            userRole.setShopId(shopId);
            userRole.setRoleId(roleId);
            count += userRoleDao.insert(userRole);
        }
        return count;
    }

    /**
     * 根据店铺 ID 删除用户角色关联
     *
     * @param shopId 店铺 ID
     * @return 结果
     */
    @Override
    public int removeByShopId(Long shopId) {
        return userRoleDao.deleteByShopId(shopId);
    }

}
