package com.service.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.FileDao;
import com.domain.File;
import com.service.FileService;

@Service
public class FileServiceImpl implements FileService {

	@Autowired
	private FileDao fileDao;
	
	@Override
	public File findById(Long id) {
		return this.fileDao.selectById(id);
	}
	
	@Override
	public File findByUrl(String url) {
		return this.fileDao.selectByUrl(url);
	}
	
	@Override
	public Integer count(File file) {
		return this.fileDao.count(file);
	}

	@Override
	public List<File> find(File file, Integer start, Integer limit) {
		return this.fileDao.select(file, start, limit);
	}
	

	@Override
	public Integer create(File file) {
		return this.fileDao.insert(file);
	}
	
	@Override
	public Integer modifyById(File file) {
		return this.fileDao.updateById(file);
	}

}
