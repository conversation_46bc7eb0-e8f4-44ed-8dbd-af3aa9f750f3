package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserCartDao;
import com.domain.UserCart;
import com.dto.UserCartDTO;
import com.query.UserCartQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserCartService;

/**
 * 用户购物车Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */
@Service
public class UserCartServiceImpl extends BaseService implements UserCartService {

    @Autowired
    private UserCartDao userCartDao;

    /**
     * 查询用户购物车
     * 
     * @param id 用户购物车ID
     * @return 用户购物车
     */
    @Override
    public UserCartDTO findById(Long id) {
        return userCartDao.selectById(id);
    }

    /**
     * 查询用户购物车列表
     *
     * @param ids 编号集合
     * @return 用户购物车集合
     */
    @Override
    public List<UserCartDTO> findByIds(List<Long> ids) {
        return userCartDao.selectByIds(ids);
    }

    /**
     * 查询用户购物车列表
     *
     * @param userCartQuery 用户购物车
     * @return 用户购物车
     */
    @Override
    public List<UserCartDTO> findAll(UserCartQuery userCartQuery) {
        return userCartDao.select(userCartQuery);
    }

	/**
	 * 分页查询用户购物车列表
	 *
	 * @param userCartQuery 用户购物车
	 * @return 用户购物车
	 */
	@Override
	public PageInfo<UserCartDTO> find(UserCartQuery userCartQuery) {
        PageHelper.startPage(userCartQuery.getPageNum(),userCartQuery.getPageSize());
		List<UserCartDTO> userCartDTOList = userCartDao.select(userCartQuery);
		return new PageInfo<>(userCartDTOList);
	}

    /**
     * 查询用户购物车Map
     *
     * @param ids 编号集合
     * @return 用户购物车Map
     */
    @Override
    public Map<Long, UserCartDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserCartDTO> userCartDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserCartDTO> userCartDTOList =  userCartDao.selectByIds(ids);
            for (UserCartDTO userCartDTO : userCartDTOList) {
                    userCartDTOMap.put(userCartDTO.getId(),userCartDTO);
            }
        }
        return userCartDTOMap;
    }

    /**
     * 新增用户购物车
     *
     * @param userCart 用户购物车
     * @return 结果
     */
    @Override
    public int create(UserCart userCart) {
        return userCartDao.insert(userCart);
    }

    /**
     * 修改用户购物车
     *
     * @param userCart 用户购物车
     * @return 结果
     */
    @Override
    public int modifyById(UserCart userCart) {
        return userCartDao.updateById(userCart);
    }

    @Override
    @Transactional
    public int batchModifyById(List<UserCart> userCarts) {
        int rows = 0;
        for (UserCart userCart : userCarts) {
            rows += userCartDao.updateById(userCart);
        }
        return rows;
    }


    /**
     * 删除用户购物车信息
     *
     * @param id 用户购物车ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userCartDao.deleteById(id);
    }

}
