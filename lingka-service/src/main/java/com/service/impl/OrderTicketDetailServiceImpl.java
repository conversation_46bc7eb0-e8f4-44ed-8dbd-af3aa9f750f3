package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.OrderTicketDetailDao;
import com.domain.OrderTicketDetail;
import com.dto.OrderTicketDetailDTO;
import com.query.OrderTicketDetailQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.OrderTicketDetailService;

/**
 * 订单门票详情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
@Service
public class OrderTicketDetailServiceImpl extends BaseService implements OrderTicketDetailService {

    @Autowired
    private OrderTicketDetailDao orderTicketDetailDao;

    /**
     * 查询订单门票详情
     * 
     * @param id 订单门票详情ID
     * @return 订单门票详情
     */
    @Override
    public OrderTicketDetailDTO findById(Long id) {
        return orderTicketDetailDao.selectById(id);
    }

    /**
     * 查询订单门票详情列表
     *
     * @param ids 编号集合
     * @return 订单门票详情集合
     */
    @Override
    public List<OrderTicketDetailDTO> findByIds(List<Long> ids) {
        return orderTicketDetailDao.selectByIds(ids);
    }

    /**
     * 查询订单门票详情列表
     *
     * @param orderTicketDetailQuery 订单门票详情
     * @return 订单门票详情
     */
    @Override
    public List<OrderTicketDetailDTO> findAll(OrderTicketDetailQuery orderTicketDetailQuery) {
        return orderTicketDetailDao.select(orderTicketDetailQuery);
    }

	/**
	 * 分页查询订单门票详情列表
	 *
	 * @param orderTicketDetailQuery 订单门票详情
	 * @return 订单门票详情
	 */
	@Override
	public PageInfo<OrderTicketDetailDTO> find(OrderTicketDetailQuery orderTicketDetailQuery) {
        PageHelper.startPage(orderTicketDetailQuery.getPageNum(),orderTicketDetailQuery.getPageSize());
		List<OrderTicketDetailDTO> orderTicketDetailDTOList = orderTicketDetailDao.select(orderTicketDetailQuery);
		return new PageInfo<>(orderTicketDetailDTOList);
	}

    /**
     * 查询订单门票详情Map
     *
     * @param ids 编号集合
     * @return 订单门票详情Map
     */
    @Override
    public Map<Long, OrderTicketDetailDTO> findMapByIds(List<Long> ids) {
        Map<Long, OrderTicketDetailDTO> orderTicketDetailDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<OrderTicketDetailDTO> orderTicketDetailDTOList =  orderTicketDetailDao.selectByIds(ids);
            for (OrderTicketDetailDTO orderTicketDetailDTO : orderTicketDetailDTOList) {
                    orderTicketDetailDTOMap.put(orderTicketDetailDTO.getId(),orderTicketDetailDTO);
            }
        }
        return orderTicketDetailDTOMap;
    }

    /**
     * 新增订单门票详情
     *
     * @param orderTicketDetail 订单门票详情
     * @return 结果
     */
    @Override
    public int create(OrderTicketDetail orderTicketDetail) {
        return orderTicketDetailDao.insert(orderTicketDetail);
    }

    /**
     * 修改订单门票详情
     *
     * @param orderTicketDetail 订单门票详情
     * @return 结果
     */
    @Override
    public int modifyById(OrderTicketDetail orderTicketDetail) {
        return orderTicketDetailDao.updateById(orderTicketDetail);
    }


    /**
     * 删除订单门票详情信息
     *
     * @param id 订单门票详情ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return orderTicketDetailDao.deleteById(id);
    }

}
