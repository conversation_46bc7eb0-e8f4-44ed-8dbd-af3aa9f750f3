package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.ProductOptionDao;
import com.domain.ProductOption;
import com.dto.ProductOptionDTO;
import com.query.ProductOptionQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ProductOptionService;

/**
 * 产品选项Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class ProductOptionServiceImpl extends BaseService implements ProductOptionService {

    @Autowired
    private ProductOptionDao productOptionDao;

    /**
     * 查询产品选项
     * 
     * @param id 产品选项ID
     * @return 产品选项
     */
    @Override
    public ProductOptionDTO findById(Long id) {
        return productOptionDao.selectById(id);
    }

    /**
     * 查询产品选项列表
     *
     * @param ids 编号集合
     * @return 产品选项集合
     */
    @Override
    public List<ProductOptionDTO> findByIds(List<Long> ids) {
        return productOptionDao.selectByIds(ids);
    }

    /**
     * 查询产品选项列表
     *
     * @param productOptionQuery 产品选项
     * @return 产品选项
     */
    @Override
    public List<ProductOptionDTO> findAll(ProductOptionQuery productOptionQuery) {
        return productOptionDao.select(productOptionQuery);
    }

	/**
	 * 分页查询产品选项列表
	 *
	 * @param productOptionQuery 产品选项
	 * @return 产品选项
	 */
	@Override
	public PageInfo<ProductOptionDTO> find(ProductOptionQuery productOptionQuery) {
        PageHelper.startPage(productOptionQuery.getPageNum(),productOptionQuery.getPageSize());
		List<ProductOptionDTO> productOptionDTOList = productOptionDao.select(productOptionQuery);
		return new PageInfo<>(productOptionDTOList);
	}

    /**
     * 查询产品选项Map
     *
     * @param ids 编号集合
     * @return 产品选项Map
     */
    @Override
    public Map<Long, ProductOptionDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProductOptionDTO> productOptionDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductOptionDTO> productOptionDTOList =  productOptionDao.selectByIds(ids);
            for (ProductOptionDTO productOptionDTO : productOptionDTOList) {
                    productOptionDTOMap.put(productOptionDTO.getId(),productOptionDTO);
            }
        }
        return productOptionDTOMap;
    }

    /**
     * 新增产品选项
     *
     * @param productOption 产品选项
     * @return 结果
     */
    @Override
    public int create(ProductOption productOption) {
        return productOptionDao.insert(productOption);
    }

    /**
     * 修改产品选项
     *
     * @param productOption 产品选项
     * @return 结果
     */
    @Override
    public int modifyById(ProductOption productOption) {
        return productOptionDao.updateById(productOption);
    }


    /**
     * 删除产品选项信息
     *
     * @param id 产品选项ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return productOptionDao.deleteById(id);
    }

}
