package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.UserDao;
import com.domain.User;
import com.dto.UserDTO;
import com.query.UserQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserService;

/**
 * 用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-03
 */
@Service
public class UserServiceImpl extends BaseService implements UserService {

    @Autowired
    private UserDao userDao;

    /**
     * 查询用户
     * 
     * @param id 用户ID
     * @return 用户
     */
    @Override
    public UserDTO findById(Long id) {
        return userDao.selectById(id);
    }

    @Override
    public UserDTO findByOpenid(String openid) {
        return userDao.selectByOpenid(openid);
    }

    @Override
    public UserDTO findByMobile(String mobile) {
        return userDao.selectByMobile(mobile);
    }

    /**
     * 查询用户列表
     *
     * @param ids 编号集合
     * @return 用户集合
     */
    @Override
    public List<UserDTO> findByIds(List<Long> ids) {
        return userDao.selectByIds(ids);
    }

    /**
     * 查询用户列表
     *
     * @param userQuery 用户
     * @return 用户
     */
    @Override
    public List<UserDTO> findAll(UserQuery userQuery) {
        return userDao.select(userQuery);
    }

	/**
	 * 分页查询用户列表
	 *
	 * @param userQuery 用户
	 * @return 用户
	 */
	@Override
	public PageInfo<UserDTO> find(UserQuery userQuery) {
        PageHelper.startPage(userQuery.getPageNum(),userQuery.getPageSize());
		List<UserDTO> userDTOList = userDao.select(userQuery);
		return new PageInfo<>(userDTOList);
	}

    /**
     * 查询用户Map
     *
     * @param ids 编号集合
     * @return 用户Map
     */
    @Override
    public Map<Long, UserDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserDTO> userDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserDTO> userDTOList =  userDao.selectByIds(ids);
            for (UserDTO userDTO : userDTOList) {
                    userDTOMap.put(userDTO.getId(),userDTO);
            }
        }
        return userDTOMap;
    }

    /**
     * 新增用户
     *
     * @param user 用户
     * @return 结果
     */
    @Override
    public int create(User user) {
        return userDao.insert(user);
    }

    /**
     * 修改用户
     *
     * @param user 用户
     * @return 结果
     */
    @Override
    public int modifyById(User user) {
        return userDao.updateById(user);
    }

    @Override
    public int updatePartialById(Map<String, Object> params) {
        return userDao.updatePartialById(params);
    }


    /**
     * 删除用户信息
     *
     * @param id 用户ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userDao.deleteById(id);
    }

}
