package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.DistrictDao;
import com.domain.District;
import com.dto.DistrictDTO;
import com.query.DistrictQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.DistrictService;

/**
 * 区(县)Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
public class DistrictServiceImpl extends BaseService implements DistrictService {

    @Autowired
    private DistrictDao districtDao;

    /**
     * 查询区(县)
     *
     * @param id 区(县)ID
     * @return 区(县)
     */
    @Override
    public DistrictDTO findById(Long id) {
        return districtDao.selectById(id);
    }

    /**
     * 查询区(县)列表
     *
     * @param ids 编号集合
     * @return 区(县)集合
     */
    @Override
    public List<DistrictDTO> findByIds(List<Long> ids) {
        return districtDao.selectByIds(ids);
    }

    /**
     * 查询区(县)列表
     *
     * @param districtQuery 区(县)
     * @return 区(县)
     */
    @Override
    public List<DistrictDTO> findAll(DistrictQuery districtQuery) {
        return districtDao.select(districtQuery);
    }

    /**
     * 分页查询区(县)列表
     *
     * @param districtQuery 区(县)
     * @return 区(县)
     */
    @Override
    public PageInfo<DistrictDTO> find(DistrictQuery districtQuery) {
        PageHelper.startPage(districtQuery.getPageNum(), districtQuery.getPageSize());
        List<DistrictDTO> districtDTOList = districtDao.select(districtQuery);
        return new PageInfo<>(districtDTOList);
    }

    /**
     * 查询区(县)Map
     *
     * @param ids 编号集合
     * @return 区(县)Map
     */
    @Override
    public Map<Long, DistrictDTO> findMapByIds(List<Long> ids) {
        Map<Long, DistrictDTO> districtDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<DistrictDTO> districtDTOList = districtDao.selectByIds(ids);
            for (DistrictDTO districtDTO : districtDTOList) {
                districtDTOMap.put(districtDTO.getId(), districtDTO);
            }
        }
        return districtDTOMap;
    }

    /**
     * 新增区(县)
     *
     * @param district 区(县)
     * @return 结果
     */
    @Override
    public int create(District district) {
        return districtDao.insert(district);
    }

    /**
     * 修改区(县)
     *
     * @param district 区(县)
     * @return 结果
     */
    @Override
    public int modifyById(District district) {
        return districtDao.updateById(district);
    }


    /**
     * 删除区(县)信息
     *
     * @param id 区(县)ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return districtDao.deleteById(id);
    }

}
