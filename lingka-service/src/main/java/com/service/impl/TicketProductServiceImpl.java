package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.TicketProductDao;
import com.domain.TicketProduct;
import com.dto.TicketProductDTO;
import com.query.TicketProductQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.TicketProductService;

/**
 * 门票产品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
@Service
public class TicketProductServiceImpl extends BaseService implements TicketProductService {

    @Autowired
    private TicketProductDao ticketProductDao;

    /**
     * 查询门票产品
     * 
     * @param id 门票产品ID
     * @return 门票产品
     */
    @Override
    public TicketProductDTO findById(Long id) {
        return ticketProductDao.selectById(id);
    }

    /**
     * 查询门票产品列表
     *
     * @param ids 编号集合
     * @return 门票产品集合
     */
    @Override
    public List<TicketProductDTO> findByIds(List<Long> ids) {
        return ticketProductDao.selectByIds(ids);
    }

    /**
     * 查询门票产品列表
     *
     * @param ticketProductQuery 门票产品
     * @return 门票产品
     */
    @Override
    public List<TicketProductDTO> findAll(TicketProductQuery ticketProductQuery) {
        return ticketProductDao.select(ticketProductQuery);
    }

	/**
	 * 分页查询门票产品列表
	 *
	 * @param ticketProductQuery 门票产品
	 * @return 门票产品
	 */
	@Override
	public PageInfo<TicketProductDTO> find(TicketProductQuery ticketProductQuery) {
        PageHelper.startPage(ticketProductQuery.getPageNum(),ticketProductQuery.getPageSize());
		List<TicketProductDTO> ticketProductDTOList = ticketProductDao.select(ticketProductQuery);
		return new PageInfo<>(ticketProductDTOList);
	}

    /**
     * 查询门票产品Map
     *
     * @param ids 编号集合
     * @return 门票产品Map
     */
    @Override
    public Map<Long, TicketProductDTO> findMapByIds(List<Long> ids) {
        Map<Long, TicketProductDTO> ticketProductDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<TicketProductDTO> ticketProductDTOList =  ticketProductDao.selectByIds(ids);
            for (TicketProductDTO ticketProductDTO : ticketProductDTOList) {
                    ticketProductDTOMap.put(ticketProductDTO.getId(),ticketProductDTO);
            }
        }
        return ticketProductDTOMap;
    }

    /**
     * 新增门票产品
     *
     * @param ticketProduct 门票产品
     * @return 结果
     */
    @Override
    public int create(TicketProduct ticketProduct) {
        return ticketProductDao.insert(ticketProduct);
    }

    /**
     * 修改门票产品
     *
     * @param ticketProduct 门票产品
     * @return 结果
     */
    @Override
    public int modifyById(TicketProduct ticketProduct) {
        return ticketProductDao.updateById(ticketProduct);
    }


    /**
     * 删除门票产品信息
     *
     * @param id 门票产品ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return ticketProductDao.deleteById(id);
    }

}
