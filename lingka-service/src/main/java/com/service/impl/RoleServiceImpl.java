package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.RoleDao;
import com.domain.Role;
import com.dto.RoleDTO;
import com.query.RoleQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.RoleService;

/**
 * 角色Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
public class RoleServiceImpl extends BaseService implements RoleService {

    @Autowired
    private RoleDao roleDao;

    /**
     * 查询角色
     * 
     * @param id 角色ID
     * @return 角色
     */
    @Override
    public RoleDTO findById(Long id) {
        return roleDao.selectById(id);
    }

    /**
     * 查询角色列表
     *
     * @param ids 编号集合
     * @return 角色集合
     */
    @Override
    public List<RoleDTO> findByIds(List<Long> ids) {
        return roleDao.selectByIds(ids);
    }

    /**
     * 查询角色列表
     *
     * @param roleQuery 角色
     * @return 角色
     */
    @Override
    public List<RoleDTO> findAll(RoleQuery roleQuery) {
        return roleDao.select(roleQuery);
    }

	/**
	 * 分页查询角色列表
	 *
	 * @param roleQuery 角色
	 * @return 角色
	 */
	@Override
	public PageInfo<RoleDTO> find(RoleQuery roleQuery) {
        PageHelper.startPage(roleQuery.getPageNum(),roleQuery.getPageSize());
		List<RoleDTO> roleDTOList = roleDao.select(roleQuery);
		return new PageInfo<>(roleDTOList);
	}

    /**
     * 查询角色Map
     *
     * @param ids 编号集合
     * @return 角色Map
     */
    @Override
    public Map<Long, RoleDTO> findMapByIds(List<Long> ids) {
        Map<Long, RoleDTO> roleDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<RoleDTO> roleDTOList =  roleDao.selectByIds(ids);
            for (RoleDTO roleDTO : roleDTOList) {
                    roleDTOMap.put(roleDTO.getId(),roleDTO);
            }
        }
        return roleDTOMap;
    }

    /**
     * 新增角色
     *
     * @param role 角色
     * @return 结果
     */
    @Override
    public int create(Role role) {
        return roleDao.insert(role);
    }

    /**
     * 修改角色
     *
     * @param role 角色
     * @return 结果
     */
    @Override
    public int modifyById(Role role) {
        return roleDao.updateById(role);
    }


    /**
     * 删除角色信息
     *
     * @param id 角色ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return roleDao.deleteById(id);
    }

}
