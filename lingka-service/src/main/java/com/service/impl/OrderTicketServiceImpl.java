package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.OrderTicketDao;
import com.domain.OrderTicket;
import com.dto.OrderTicketDTO;
import com.query.OrderTicketQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.OrderTicketService;

/**
 * 订单门票Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
@Service
public class OrderTicketServiceImpl extends BaseService implements OrderTicketService {

    @Autowired
    private OrderTicketDao orderTicketDao;

    /**
     * 查询订单门票
     * 
     * @param id 订单门票ID
     * @return 订单门票
     */
    @Override
    public OrderTicketDTO findById(Long id) {
        return orderTicketDao.selectById(id);
    }

    /**
     * 查询订单门票列表
     *
     * @param ids 编号集合
     * @return 订单门票集合
     */
    @Override
    public List<OrderTicketDTO> findByIds(List<Long> ids) {
        return orderTicketDao.selectByIds(ids);
    }

    /**
     * 查询订单门票列表
     *
     * @param orderTicketQuery 订单门票
     * @return 订单门票
     */
    @Override
    public List<OrderTicketDTO> findAll(OrderTicketQuery orderTicketQuery) {
        return orderTicketDao.select(orderTicketQuery);
    }

	/**
	 * 分页查询订单门票列表
	 *
	 * @param orderTicketQuery 订单门票
	 * @return 订单门票
	 */
	@Override
	public PageInfo<OrderTicketDTO> find(OrderTicketQuery orderTicketQuery) {
        PageHelper.startPage(orderTicketQuery.getPageNum(),orderTicketQuery.getPageSize());
		List<OrderTicketDTO> orderTicketDTOList = orderTicketDao.select(orderTicketQuery);
		return new PageInfo<>(orderTicketDTOList);
	}

    /**
     * 查询订单门票Map
     *
     * @param ids 编号集合
     * @return 订单门票Map
     */
    @Override
    public Map<Long, OrderTicketDTO> findMapByIds(List<Long> ids) {
        Map<Long, OrderTicketDTO> orderTicketDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<OrderTicketDTO> orderTicketDTOList =  orderTicketDao.selectByIds(ids);
            for (OrderTicketDTO orderTicketDTO : orderTicketDTOList) {
                    orderTicketDTOMap.put(orderTicketDTO.getId(),orderTicketDTO);
            }
        }
        return orderTicketDTOMap;
    }

    /**
     * 新增订单门票
     *
     * @param orderTicket 订单门票
     * @return 结果
     */
    @Override
    public int create(OrderTicket orderTicket) {
        return orderTicketDao.insert(orderTicket);
    }

    /**
     * 修改订单门票
     *
     * @param orderTicket 订单门票
     * @return 结果
     */
    @Override
    public int modifyById(OrderTicket orderTicket) {
        return orderTicketDao.updateById(orderTicket);
    }


    /**
     * 删除订单门票信息
     *
     * @param id 订单门票ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return orderTicketDao.deleteById(id);
    }

}
