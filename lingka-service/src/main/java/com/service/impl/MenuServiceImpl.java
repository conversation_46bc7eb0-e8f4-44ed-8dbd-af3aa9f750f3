package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.MenuDao;
import com.domain.Menu;
import com.dto.MenuDTO;
import com.query.MenuQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.MenuService;

/**
 * 菜单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
public class MenuServiceImpl extends BaseService implements MenuService {

    @Autowired
    private MenuDao menuDao;

    /**
     * 查询菜单
     * 
     * @param id 菜单ID
     * @return 菜单
     */
    @Override
    public MenuDTO findById(Long id) {
        return menuDao.selectById(id);
    }

    /**
     * 查询菜单列表
     *
     * @param ids 编号集合
     * @return 菜单集合
     */
    @Override
    public List<MenuDTO> findByIds(List<Long> ids) {
        return menuDao.selectByIds(ids);
    }

    /**
     * 查询菜单列表
     *
     * @param menuQuery 菜单
     * @return 菜单
     */
    @Override
    public List<MenuDTO> findAll(MenuQuery menuQuery) {
        return menuDao.select(menuQuery);
    }

	/**
	 * 分页查询菜单列表
	 *
	 * @param menuQuery 菜单
	 * @return 菜单
	 */
	@Override
	public PageInfo<MenuDTO> find(MenuQuery menuQuery) {
        PageHelper.startPage(menuQuery.getPageNum(),menuQuery.getPageSize());
		List<MenuDTO> menuDTOList = menuDao.select(menuQuery);
		return new PageInfo<>(menuDTOList);
	}

    /**
     * 查询菜单Map
     *
     * @param ids 编号集合
     * @return 菜单Map
     */
    @Override
    public Map<Long, MenuDTO> findMapByIds(List<Long> ids) {
        Map<Long, MenuDTO> menuDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<MenuDTO> menuDTOList =  menuDao.selectByIds(ids);
            for (MenuDTO menuDTO : menuDTOList) {
                    menuDTOMap.put(menuDTO.getId(),menuDTO);
            }
        }
        return menuDTOMap;
    }

    /**
     * 新增菜单
     *
     * @param menu 菜单
     * @return 结果
     */
    @Override
    public int create(Menu menu) {
        return menuDao.insert(menu);
    }

    /**
     * 修改菜单
     *
     * @param menu 菜单
     * @return 结果
     */
    @Override
    public int modifyById(Menu menu) {
        return menuDao.updateById(menu);
    }


    /**
     * 删除菜单信息
     *
     * @param id 菜单ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return menuDao.deleteById(id);
    }

}
