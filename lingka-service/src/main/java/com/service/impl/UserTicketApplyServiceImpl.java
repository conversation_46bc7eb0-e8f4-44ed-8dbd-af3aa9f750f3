package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserTicketApplyDao;
import com.dao.UserTicketDao;
import com.domain.UserTicket;
import com.domain.UserTicketApply;
import com.dto.UserTicketApplyDTO;
import com.query.UserTicketApplyQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserTicketApplyService;

/**
 * 用户门票报名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
@Service
public class UserTicketApplyServiceImpl extends BaseService implements UserTicketApplyService {

    @Autowired
    private UserTicketApplyDao userTicketApplyDao;
    @Autowired
    private UserTicketDao userTicketDao;

    /**
     * 查询用户门票报名
     * 
     * @param id 用户门票报名ID
     * @return 用户门票报名
     */
    @Override
    public UserTicketApplyDTO findById(Long id) {
        return userTicketApplyDao.selectById(id);
    }

    /**
     * 查询用户门票报名列表
     *
     * @param ids 编号集合
     * @return 用户门票报名集合
     */
    @Override
    public List<UserTicketApplyDTO> findByIds(List<Long> ids) {
        return userTicketApplyDao.selectByIds(ids);
    }

    /**
     * 查询用户门票报名列表
     *
     * @param userTicketApplyQuery 用户门票报名
     * @return 用户门票报名
     */
    @Override
    public List<UserTicketApplyDTO> findAll(UserTicketApplyQuery userTicketApplyQuery) {
        return userTicketApplyDao.select(userTicketApplyQuery);
    }

	/**
	 * 分页查询用户门票报名列表
	 *
	 * @param userTicketApplyQuery 用户门票报名
	 * @return 用户门票报名
	 */
	@Override
	public PageInfo<UserTicketApplyDTO> find(UserTicketApplyQuery userTicketApplyQuery) {
        PageHelper.startPage(userTicketApplyQuery.getPageNum(),userTicketApplyQuery.getPageSize());
		List<UserTicketApplyDTO> userTicketApplyDTOList = userTicketApplyDao.select(userTicketApplyQuery);
		return new PageInfo<>(userTicketApplyDTOList);
	}

    /**
     * 查询用户门票报名Map
     *
     * @param ids 编号集合
     * @return 用户门票报名Map
     */
    @Override
    public Map<Long, UserTicketApplyDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserTicketApplyDTO> userTicketApplyDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserTicketApplyDTO> userTicketApplyDTOList =  userTicketApplyDao.selectByIds(ids);
            for (UserTicketApplyDTO userTicketApplyDTO : userTicketApplyDTOList) {
                    userTicketApplyDTOMap.put(userTicketApplyDTO.getId(),userTicketApplyDTO);
            }
        }
        return userTicketApplyDTOMap;
    }

    /**
     * 新增用户门票报名
     *
     * @param userTicketApply 用户门票报名
     * @return 结果
     */
    @Override
    public int create(UserTicketApply userTicketApply) {
        return userTicketApplyDao.insert(userTicketApply);
    }

    /**
     * 修改用户门票报名
     *
     * @param userTicketApply 用户门票报名
     * @return 结果
     */
    @Override
    public int modifyById(UserTicketApply userTicketApply) {
        return userTicketApplyDao.updateById(userTicketApply);
    }

    @Override
    @Transactional
    public int pass(UserTicketApply userTicketApply, List<UserTicket> userTickets) {
        for (UserTicket userTicket : userTickets) {
            userTicketDao.insert(userTicket);
        }
        return userTicketApplyDao.updateById(userTicketApply);
    }


    /**
     * 删除用户门票报名信息
     *
     * @param id 用户门票报名ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userTicketApplyDao.deleteById(id);
    }

}
