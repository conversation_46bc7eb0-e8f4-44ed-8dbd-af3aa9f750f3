package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.ProductSkuDao;
import com.domain.ProductSku;
import com.dto.ProductSkuDTO;
import com.query.ProductSkuQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ProductSkuService;

/**
 * 产品SKUService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class ProductSkuServiceImpl extends BaseService implements ProductSkuService {

    @Autowired
    private ProductSkuDao productSkuDao;

    /**
     * 查询产品SKU
     * 
     * @param id 产品SKUID
     * @return 产品SKU
     */
    @Override
    public ProductSkuDTO findById(Long id) {
        return productSkuDao.selectById(id);
    }

    /**
     * 查询产品SKU列表
     *
     * @param ids 编号集合
     * @return 产品SKU集合
     */
    @Override
    public List<ProductSkuDTO> findByIds(List<Long> ids) {
        return productSkuDao.selectByIds(ids);
    }

    /**
     * 查询产品SKU列表
     *
     * @param productSkuQuery 产品SKU
     * @return 产品SKU
     */
    @Override
    public List<ProductSkuDTO> findAll(ProductSkuQuery productSkuQuery) {
        return productSkuDao.select(productSkuQuery);
    }

	/**
	 * 分页查询产品SKU列表
	 *
	 * @param productSkuQuery 产品SKU
	 * @return 产品SKU
	 */
	@Override
	public PageInfo<ProductSkuDTO> find(ProductSkuQuery productSkuQuery) {
        PageHelper.startPage(productSkuQuery.getPageNum(),productSkuQuery.getPageSize());
		List<ProductSkuDTO> productSkuDTOList = productSkuDao.select(productSkuQuery);
		return new PageInfo<>(productSkuDTOList);
	}

    /**
     * 查询产品SKUMap
     *
     * @param ids 编号集合
     * @return 产品SKUMap
     */
    @Override
    public Map<Long, ProductSkuDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProductSkuDTO> productSkuDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductSkuDTO> productSkuDTOList =  productSkuDao.selectByIds(ids);
            for (ProductSkuDTO productSkuDTO : productSkuDTOList) {
                    productSkuDTOMap.put(productSkuDTO.getId(),productSkuDTO);
            }
        }
        return productSkuDTOMap;
    }

    /**
     * 新增产品SKU
     *
     * @param productSku 产品SKU
     * @return 结果
     */
    @Override
    public int create(ProductSku productSku) {
        return productSkuDao.insert(productSku);
    }

    /**
     * 修改产品SKU
     *
     * @param productSku 产品SKU
     * @return 结果
     */
    @Override
    public int modifyById(ProductSku productSku) {
        return productSkuDao.updateById(productSku);
    }


    /**
     * 删除产品SKU信息
     *
     * @param id 产品SKUID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return productSkuDao.deleteById(id);
    }

}
