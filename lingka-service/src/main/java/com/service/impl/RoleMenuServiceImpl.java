package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.RoleMenuDao;
import com.domain.RoleMenu;
import com.dto.RoleMenuDTO;
import com.query.RoleMenuQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.RoleMenuService;

/**
 * 角色菜单关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
public class RoleMenuServiceImpl extends BaseService implements RoleMenuService {

    @Autowired
    private RoleMenuDao roleMenuDao;

    /**
     * 查询角色菜单关联
     * 
     * @param id 角色菜单关联ID
     * @return 角色菜单关联
     */
    @Override
    public RoleMenuDTO findById(Long id) {
        return roleMenuDao.selectById(id);
    }

    /**
     * 查询角色菜单关联列表
     *
     * @param ids 编号集合
     * @return 角色菜单关联集合
     */
    @Override
    public List<RoleMenuDTO> findByIds(List<Long> ids) {
        return roleMenuDao.selectByIds(ids);
    }

    /**
     * 查询角色菜单关联列表
     *
     * @param roleMenuQuery 角色菜单关联
     * @return 角色菜单关联
     */
    @Override
    public List<RoleMenuDTO> findAll(RoleMenuQuery roleMenuQuery) {
        return roleMenuDao.select(roleMenuQuery);
    }

	/**
	 * 分页查询角色菜单关联列表
	 *
	 * @param roleMenuQuery 角色菜单关联
	 * @return 角色菜单关联
	 */
	@Override
	public PageInfo<RoleMenuDTO> find(RoleMenuQuery roleMenuQuery) {
        PageHelper.startPage(roleMenuQuery.getPageNum(),roleMenuQuery.getPageSize());
		List<RoleMenuDTO> roleMenuDTOList = roleMenuDao.select(roleMenuQuery);
		return new PageInfo<>(roleMenuDTOList);
	}

    /**
     * 查询角色菜单关联Map
     *
     * @param ids 编号集合
     * @return 角色菜单关联Map
     */
    @Override
    public Map<Long, RoleMenuDTO> findMapByIds(List<Long> ids) {
        Map<Long, RoleMenuDTO> roleMenuDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<RoleMenuDTO> roleMenuDTOList =  roleMenuDao.selectByIds(ids);
            for (RoleMenuDTO roleMenuDTO : roleMenuDTOList) {
                    roleMenuDTOMap.put(roleMenuDTO.getId(),roleMenuDTO);
            }
        }
        return roleMenuDTOMap;
    }

    /**
     * 新增角色菜单关联
     *
     * @param roleMenu 角色菜单关联
     * @return 结果
     */
    @Override
    public int create(RoleMenu roleMenu) {
        return roleMenuDao.insert(roleMenu);
    }

    /**
     * 修改角色菜单关联
     *
     * @param roleMenu 角色菜单关联
     * @return 结果
     */
    @Override
    public int modifyById(RoleMenu roleMenu) {
        return roleMenuDao.updateById(roleMenu);
    }


    /**
     * 删除角色菜单关联信息
     *
     * @param id 角色菜单关联ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return roleMenuDao.deleteById(id);
    }

}
