package com.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.constant.DataStatus;
import com.dao.TicketDao;
import com.dao.TicketProductDao;
import com.domain.Ticket;
import com.domain.TicketProduct;
import com.dto.TicketDTO;
import com.dto.TicketProductDTO;
import com.query.TicketProductQuery;
import com.query.TicketQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.TicketService;

/**
 * 门票Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */
@Service
public class TicketServiceImpl extends BaseService implements TicketService {

    @Autowired
    private TicketDao ticketDao;
    @Autowired
    private TicketProductDao ticketProductDao;

    /**
     * 查询门票
     * 
     * @param id 门票ID
     * @return 门票
     */
    @Override
    public TicketDTO findById(Long id) {
        return ticketDao.selectById(id);
    }

    /**
     * 查询门票列表
     *
     * @param ids 编号集合
     * @return 门票集合
     */
    @Override
    public List<TicketDTO> findByIds(List<Long> ids) {
        return ticketDao.selectByIds(ids);
    }

    /**
     * 查询门票列表
     *
     * @param ticketQuery 门票
     * @return 门票
     */
    @Override
    public List<TicketDTO> findAll(TicketQuery ticketQuery) {
        return ticketDao.select(ticketQuery);
    }

	/**
	 * 分页查询门票列表
	 *
	 * @param ticketQuery 门票
	 * @return 门票
	 */
	@Override
	public PageInfo<TicketDTO> find(TicketQuery ticketQuery) {
        PageHelper.startPage(ticketQuery.getPageNum(),ticketQuery.getPageSize());
		List<TicketDTO> ticketDTOList = ticketDao.select(ticketQuery);
		return new PageInfo<>(ticketDTOList);
	}

    /**
     * 查询门票Map
     *
     * @param ids 编号集合
     * @return 门票Map
     */
    @Override
    public Map<Long, TicketDTO> findMapByIds(List<Long> ids) {
        Map<Long, TicketDTO> ticketDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<TicketDTO> ticketDTOList =  ticketDao.selectByIds(ids);
            for (TicketDTO ticketDTO : ticketDTOList) {
                    ticketDTOMap.put(ticketDTO.getId(),ticketDTO);
            }
        }
        return ticketDTOMap;
    }

    /**
     * 新增门票
     *
     * @param ticket 门票
     * @return 结果
     */
    @Override
    public int create(Ticket ticket) {
        return ticketDao.insert(ticket);
    }

    @Override
    @Transactional
    public int create(Ticket ticket, List<TicketProduct> ticketProducts) {
        int rows = ticketDao.insert(ticket);
        if (ticketProducts != null && !ticketProducts.isEmpty()){
            for (TicketProduct ticketProduct : ticketProducts) {
                ticketProduct.setTicketId(ticket.getId());
                ticketProductDao.insert(ticketProduct);
            }
        }
        return rows;
    }

    /**
     * 修改门票
     *
     * @param ticket 门票
     * @return 结果
     */
    @Override
    public int modifyById(Ticket ticket) {
        return ticketDao.updateById(ticket);
    }

    /**
     * 修改门票和门票产品
     *
     * @param ticket 门票
     * @param ticketProducts 门票产品
     * @return 结果
     */
    @Override
    @Transactional
    public int modifyAndTicketProducts(Ticket ticket, List<TicketProduct> ticketProducts) {
        // 修改门票基本信息
        int rows = ticketDao.updateById(ticket);
        // 先查询出来原有的门票产品
        TicketProductQuery ticketProductQuery = new TicketProductQuery();
        ticketProductQuery.setTicketId(ticket.getId());
        ticketProductQuery.setStatus(DataStatus.Y.getCode());
        List<TicketProductDTO> existingTicketProducts = ticketProductDao.select(ticketProductQuery);
        List<Long> existingTicketProductIds = new ArrayList<>();
        if (existingTicketProducts != null && !existingTicketProducts.isEmpty()) {
            for (TicketProductDTO existingTicketProduct : existingTicketProducts) {
                existingTicketProductIds.add(existingTicketProduct.getId());
            }
        }

        // 添加新的门票产品
        if (ticketProducts != null && !ticketProducts.isEmpty()) {
            for (TicketProduct ticketProduct : ticketProducts) {
                ticketProduct.setTicketId(ticket.getId());
                if (ticketProduct.getId() != null) {
                    // 如果有ID，说明是修改现有的
                    existingTicketProductIds.remove(ticketProduct.getId());
                    ticketProductDao.updateById(ticketProduct);
                } else {
                    // 如果没有ID，说明是新增的
                    ticketProductDao.insert(ticketProduct);
                }
            }
        }
        // 将之前没有编辑上的酒水给删除掉
        if (!existingTicketProductIds.isEmpty()){
            for (Long existingTicketProductId : existingTicketProductIds) {
                TicketProduct deleteTicketProduct = new TicketProduct();
                deleteTicketProduct.setId(existingTicketProductId);
                deleteTicketProduct.setStatus(DataStatus.N.getCode());
                deleteTicketProduct.setModifyTime(ticket.getModifyTime());
                ticketProductDao.updateById(deleteTicketProduct);
            }
        }
        return rows;
    }

    /**
     * 删除门票信息
     *
     * @param id 门票ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return ticketDao.deleteById(id);
    }

}
