package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.common.constant.DataStatus;
import com.dao.ShopOpenCloseTimeDao;
import com.domain.ShopOpenCloseTime;
import com.dto.ShopOpenCloseTimeDTO;
import com.github.pagehelper.Page;
import com.query.ShopOpenCloseTimeQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ShopOpenCloseTimeService;

/**
 * 店铺营业时间Service业务层处理
 *
 */
@Service
public class ShopOpenCloseTimeServiceImpl extends BaseService implements ShopOpenCloseTimeService {

    @Autowired
    private ShopOpenCloseTimeDao shopOpenCloseTimeDao;

    /**
     * 查询店铺营业时间
     *
     * @param id 店铺营业时间ID
     * @return 店铺营业时间
     */
    @Override
    public ShopOpenCloseTimeDTO findById(Long id) {
        return shopOpenCloseTimeDao.selectById(id);
    }

    /**
     * 查询店铺营业时间列表
     *
     * @param ids 编号集合
     * @return 店铺营业时间集合
     */
    @Override
    public List<ShopOpenCloseTimeDTO> findByIds(List<Long> ids) {
        return shopOpenCloseTimeDao.selectByIds(ids);
    }

    /**
     * 查询店铺营业时间列表
     *
     * @param shopOpenCloseTimeQuery 店铺营业时间
     * @return 店铺营业时间
     */
    @Override
    public List<ShopOpenCloseTimeDTO> findAll(ShopOpenCloseTimeQuery shopOpenCloseTimeQuery) {
        return shopOpenCloseTimeDao.select(shopOpenCloseTimeQuery);
    }

    /**
     * 分页查询店铺营业时间列表
     *
     * @param shopOpenCloseTimeQuery 店铺营业时间
     * @return 店铺营业时间
     */
    @Override
    public PageInfo<ShopOpenCloseTimeDTO> find(ShopOpenCloseTimeQuery shopOpenCloseTimeQuery) {
        try (Page<ShopOpenCloseTimeDTO> page = PageHelper.startPage(shopOpenCloseTimeQuery.getPageNum(), shopOpenCloseTimeQuery.getPageSize())) {
            shopOpenCloseTimeDao.select(shopOpenCloseTimeQuery);
            return page.toPageInfo();
        }
    }

    /**
     * 查询店铺营业时间Map
     *
     * @param ids 编号集合
     * @return 店铺营业时间Map
     */
    @Override
    public Map<Long, ShopOpenCloseTimeDTO> findMapByIds(List<Long> ids) {
        Map<Long, ShopOpenCloseTimeDTO> shopOpenCloseTimeDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<ShopOpenCloseTimeDTO> shopOpenCloseTimeDTOList = shopOpenCloseTimeDao.selectByIds(ids);
            for (ShopOpenCloseTimeDTO shopOpenCloseTimeDTO : shopOpenCloseTimeDTOList) {
                shopOpenCloseTimeDTOMap.put(shopOpenCloseTimeDTO.getId(), shopOpenCloseTimeDTO);
            }
        }
        return shopOpenCloseTimeDTOMap;
    }

    /**
     * 新增店铺营业时间
     *
     * @param shopOpenCloseTime 店铺营业时间
     * @return 结果
     */
    @Override
    public int create(ShopOpenCloseTime shopOpenCloseTime) {
        shopOpenCloseTime.setStatus(DataStatus.Y.getCode());
        return shopOpenCloseTimeDao.insert(shopOpenCloseTime);
    }

    /**
     * 修改店铺营业时间
     *
     * @param shopOpenCloseTime 店铺营业时间
     * @return 结果
     */
    @Override
    public int modifyById(ShopOpenCloseTime shopOpenCloseTime) {
        return shopOpenCloseTimeDao.updateById(shopOpenCloseTime);
    }


    /**
     * 删除店铺营业时间信息
     *
     * @param id 店铺营业时间ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return shopOpenCloseTimeDao.deleteById(id);
    }

    /**
     * 删除店铺营业时间信息
     *
     * @param shopId 店铺 ID
     * @return 结果
     */
    @Override
    public int removeByShopId(Long shopId) {
        return shopOpenCloseTimeDao.deleteByShopId(shopId);
    }
}
