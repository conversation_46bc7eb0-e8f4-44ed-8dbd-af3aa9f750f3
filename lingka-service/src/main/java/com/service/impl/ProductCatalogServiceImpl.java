package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.ProductCatalogDao;
import com.domain.ProductCatalog;
import com.dto.ProductCatalogDTO;
import com.query.ProductCatalogQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ProductCatalogService;

/**
 * 产品分类Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class ProductCatalogServiceImpl extends BaseService implements ProductCatalogService {

    @Autowired
    private ProductCatalogDao productCatalogDao;

    /**
     * 查询产品分类
     * 
     * @param id 产品分类ID
     * @return 产品分类
     */
    @Override
    public ProductCatalogDTO findById(Long id) {
        return productCatalogDao.selectById(id);
    }

    /**
     * 查询产品分类列表
     *
     * @param ids 编号集合
     * @return 产品分类集合
     */
    @Override
    public List<ProductCatalogDTO> findByIds(List<Long> ids) {
        return productCatalogDao.selectByIds(ids);
    }

    /**
     * 查询产品分类列表
     *
     * @param productCatalogQuery 产品分类
     * @return 产品分类
     */
    @Override
    public List<ProductCatalogDTO> findAll(ProductCatalogQuery productCatalogQuery) {
        return productCatalogDao.select(productCatalogQuery);
    }

    @Override
    public List<ProductCatalogDTO> findAllBySort(ProductCatalogQuery productCatalogQuery) {
        return productCatalogDao.selectBySort(productCatalogQuery);
    }

    /**
	 * 分页查询产品分类列表
	 *
	 * @param productCatalogQuery 产品分类
	 * @return 产品分类
	 */
	@Override
	public PageInfo<ProductCatalogDTO> find(ProductCatalogQuery productCatalogQuery) {
        PageHelper.startPage(productCatalogQuery.getPageNum(),productCatalogQuery.getPageSize());
		List<ProductCatalogDTO> productCatalogDTOList = productCatalogDao.select(productCatalogQuery);
		return new PageInfo<>(productCatalogDTOList);
	}

    /**
     * 查询产品分类Map
     *
     * @param ids 编号集合
     * @return 产品分类Map
     */
    @Override
    public Map<Long, ProductCatalogDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProductCatalogDTO> productCatalogDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductCatalogDTO> productCatalogDTOList =  productCatalogDao.selectByIds(ids);
            for (ProductCatalogDTO productCatalogDTO : productCatalogDTOList) {
                    productCatalogDTOMap.put(productCatalogDTO.getId(),productCatalogDTO);
            }
        }
        return productCatalogDTOMap;
    }

    /**
     * 新增产品分类
     *
     * @param productCatalog 产品分类
     * @return 结果
     */
    @Override
    public int create(ProductCatalog productCatalog) {
        return productCatalogDao.insert(productCatalog);
    }

    /**
     * 修改产品分类
     *
     * @param productCatalog 产品分类
     * @return 结果
     */
    @Override
    public int modifyById(ProductCatalog productCatalog) {
        return productCatalogDao.updateById(productCatalog);
    }


    /**
     * 删除产品分类信息
     *
     * @param id 产品分类ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return productCatalogDao.deleteById(id);
    }

}
