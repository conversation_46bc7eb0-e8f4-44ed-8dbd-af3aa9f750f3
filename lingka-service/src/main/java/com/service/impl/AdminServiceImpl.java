package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.AdminDao;
import com.domain.Admin;
import com.dto.AdminDTO;
import com.query.AdminQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.AdminService;

/**
 * 管理员Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Service
public class AdminServiceImpl extends BaseService implements AdminService {

    @Autowired
    private AdminDao adminDao;

    /**
     * 查询管理员
     *
     * @param id 管理员ID
     * @return 管理员
     */
    @Override
    public AdminDTO findById(Long id) {
        return adminDao.selectById(id);
    }

    @Override
    public AdminDTO findByUsername(String username) {
        return adminDao.selectByUsername(username);
    }

    @Override
    public AdminDTO findByMobile(String mobile) {
        return adminDao.selectByMobile(mobile);
    }

    /**
     * 查询管理员列表
     *
     * @param ids 编号集合
     * @return 管理员集合
     */
    @Override
    public List<AdminDTO> findByIds(List<Long> ids) {
        return adminDao.selectByIds(ids);
    }


    /**
     * 查询管理员列表
     *
     * @param adminQuery 管理员
     * @return 管理员
     */
    @Override
    public List<AdminDTO> findAll(AdminQuery adminQuery) {
        return adminDao.select(adminQuery);
    }

    /**
     * 分页查询管理员列表
     *
     * @param adminQuery 管理员
     * @return 管理员
     */
    @Override
    public PageInfo<AdminDTO> find(AdminQuery adminQuery) {
        PageHelper.startPage(adminQuery.getPageNum(), adminQuery.getPageSize());
        List<AdminDTO> adminDTOList = adminDao.select(adminQuery);
        return new PageInfo<>(adminDTOList);
    }

    /**
     * 查询管理员Map
     *
     * @param ids 编号集合
     * @return 管理员Map
     */
    @Override
    public Map<Long, AdminDTO> findMapByIds(List<Long> ids) {
        Map<Long, AdminDTO> adminDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<AdminDTO> adminDTOList = adminDao.selectByIds(ids);
            for (AdminDTO adminDTO : adminDTOList) {
                adminDTOMap.put(adminDTO.getId(), adminDTO);
            }
        }
        return adminDTOMap;
    }


    /**
     * 新增管理员
     *
     * @param admin 管理员
     * @return 结果
     */
    @Override
    public int create(Admin admin) {
        return adminDao.insert(admin);
    }

    /**
     * 修改管理员
     *
     * @param admin 管理员
     * @return 结果
     */
    @Override
    public int modifyById(Admin admin) {
        return adminDao.updateById(admin);
    }


    /**
     * 删除管理员信息
     *
     * @param id 管理员ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return adminDao.deleteById(id);
    }

}
