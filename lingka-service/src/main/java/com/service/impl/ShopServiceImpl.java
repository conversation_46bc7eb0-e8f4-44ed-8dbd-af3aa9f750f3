package com.service.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.common.constant.DataStatus;
import com.common.constant.ShopState;
import com.dao.ShopConfigDao;
import com.dao.ShopDao;
import com.domain.Shop;
import com.domain.ShopConfig;
import com.dto.ShopConfigDTO;
import com.dto.ShopDTO;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.query.ShopConfigQuery;
import com.query.ShopQuery;
import com.service.ShopService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

@Service
public class ShopServiceImpl extends BaseService implements ShopService {
    @Autowired
    private ShopDao shopDao;

    @Autowired
    private ShopConfigDao shopConfigDao;

    /**
     * 查询店铺
     *
     * @param id 店铺主键
     * @return 店铺
     */
    @Override
    public ShopDTO selectShopById(Long id) {
        return shopDao.selectShopById(id);
    }

    @Override
    public List<ShopDTO> selectShopByIds(List<Long> ids) {
        return shopDao.selectShopByIds(ids);
    }

    @Override
    public Map<Long, ShopDTO> findMapByIds(List<Long> ids) {
        Map<Long, ShopDTO> shopDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<ShopDTO> shopDTOList = shopDao.selectShopByIds(ids);
            for (ShopDTO shopDTO : shopDTOList) {
                shopDTOMap.put(shopDTO.getId(), shopDTO);
            }
        }
        return shopDTOMap;
    }

    /**
     * 查询店铺列表
     *
     * @param shopQuery 店铺
     * @return 店铺
     */
    @Override
    public PageInfo<ShopDTO> selectShopList(ShopQuery shopQuery) {
        try (Page<ShopDTO> page = PageHelper.startPage(shopQuery.getPageNum(), shopQuery.getPageSize())) {
            shopDao.selectShopList(shopQuery);
            return page.toPageInfo();
        }
    }

    @Override
    public List<ShopDTO> findAll(ShopQuery shopQuery) {
        return shopDao.selectShopList(shopQuery);
    }

    /**
     * 新增店铺
     *
     * @param shop 店铺
     * @return 结果
     */
    @Override
    public int insertShop(Shop shop) {
        shop.setStatus(DataStatus.Y.getCode());
        return shopDao.insertShop(shop);
    }

    @Override
    @Transactional
    public int createShopAndShopConfig(Shop shop, ShopConfig shopConfig) {
        int count = shopDao.insertShop(shop);
        shopConfig.setShopId(shop.getId());
        // 设置初始营业状态为 close_manual
        shopConfig.setState(ShopState.CLOSE_MANUAL.getCode());
        ShopConfigDTO shopConfigDTOOld = shopConfigDao.selectByShopId(shop.getId());
        if (shopConfigDTOOld != null) {
            ShopConfig shopConfigOld = new ShopConfig(shopConfigDTOOld);
            shopConfigOld.setStatus(DataStatus.Y.getCode());
            shopConfigDao.updateById(shopConfigOld);
        } else {
            count += shopConfigDao.insert(shopConfig);
        }
        return count;
    }


    /**
     * 修改店铺
     *
     * @param shop 店铺
     * @return 结果
     */
    @Override
    public int updateShop(Shop shop) {
        return shopDao.updateShop(shop);
    }

    /**
     * 批量删除店铺
     *
     * @param ids 需要删除的店铺主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteShopByIds(String ids) {
        String[] shopIdList = StringUtils.commaDelimitedListToStringArray(ids);
        int count = 0;
        for (String shopId : shopIdList) {
            count += deleteShopById(Long.valueOf(shopId));
        }
        return count;
    }

    /**
     * 删除店铺信息
     *
     * @param id 店铺主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteShopById(Long id) {
        Shop shop = new Shop();
        shop.setId(id);
        shop.setStatus(DataStatus.N.getCode());
        ShopConfigQuery shopConfigQuery = new ShopConfigQuery();
        shopConfigQuery.setShopId(id);
        List<ShopConfigDTO> shopConfigs = shopConfigDao.select(shopConfigQuery);
        for (ShopConfigDTO shopConfigDTO : shopConfigs) {
            shopConfigDao.deleteById(shopConfigDTO.getId());
        }
        return shopDao.updateShop(shop);
    }
}
