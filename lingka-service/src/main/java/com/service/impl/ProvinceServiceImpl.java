package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.ProvinceDao;
import com.domain.Province;
import com.dto.ProvinceDTO;
import com.query.ProvinceQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ProvinceService;

/**
 * 省份Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
public class ProvinceServiceImpl extends BaseService implements ProvinceService {

    @Autowired
    private ProvinceDao provinceDao;

    /**
     * 查询省份
     *
     * @param id 省份ID
     * @return 省份
     */
    @Override
    public ProvinceDTO findById(Long id) {
        return provinceDao.selectById(id);
    }

    /**
     * 查询省份列表
     *
     * @param ids 编号集合
     * @return 省份集合
     */
    @Override
    public List<ProvinceDTO> findByIds(List<Long> ids) {
        return provinceDao.selectByIds(ids);
    }

    /**
     * 查询省份列表
     *
     * @param provinceQuery 省份
     * @return 省份
     */
    @Override
    public List<ProvinceDTO> findAll(ProvinceQuery provinceQuery) {
        return provinceDao.select(provinceQuery);
    }

    /**
     * 分页查询省份列表
     *
     * @param provinceQuery 省份
     * @return 省份
     */
    @Override
    public PageInfo<ProvinceDTO> find(ProvinceQuery provinceQuery) {
        PageHelper.startPage(provinceQuery.getPageNum(), provinceQuery.getPageSize());
        List<ProvinceDTO> provinceDTOList = provinceDao.select(provinceQuery);
        return new PageInfo<>(provinceDTOList);
    }

    /**
     * 查询省份Map
     *
     * @param ids 编号集合
     * @return 省份Map
     */
    @Override
    public Map<Long, ProvinceDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProvinceDTO> provinceDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<ProvinceDTO> provinceDTOList = provinceDao.selectByIds(ids);
            for (ProvinceDTO provinceDTO : provinceDTOList) {
                provinceDTOMap.put(provinceDTO.getId(), provinceDTO);
            }
        }
        return provinceDTOMap;
    }

    /**
     * 新增省份
     *
     * @param province 省份
     * @return 结果
     */
    @Override
    public int create(Province province) {
        return provinceDao.insert(province);
    }

    /**
     * 修改省份
     *
     * @param province 省份
     * @return 结果
     */
    @Override
    public int modifyById(Province province) {
        return provinceDao.updateById(province);
    }


    /**
     * 删除省份信息
     *
     * @param id 省份ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return provinceDao.deleteById(id);
    }

}
