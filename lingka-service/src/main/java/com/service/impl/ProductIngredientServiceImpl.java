package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.ProductIngredientDao;
import com.domain.ProductIngredient;
import com.dto.ProductIngredientDTO;
import com.query.ProductIngredientQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.ProductIngredientService;

/**
 * 产品小料Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
@Service
public class ProductIngredientServiceImpl extends BaseService implements ProductIngredientService {

    @Autowired
    private ProductIngredientDao productIngredientDao;

    /**
     * 查询产品小料
     * 
     * @param id 产品小料ID
     * @return 产品小料
     */
    @Override
    public ProductIngredientDTO findById(Long id) {
        return productIngredientDao.selectById(id);
    }

    /**
     * 查询产品小料列表
     *
     * @param ids 编号集合
     * @return 产品小料集合
     */
    @Override
    public List<ProductIngredientDTO> findByIds(List<Long> ids) {
        return productIngredientDao.selectByIds(ids);
    }

    /**
     * 查询产品小料列表
     *
     * @param productIngredientQuery 产品小料
     * @return 产品小料
     */
    @Override
    public List<ProductIngredientDTO> findAll(ProductIngredientQuery productIngredientQuery) {
        return productIngredientDao.select(productIngredientQuery);
    }

	/**
	 * 分页查询产品小料列表
	 *
	 * @param productIngredientQuery 产品小料
	 * @return 产品小料
	 */
	@Override
	public PageInfo<ProductIngredientDTO> find(ProductIngredientQuery productIngredientQuery) {
        PageHelper.startPage(productIngredientQuery.getPageNum(),productIngredientQuery.getPageSize());
		List<ProductIngredientDTO> productIngredientDTOList = productIngredientDao.select(productIngredientQuery);
		return new PageInfo<>(productIngredientDTOList);
	}

    /**
     * 查询产品小料Map
     *
     * @param ids 编号集合
     * @return 产品小料Map
     */
    @Override
    public Map<Long, ProductIngredientDTO> findMapByIds(List<Long> ids) {
        Map<Long, ProductIngredientDTO> productIngredientDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<ProductIngredientDTO> productIngredientDTOList =  productIngredientDao.selectByIds(ids);
            for (ProductIngredientDTO productIngredientDTO : productIngredientDTOList) {
                    productIngredientDTOMap.put(productIngredientDTO.getId(),productIngredientDTO);
            }
        }
        return productIngredientDTOMap;
    }

    /**
     * 新增产品小料
     *
     * @param productIngredient 产品小料
     * @return 结果
     */
    @Override
    public int create(ProductIngredient productIngredient) {
        return productIngredientDao.insert(productIngredient);
    }

    /**
     * 修改产品小料
     *
     * @param productIngredient 产品小料
     * @return 结果
     */
    @Override
    public int modifyById(ProductIngredient productIngredient) {
        return productIngredientDao.updateById(productIngredient);
    }


    /**
     * 删除产品小料信息
     *
     * @param id 产品小料ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return productIngredientDao.deleteById(id);
    }

}
