package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.UserTicketDao;
import com.dao.UserTicketGiftRecordDao;
import com.domain.UserTicket;
import com.domain.UserTicketGiftRecord;
import com.dto.UserTicketGiftRecordDTO;
import com.query.UserTicketGiftRecordQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserTicketGiftRecordService;

/**
 * 用户门票转赠记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
@Service
public class UserTicketGiftRecordServiceImpl extends BaseService implements UserTicketGiftRecordService {

    @Autowired
    private UserTicketGiftRecordDao userTicketGiftRecordDao;
    @Autowired
    private UserTicketDao userTicketDao;

    /**
     * 查询用户门票转赠记录
     * 
     * @param id 用户门票转赠记录ID
     * @return 用户门票转赠记录
     */
    @Override
    public UserTicketGiftRecordDTO findById(Long id) {
        return userTicketGiftRecordDao.selectById(id);
    }

    /**
     * 查询用户门票转赠记录列表
     *
     * @param ids 编号集合
     * @return 用户门票转赠记录集合
     */
    @Override
    public List<UserTicketGiftRecordDTO> findByIds(List<Long> ids) {
        return userTicketGiftRecordDao.selectByIds(ids);
    }

    /**
     * 查询用户门票转赠记录列表
     *
     * @param userTicketGiftRecordQuery 用户门票转赠记录
     * @return 用户门票转赠记录
     */
    @Override
    public List<UserTicketGiftRecordDTO> findAll(UserTicketGiftRecordQuery userTicketGiftRecordQuery) {
        return userTicketGiftRecordDao.select(userTicketGiftRecordQuery);
    }

	/**
	 * 分页查询用户门票转赠记录列表
	 *
	 * @param userTicketGiftRecordQuery 用户门票转赠记录
	 * @return 用户门票转赠记录
	 */
	@Override
	public PageInfo<UserTicketGiftRecordDTO> find(UserTicketGiftRecordQuery userTicketGiftRecordQuery) {
        PageHelper.startPage(userTicketGiftRecordQuery.getPageNum(),userTicketGiftRecordQuery.getPageSize());
		List<UserTicketGiftRecordDTO> userTicketGiftRecordDTOList = userTicketGiftRecordDao.select(userTicketGiftRecordQuery);
		return new PageInfo<>(userTicketGiftRecordDTOList);
	}

    /**
     * 查询用户门票转赠记录Map
     *
     * @param ids 编号集合
     * @return 用户门票转赠记录Map
     */
    @Override
    public Map<Long, UserTicketGiftRecordDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserTicketGiftRecordDTO> userTicketGiftRecordDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserTicketGiftRecordDTO> userTicketGiftRecordDTOList =  userTicketGiftRecordDao.selectByIds(ids);
            for (UserTicketGiftRecordDTO userTicketGiftRecordDTO : userTicketGiftRecordDTOList) {
                    userTicketGiftRecordDTOMap.put(userTicketGiftRecordDTO.getId(),userTicketGiftRecordDTO);
            }
        }
        return userTicketGiftRecordDTOMap;
    }

    /**
     * 新增用户门票转赠记录
     *
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    @Override
    public int create(UserTicketGiftRecord userTicketGiftRecord) {
        return userTicketGiftRecordDao.insert(userTicketGiftRecord);
    }

    /**
     * 修改用户门票转赠记录
     *
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    @Override
    public int modifyById(UserTicketGiftRecord userTicketGiftRecord) {
        return userTicketGiftRecordDao.updateById(userTicketGiftRecord);
    }

    @Override
    @Transactional
    public int modifyById(UserTicketGiftRecord userTicketGiftRecord, UserTicket userTicket) {
        userTicketDao.updateById(userTicket);
        return userTicketGiftRecordDao.updateById(userTicketGiftRecord);
    }


    /**
     * 删除用户门票转赠记录信息
     *
     * @param id 用户门票转赠记录ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userTicketGiftRecordDao.deleteById(id);
    }

}
