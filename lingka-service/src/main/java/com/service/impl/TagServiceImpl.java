package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.TagDao;
import com.domain.Tag;
import com.dto.TagDTO;
import com.query.TagQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.TagService;

/**
 * 标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
public class TagServiceImpl extends BaseService implements TagService {

    @Autowired
    private TagDao tagDao;

    /**
     * 查询标签
     *
     * @param id 标签ID
     * @return 标签
     */
    @Override
    public TagDTO findById(Long id) {
        return tagDao.selectById(id);
    }

    /**
     * 查询标签列表
     *
     * @param ids 编号集合
     * @return 标签集合
     */
    @Override
    public List<TagDTO> findByIds(List<Long> ids) {
        return tagDao.selectByIds(ids);
    }

    /**
     * 查询标签列表
     *
     * @param tagQuery 标签
     * @return 标签
     */
    @Override
    public List<TagDTO> findAll(TagQuery tagQuery) {
        return tagDao.select(tagQuery);
    }

    /**
     * 分页查询标签列表
     *
     * @param tagQuery 标签
     * @return 标签
     */
    @Override
    public PageInfo<TagDTO> find(TagQuery tagQuery) {
        PageHelper.startPage(tagQuery.getPageNum(), tagQuery.getPageSize());
        List<TagDTO> tagDTOList = tagDao.select(tagQuery);
        return new PageInfo<>(tagDTOList);
    }

    /**
     * 查询标签Map
     *
     * @param ids 编号集合
     * @return 标签Map
     */
    @Override
    public Map<Long, TagDTO> findMapByIds(List<Long> ids) {
        Map<Long, TagDTO> tagDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<TagDTO> tagDTOList = tagDao.selectByIds(ids);
            for (TagDTO tagDTO : tagDTOList) {
                tagDTOMap.put(tagDTO.getId(), tagDTO);
            }
        }
        return tagDTOMap;
    }

    /**
     * 新增标签
     *
     * @param tag 标签
     * @return 结果
     */
    @Override
    public int create(Tag tag) {
        return tagDao.insert(tag);
    }

    /**
     * 修改标签
     *
     * @param tag 标签
     * @return 结果
     */
    @Override
    public int modifyById(Tag tag) {
        return tagDao.updateById(tag);
    }


    /**
     * 删除标签信息
     *
     * @param id 标签ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return tagDao.deleteById(id);
    }

}
