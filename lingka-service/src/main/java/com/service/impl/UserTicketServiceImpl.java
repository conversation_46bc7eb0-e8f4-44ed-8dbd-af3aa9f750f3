package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.UserTicketDao;
import com.domain.UserTicket;
import com.dto.UserTicketDTO;
import com.query.UserTicketQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.UserTicketService;

/**
 * 用户门票Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */
@Service
public class UserTicketServiceImpl extends BaseService implements UserTicketService {

    @Autowired
    private UserTicketDao userTicketDao;

    /**
     * 查询用户门票
     * 
     * @param id 用户门票ID
     * @return 用户门票
     */
    @Override
    public UserTicketDTO findById(Long id) {
        return userTicketDao.selectById(id);
    }

    /**
     * 查询用户门票列表
     *
     * @param ids 编号集合
     * @return 用户门票集合
     */
    @Override
    public List<UserTicketDTO> findByIds(List<Long> ids) {
        return userTicketDao.selectByIds(ids);
    }

    /**
     * 查询用户门票列表
     *
     * @param userTicketQuery 用户门票
     * @return 用户门票
     */
    @Override
    public List<UserTicketDTO> findAll(UserTicketQuery userTicketQuery) {
        return userTicketDao.select(userTicketQuery);
    }

	/**
	 * 分页查询用户门票列表
	 *
	 * @param userTicketQuery 用户门票
	 * @return 用户门票
	 */
	@Override
	public PageInfo<UserTicketDTO> find(UserTicketQuery userTicketQuery) {
        PageHelper.startPage(userTicketQuery.getPageNum(),userTicketQuery.getPageSize());
		List<UserTicketDTO> userTicketDTOList = userTicketDao.select(userTicketQuery);
		return new PageInfo<>(userTicketDTOList);
	}

    /**
     * 查询用户门票Map
     *
     * @param ids 编号集合
     * @return 用户门票Map
     */
    @Override
    public Map<Long, UserTicketDTO> findMapByIds(List<Long> ids) {
        Map<Long, UserTicketDTO> userTicketDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()){
            List<UserTicketDTO> userTicketDTOList =  userTicketDao.selectByIds(ids);
            for (UserTicketDTO userTicketDTO : userTicketDTOList) {
                    userTicketDTOMap.put(userTicketDTO.getId(),userTicketDTO);
            }
        }
        return userTicketDTOMap;
    }

    /**
     * 新增用户门票
     *
     * @param userTicket 用户门票
     * @return 结果
     */
    @Override
    public int create(UserTicket userTicket) {
        return userTicketDao.insert(userTicket);
    }

    /**
     * 修改用户门票
     *
     * @param userTicket 用户门票
     * @return 结果
     */
    @Override
    public int modifyById(UserTicket userTicket) {
        return userTicketDao.updateById(userTicket);
    }


    /**
     * 删除用户门票信息
     *
     * @param id 用户门票ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return userTicketDao.deleteById(id);
    }

}
