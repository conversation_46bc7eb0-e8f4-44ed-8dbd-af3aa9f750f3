package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.dao.CityDao;
import com.domain.City;
import com.dto.CityDTO;
import com.query.CityQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.CityService;

/**
 * 城市Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-06
 */
@Service
public class CityServiceImpl extends BaseService implements CityService {

    @Autowired
    private CityDao cityDao;

    /**
     * 查询城市
     *
     * @param id 城市ID
     * @return 城市
     */
    @Override
    public CityDTO findById(Long id) {
        return cityDao.selectById(id);
    }

    /**
     * 查询城市列表
     *
     * @param ids 编号集合
     * @return 城市集合
     */
    @Override
    public List<CityDTO> findByIds(List<Long> ids) {
        return cityDao.selectByIds(ids);
    }

    /**
     * 查询城市列表
     *
     * @param cityQuery 城市
     * @return 城市
     */
    @Override
    public List<CityDTO> findAll(CityQuery cityQuery) {
        return cityDao.select(cityQuery);
    }

    /**
     * 分页查询城市列表
     *
     * @param cityQuery 城市
     * @return 城市
     */
    @Override
    public PageInfo<CityDTO> find(CityQuery cityQuery) {
        PageHelper.startPage(cityQuery.getPageNum(), cityQuery.getPageSize());
        List<CityDTO> cityDTOList = cityDao.select(cityQuery);
        return new PageInfo<>(cityDTOList);
    }

    /**
     * 查询城市Map
     *
     * @param ids 编号集合
     * @return 城市Map
     */
    @Override
    public Map<Long, CityDTO> findMapByIds(List<Long> ids) {
        Map<Long, CityDTO> cityDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<CityDTO> cityDTOList = cityDao.selectByIds(ids);
            for (CityDTO cityDTO : cityDTOList) {
                cityDTOMap.put(cityDTO.getId(), cityDTO);
            }
        }
        return cityDTOMap;
    }

    /**
     * 新增城市
     *
     * @param city 城市
     * @return 结果
     */
    @Override
    public int create(City city) {
        return cityDao.insert(city);
    }

    /**
     * 修改城市
     *
     * @param city 城市
     * @return 结果
     */
    @Override
    public int modifyById(City city) {
        return cityDao.updateById(city);
    }


    /**
     * 删除城市信息
     *
     * @param id 城市ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return cityDao.deleteById(id);
    }

}
