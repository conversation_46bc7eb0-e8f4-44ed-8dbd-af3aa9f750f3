package com.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.dao.OrderDao;
import com.dao.OrderProductDao;
import com.dao.OrderTicketDao;
import com.dao.OrderTicketDetailDao;
import com.dao.UserCartDao;
import com.domain.Order;
import com.domain.OrderProduct;
import com.domain.OrderTicket;
import com.domain.OrderTicketDetail;
import com.domain.UserCart;
import com.dto.OrderDTO;
import com.dto.OrderProductDTO;
import com.query.OrderQuery;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.service.OrderService;

/**
 * 订单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-09-06
 */
@Service
public class OrderServiceImpl extends BaseService implements OrderService {

    @Autowired
    private OrderDao orderDao;
    @Autowired
    private OrderProductDao orderProductDao;
    @Autowired
    private UserCartDao userCartDao;
    @Autowired
    private OrderTicketDao orderTicketDao;
    @Autowired
    private OrderTicketDetailDao orderTicketDetailDao;

    /**
     * 查询订单
     *
     * @param id 订单ID
     * @return 订单
     */
    @Override
    public OrderDTO findById(Long id) {
        return orderDao.selectById(id);
    }

    /**
     * 查询订单列表
     *
     * @param ids 编号集合
     * @return 订单集合
     */
    @Override
    public List<OrderDTO> findByIds(List<Long> ids) {
        return orderDao.selectByIds(ids);
    }

    /**
     * 查询订单列表
     *
     * @param orderQuery 订单
     * @return 订单
     */
    @Override
    public List<OrderDTO> findAll(OrderQuery orderQuery) {
        return orderDao.select(orderQuery);
    }

    /**
     * 分页查询订单列表
     *
     * @param orderQuery 订单
     * @return 订单
     */
    @Override
    public PageInfo<OrderDTO> find(OrderQuery orderQuery) {
        PageHelper.startPage(orderQuery.getPageNum(), orderQuery.getPageSize());
        List<OrderDTO> orderDTOList = orderDao.select(orderQuery);
        return new PageInfo<>(orderDTOList);
    }

    /**
     * 查询订单Map
     *
     * @param ids 编号集合
     * @return 订单Map
     */
    @Override
    public Map<Long, OrderDTO> findMapByIds(List<Long> ids) {
        Map<Long, OrderDTO> orderDTOMap = new HashMap<>();
        if (ids != null && !ids.isEmpty()) {
            List<OrderDTO> orderDTOList = orderDao.selectByIds(ids);
            for (OrderDTO orderDTO : orderDTOList) {
                orderDTOMap.put(orderDTO.getId(), orderDTO);
            }
        }
        return orderDTOMap;
    }

    /**
     * 新增订单
     *
     * @param order 订单
     * @return 结果
     */
    @Override
    public int create(Order order) {
        return orderDao.insert(order);
    }

    @Override
    @Transactional
    public int create(Order order,OrderTicket orderTicket, List<OrderTicketDetail> orderTicketDetails) {
        int i = orderDao.insert(order);
        orderTicket.setOrderId(order.getId());
        orderTicketDao.insert(orderTicket);
        if (orderTicketDetails != null && !orderTicketDetails.isEmpty()) {
            for (OrderTicketDetail orderTicketDetail : orderTicketDetails) {
                orderTicketDetail.setOrderId(order.getId());
                orderTicketDetailDao.insert(orderTicketDetail);
            }
        }
        return i;
    }

    @Override
    @Transactional
    public int create(Order order, List<OrderProduct> orderProducts, List<UserCart> userCarts) {
        int i = orderDao.insert(order);
        for (OrderProduct orderProduct : orderProducts) {
            orderProduct.setOrderId(order.getId());
            orderProductDao.insert(orderProduct);
        }
        for (UserCart userCart : userCarts) {
            //userCartDao.updateById(userCart);
        }
        return i;
    }

    /**
     * 修改订单
     *
     * @param order 订单
     * @return 结果
     */
    @Override
    public int modifyById(Order order) {
        return orderDao.updateById(order);
    }


    /**
     * 删除订单信息
     *
     * @param id 订单ID
     * @return 结果
     */
    @Override
    public int removeById(Long id) {
        return orderDao.deleteById(id);
    }

}
