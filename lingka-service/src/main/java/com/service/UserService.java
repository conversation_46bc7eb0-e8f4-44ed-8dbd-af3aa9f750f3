package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.User;
import com.dto.UserDTO;
import com.query.UserQuery;

/**
 * 用户Service接口
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
public interface UserService {
    /**
     * 查询用户
     *
     * @param id 用户ID
     * @return 用户
     */
    UserDTO findById(Long id);

    /**
     * 查询用户
     *
     * @param openid 用户openid
     * @return 用户
     */
    UserDTO findByOpenid(String openid);

    /**
     * 查询用户
     *
     * @param mobile 用户手机号
     * @return 用户
     */
    UserDTO findByMobile(String mobile);

    /**
     * 查询用户列表
     *
     * @param ids 编号集合
     * @return 用户集合
     */
    List<UserDTO> findByIds(List<Long> ids);

    /**
     * 查询用户列表
     *
     * @param userQuery 用户
     * @return 用户集合
     */
    List<UserDTO> findAll(UserQuery userQuery);

    /**
     * 分页查询用户列表
     *
     * @param userQuery 用户
     * @return 用户集合
     */
    PageInfo<UserDTO> find(UserQuery userQuery);

    /**
     * 查询用户Map
     *
     * @param ids 编号集合
     * @return 用户Map
     */
    Map<Long, UserDTO> findMapByIds(List<Long> ids);

    /**
     * 新增用户
     *
     * @param user 用户
     * @return 结果
     */
    int create(User user);

    /**
     * 修改用户
     *
     * @param user 用户
     * @return 结果
     */
    int modifyById(User user);

    /**
     * 增量修改用户配置
     *
     * @param params 差异用户配置
     * @return 更新数量
     */
    int updatePartialById(Map<String, Object> params);

    /**
     * 删除用户信息
     *
     * @param id 用户id
     * @return 结果
     */
    int removeById(Long id);
}
