package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.ProductCatalog;
import com.dto.ProductCatalogDTO;
import com.query.ProductCatalogQuery;

/**
 * 产品分类Service接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */
public interface ProductCatalogService {
    /**
     * 查询产品分类
     * 
     * @param id 产品分类ID
     * @return 产品分类
     */
    ProductCatalogDTO findById(Long id);

    /**
     * 查询产品分类列表
     *
     * @param ids 编号集合
     * @return 产品分类集合
     */
    List<ProductCatalogDTO> findByIds(List<Long> ids);

    /**
     * 查询产品分类列表
     * 
     * @param productCatalogQuery 产品分类
     * @return 产品分类集合
     */
    List<ProductCatalogDTO> findAll(ProductCatalogQuery productCatalogQuery);



    /**
     * 排序查询产品分类列表
     *
     * @param productCatalogQuery 产品分类
     * @return 产品分类集合
     */
    List<ProductCatalogDTO> findAllBySort(ProductCatalogQuery productCatalogQuery);

	/**
	 *  分页查询产品分类列表
	 *
	 * @param productCatalogQuery 产品分类
	 * @return 产品分类集合
	 */
	PageInfo<ProductCatalogDTO> find(ProductCatalogQuery productCatalogQuery);

    /**
     * 查询产品分类Map
     *
     * @param ids 编号集合
     * @return 产品分类Map
     */
    Map<Long, ProductCatalogDTO> findMapByIds(List<Long> ids);

    /**
     * 新增产品分类
     * 
     * @param productCatalog 产品分类
     * @return 结果
     */
    int create(ProductCatalog productCatalog);

    /**
     * 修改产品分类
     * 
     * @param productCatalog 产品分类
     * @return 结果
     */
    int modifyById(ProductCatalog productCatalog);

    /**
     * 删除产品分类信息
     * 
     * @param id 产品分类id
     * @return 结果
     */
   int removeById(Long id);
}
