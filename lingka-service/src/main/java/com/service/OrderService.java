package com.service;

import java.util.List;
import java.util.Map;

import com.domain.OrderProduct;
import com.domain.OrderTicket;
import com.domain.OrderTicketDetail;
import com.domain.UserCart;
import com.github.pagehelper.PageInfo;
import com.domain.Order;
import com.dto.OrderDTO;
import com.query.OrderQuery;

/**
 * 订单Service接口
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */
public interface OrderService {
    /**
     * 查询订单
     * 
     * @param id 订单ID
     * @return 订单
     */
    OrderDTO findById(Long id);

    /**
     * 查询订单列表
     *
     * @param ids 编号集合
     * @return 订单集合
     */
    List<OrderDTO> findByIds(List<Long> ids);

    /**
     * 查询订单列表
     * 
     * @param orderQuery 订单
     * @return 订单集合
     */
    List<OrderDTO> findAll(OrderQuery orderQuery);

	/**
	 *  分页查询订单列表
	 *
	 * @param orderQuery 订单
	 * @return 订单集合
	 */
	PageInfo<OrderDTO> find(OrderQuery orderQuery);

    /**
     * 查询订单Map
     *
     * @param ids 编号集合
     * @return 订单Map
     */
    Map<Long, OrderDTO> findMapByIds(List<Long> ids);

    /**
     * 新增订单
     * 
     * @param order 订单
     * @return 结果
     */
    int create(Order order);

    /**
     * 新增订单
     *
     * @param order 订单
     * @return 结果
     */
    int create(Order order,OrderTicket orderTicket, List<OrderTicketDetail> orderTicketDetails);


    /**
     * 新增订单
     *
     * @param order 订单
     * @return 结果
     */
    int create(Order order, List<OrderProduct> orderProducts, List<UserCart> userCarts);

    /**
     * 修改订单
     * 
     * @param order 订单
     * @return 结果
     */
    int modifyById(Order order);

    /**
     * 删除订单信息
     * 
     * @param id 订单id
     * @return 结果
     */
   int removeById(Long id);
}
