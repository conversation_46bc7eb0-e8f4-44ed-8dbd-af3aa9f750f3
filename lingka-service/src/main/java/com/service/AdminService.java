package com.service;

import java.util.List;
import java.util.Map;

import com.github.pagehelper.PageInfo;
import com.domain.Admin;
import com.dto.AdminDTO;
import com.query.AdminQuery;

/**
 * 管理员Service接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
public interface AdminService {
    /**
     * 查询管理员
     *
     * @param id 管理员ID
     * @return 管理员
     */
    AdminDTO findById(Long id);

    /**
     * 查询管理员
     *
     * @param username 管理员用户名
     * @return 管理员
     */
    AdminDTO findByUsername(String username);

    /**
     * 查询管理员
     *
     * @param mobile 管理员手机号
     * @return 管理员
     */
    AdminDTO findByMobile(String mobile);

    /**
     * 查询管理员列表
     *
     * @param ids 编号集合
     * @return 管理员集合
     */
    List<AdminDTO> findByIds(List<Long> ids);

    /**
     * 查询管理员列表
     *
     * @param adminQuery 管理员
     * @return 管理员集合
     */
    List<AdminDTO> findAll(AdminQuery adminQuery);

    /**
     * 分页查询管理员列表
     *
     * @param adminQuery 管理员
     * @return 管理员集合
     */
    PageInfo<AdminDTO> find(AdminQuery adminQuery);

    /**
     * 查询管理员Map
     *
     * @param ids 编号集合
     * @return 管理员Map
     */
    Map<Long, AdminDTO> findMapByIds(List<Long> ids);

    /**
     * 新增管理员
     *
     * @param admin 管理员
     * @return 结果
     */
    int create(Admin admin);

    /**
     * 修改管理员
     *
     * @param admin 管理员
     * @return 结果
     */
    int modifyById(Admin admin);

    /**
     * 删除管理员信息
     *
     * @param id 管理员id
     * @return 结果
     */
    int removeById(Long id);
}
