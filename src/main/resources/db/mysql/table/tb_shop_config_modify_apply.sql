drop table if exists `tb_shop_config_modify_apply` ;
create table `tb_shop_config_modify_apply` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `name` varchar(50) comment '店铺名称',
    `photo` varchar(330) comment '店铺介绍头图文件',
    `description` varchar(2000) comment '店铺介绍描述',
    `audit` varchar(1) comment '审核状态(0:驳回 1:审核中 2:通过 3:修改内容自动作废)',
    `audit_admin_id` bigint comment '审核人',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '店铺配置修改申请表';
alter table `tb_shop_config_modify_apply` add index `idx_shop_config_modify_apply_01` (`shop_id`);