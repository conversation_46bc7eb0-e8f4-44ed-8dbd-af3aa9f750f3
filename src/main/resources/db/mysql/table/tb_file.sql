drop table if exists `tb_file` ;
create table `tb_file` (
	`id` bigint not null auto_increment comment '主键',
	`name` varchar(100) comment '名称',
	`type` varchar(128) comment '类型',
	`size` int unsigned comment '大小',
	`url` varchar(64) comment '位置',
	`oss_url` varchar(300) comment 'oss位置(废弃)',
	`status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
	`modify_time`datetime not null default current_timestamp on update current_timestamp comment '修改时间',
	`create_time` datetime not null default current_timestamp comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '文件表';
alter table `tb_file` add unique index `idx_file_01` (`url`);