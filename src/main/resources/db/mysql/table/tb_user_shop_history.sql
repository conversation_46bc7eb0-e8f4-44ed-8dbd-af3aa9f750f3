drop table if exists `tb_user_shop_history` ;
create table `tb_user_shop_history` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户ID',
    `shop_id` bigint comment '店铺ID',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户浏览店铺历史记录表';
alter table `tb_user_shop_history` add index `idx_user_shop_history_01` (`user_id`);
alter table `tb_user_shop_history` add index `idx_user_shop_history_02` (`shop_id`);
alter table `tb_user_shop_history` add index `idx_user_shop_history_03` (`create_time`);