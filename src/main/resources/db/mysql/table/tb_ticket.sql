drop table if exists `tb_ticket` ;
create table `tb_ticket` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `name` varchar(20) comment '门票名字',
    `price_catalog` varchar(10) comment '价格属性(all:通用价格 man:男士价格 woman:女生价格)',
    `price` decimal(10,2) comment '价格',
    `product_number` int comment '酒水数量',
    `description` varchar(500) comment '门票描述',
    `stage` varchar(10) comment '上下架状态(up:上架 down:下架)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '门票表';
alter table `tb_ticket` add index `idx_ticket_01` (`shop_id`);