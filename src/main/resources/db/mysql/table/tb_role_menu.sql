drop table if exists `tb_role_menu` ;
create table `tb_role_menu` (
    `id` bigint not null auto_increment comment '主键',
    `role_id` bigint comment '角色ID',
    `menu_id` bigint comment '菜单ID',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '角色菜单关联表';
alter table `tb_role_menu` add unique index `idx_role_menu_01` (`role_id`,`menu_id`);