drop table if exists `tb_user_cart` ;
create table `tb_user_cart` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户ID',
    `shop_id` bigint comment '店铺ID',
    `product_id` bigint comment '产品ID',
    `product_sku_id` bigint comment '产品SKU ID',
    `product_option_ids` varchar(500) comment '产品选项IDS',
    `product_ingredient_ids` varchar(500) comment '产品小料IDS',
    `number` int comment '数量',
    `user_cart_status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户购物车表';
alter table `tb_user_cart` add index `idx_user_cart_01` (`user_id`);
alter table `tb_user_cart` add index `idx_user_cart_02` (`shop_id`);
alter table `tb_user_cart` add index `idx_user_cart_03` (`product_id`);