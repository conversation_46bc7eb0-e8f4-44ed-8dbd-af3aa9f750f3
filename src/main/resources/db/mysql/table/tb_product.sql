drop table if exists `tb_product` ;
create table `tb_product` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `catalog_id` bigint comment '分类ID',
    `name` varchar(20) comment '产品名字',
    `table_flag` varchar(1) comment '群酒(0:是 1:否)',
    `description` varchar(500) comment '产品介绍',
    `stage` varchar(10) comment '上下架状态(up:上架 down:下架)',
    `photo` varchar(500) comment '图片',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品表';
alter table `tb_product` add index `idx_product_01` (`shop_id`);