drop table if exists `tb_admin` ;
create table `tb_admin` (
	`id` bigint not null auto_increment comment '主键',
	`username` varchar(32) comment '用户名',
	`password` varchar(64) comment '密码',
	`mobile` varchar(11) comment '手机号',
	`name` varchar(20) comment '姓名',
    `gender` varchar(1) comment '性别(0: 男 1:女)',
    `first_login` varchar(1) comment '首次登录标志(0: 是 1:否)',
    `avatar` varchar(300) comment '头像',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
	`create_time` datetime not null default current_timestamp comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '管理员表';
alter table `tb_admin` add unique index `idx_admin_01` (`username`);
alter table `tb_admin` add unique index `idx_admin_02` (`mobile`);