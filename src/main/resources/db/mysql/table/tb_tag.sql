drop table if exists `tb_tag` ;
create table `tb_tag` (
    `id` bigint not null auto_increment comment '主键',
    `name` varchar(20) comment '标签名字',
    `catalog` varchar(20) comment '标签分类(shop:店铺 user:用户)',
    `status` varchar(1) comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '标签表';