drop table if exists `tb_user_ticket` ;
create table `tb_user_ticket` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户ID',
    `shop_id` bigint comment '店铺ID',
    `order_id` bigint comment '订单ID',
    `ticket_id` bigint comment '门票ID',
    `apply_time` date comment '报名时间',
    `catalog` varchar(1) comment '门票分类(0:自己购买 1:他人转赠)',
    `stage` varchar(1) comment '门票阶段(0:已核销 1:已退款)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户门票表';
alter table `tb_user_ticket` add index `idx_user_ticket_01` (`user_id`);
alter table `tb_user_ticket` add index `idx_user_ticket_02` (`shop_id`);
alter table `tb_user_ticket` add index `idx_user_ticket_03` (`ticket_id`);