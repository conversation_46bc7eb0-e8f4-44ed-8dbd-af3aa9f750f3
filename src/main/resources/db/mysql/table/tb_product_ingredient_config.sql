drop table if exists `tb_product_ingredient_config` ;
create table `tb_product_ingredient_config` (
    `id` bigint not null auto_increment comment '主键',
    `product_id` bigint comment '产品ID',
    `type` varchar(20) comment '类型(single:单选 multi:多选)',
    `required` varchar(1) comment '是否必选(0:必选 1:非必选)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品小料配置表';
alter table `tb_product_ingredient_config` add index `idx_product_ingredient_config_01` (`product_id`);