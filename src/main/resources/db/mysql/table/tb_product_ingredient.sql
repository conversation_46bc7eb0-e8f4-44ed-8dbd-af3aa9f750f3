drop table if exists `tb_product_ingredient` ;
create table `tb_product_ingredient` (
    `id` bigint not null auto_increment comment '主键',
    `product_id` bigint comment '产品ID',
    `name` varchar(20) comment '名字',
    `price` decimal(10,2) comment '价格',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品小料表';
alter table `tb_product_ingredient` add index `idx_product_ingredient_01` (`product_id`);