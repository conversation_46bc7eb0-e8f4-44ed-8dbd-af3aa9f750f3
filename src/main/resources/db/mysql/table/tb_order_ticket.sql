drop table if exists `tb_order_ticket` ;
create table `tb_order_ticket` (
    `id` bigint not null auto_increment comment '主键',
    `order_id` bigint comment '订单ID',
    `user_id` bigint comment '报名用户ID',
    `apply_time` date comment '报名时间',
    `photo` varchar(2000) comment '照片(多张用逗号分隔)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单门票表';
alter table `tb_order_ticket` add index `idx_order_ticket_01` (`order_id`);
alter table `tb_order_ticket` add index `idx_order_ticket_02` (`user_id`);