drop table if exists `tb_ticket_product` ;
create table `tb_ticket_product` (
    `id` bigint not null auto_increment comment '主键',
    `ticket_id` bigint comment '门票ID',
    `product_id` bigint comment '产品ID',
    `product_number` int comment '产品数量',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '门票产品表';
alter table `tb_ticket_product` add index `idx_ticket_product_02` (`ticket_id`);
alter table `tb_ticket_product` add index `idx_ticket_product_03` (`product_id`);