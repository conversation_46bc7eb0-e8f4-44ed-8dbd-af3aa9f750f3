drop table if exists `tb_district` ;
create table `tb_district` (
	`id` bigint not null auto_increment comment '主键',
	`city_id` bigint comment '城市编号',
	`name` varchar(20) comment '区(县)名称',
	`status` varchar(1) comment '状态(0:正常 1:无效)',
	`modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
	`create_time` datetime not null default current_timestamp comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '区(县)表';