drop table if exists `tb_product_option` ;
create table `tb_product_option` (
    `id` bigint not null auto_increment comment '主键',
    `product_id` bigint comment '产品ID',
    `key` varchar(20) comment '键',
    `value` varchar(20) comment '值',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品选项表';
alter table `tb_product_option` add index `idx_product_option_01` (`product_id`);