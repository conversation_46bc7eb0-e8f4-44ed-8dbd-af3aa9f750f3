drop table if exists `tb_user` ;
create table `tb_user` (
	`id` bigint not null auto_increment comment '主键',
	`mobile` varchar(11) comment '手机号',
    `nickname` varchar(20) comment '昵称',
	`avatar` varchar(300) comment '头像',
    `gender` varchar(1) comment '性别(0:男 1:女)',
	`openid` varchar(32) comment '微信小程序openid',
	`introduce` varchar(200) comment '个人简介',
	`photos` varchar(1000) comment '个人照片(多个用逗号分隔)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
	primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户表';
alter table `tb_user` add unique index `idx_user_01` (`mobile`);
alter table `tb_user` add unique index `idx_user_02` (`openid`);
