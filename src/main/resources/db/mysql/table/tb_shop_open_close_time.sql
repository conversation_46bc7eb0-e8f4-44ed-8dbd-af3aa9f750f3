DROP TABLE IF EXISTS `tb_shop_open_close_time`;
CREATE TABLE IF NOT EXISTS `tb_shop_open_close_time`
(
    `id`          BIGINT     NOT NULL AUTO_INCREMENT COMMENT '主键',
    `shop_id`     BIGINT     NOT NULL COMMENT '店铺 ID',
    `day`         VARCHAR(1) NOT NULL COMMENT '星期 (0: 统一营业时间, 1-7 星期一~日)',
    `open_time`   TIME       NOT NULL COMMENT '营业开始时间',
    `close_time`  TIME       NOT NULL COMMENT '营业结束时间',
    `status`      VARCHAR(1) NOT NULL COMMENT '数据状态 (0: 正常, 1: 删除)',
    `modify_time` DATETIME   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_time` DATETIME   NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    AUTO_INCREMENT = 10000000
    COMMENT = '店铺营业时间表';
