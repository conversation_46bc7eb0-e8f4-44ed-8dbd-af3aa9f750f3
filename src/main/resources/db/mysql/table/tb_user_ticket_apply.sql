drop table if exists `tb_user_ticket_apply` ;
create table `tb_user_ticket_apply` (
    `id` bigint not null auto_increment comment '主键',
    `user_id` bigint comment '用户ID',
    `shop_id` bigint comment '店铺ID',
    `order_id` bigint comment '订单ID',
    `apply_time` date comment '报名时间',
    `photo` varchar(2000) comment '照片(多张用逗号分隔)',
    `user_comment` varchar(500) comment '用户备注',
    `stage` varchar(1) comment '阶段(0:待审核 1:已通过 2:已拒绝 3:已取消)',
    `comment` varchar(500) comment '审核备注',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户门票报名表';
alter table `tb_user_ticket_apply` add index `idx_user_ticket_apply_01` (`user_id`);
alter table `tb_user_ticket_apply` add index `idx_user_ticket_apply_02` (`shop_id`);