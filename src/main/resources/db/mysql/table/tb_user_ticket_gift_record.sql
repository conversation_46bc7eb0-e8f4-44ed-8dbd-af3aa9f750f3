drop table if exists `tb_user_ticket_gift_record` ;
create table `tb_user_ticket_gift_record` (
    `id` bigint not null auto_increment comment '主键',
    `user_ticket_id` bigint comment '用户门票ID',
    `source_user_id` bigint comment '转赠人ID',
    `target_user_id` bigint comment '被转赠人ID',
    `stage` varchar(1) comment '认领状态(0:已认领 1:未认领)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '用户门票转赠记录表';
alter table `tb_user_ticket_gift_record` add index `idx_user_ticket_gift_record_01` (`user_ticket_id`);
alter table `tb_user_ticket_gift_record` add index `idx_user_ticket_gift_record_02` (`source_user_id`);
alter table `tb_user_ticket_gift_record` add index `idx_user_ticket_gift_record_03` (`target_user_id`);