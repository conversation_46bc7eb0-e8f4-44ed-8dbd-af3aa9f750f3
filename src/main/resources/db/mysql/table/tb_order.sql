drop table if exists `tb_order` ;
create table `tb_order` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `user_id` bigint comment '用户ID',
    `table_id` bigint comment '桌子ID',
    `code` varchar(64) comment '订单号',
    `type` varchar(20) comment '类型(ticket:门票 product:普通点单 diy:DIY)',
    `amount` decimal(10,2) comment '金额',
    `order_stage` varchar(1) comment '订单状态',
    `sub_order_stage` varchar(2) comment '二级订单状态',
    `pay_stage` varchar(1) comment '支付状态',
    `order_time` datetime comment '下单时间',
    `pay_time` datetime comment '支付时间',
    `user_comment` varchar(500) comment '用户备注',
    `user_status` varchar(1) default '0' comment '用户订单状态(0:正常 1:无效)',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单表';
alter table `tb_order` add index `idx_order_01` (`shop_id`);
alter table `tb_order` add index `idx_order_02` (`user_id`);
alter table `tb_order` add unique index `idx_order_03` (`code`);
alter table `tb_order` add index `idx_order_04` (`order_time`);
alter table `tb_order` add index `idx_order_05` (`pay_time`);