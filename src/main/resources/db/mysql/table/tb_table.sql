drop table if exists `tb_table` ;
create table `tb_table` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `table_name` varchar(20) comment '桌子名称',
    `person_number` int comment '人数',
    `order_mode` varchar(20) comment '点单模式',
    `qr_code` varchar(64) comment '小程序二维码图片地址',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '桌子表';