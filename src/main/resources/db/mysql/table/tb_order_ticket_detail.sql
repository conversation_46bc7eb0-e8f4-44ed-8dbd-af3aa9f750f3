drop table if exists `tb_order_ticket_detail` ;
create table `tb_order_ticket_detail` (
    `id` bigint not null auto_increment comment '主键',
    `order_id` bigint comment '订单ID',
    `ticket_id` bigint comment '门票ID',
    `ticket_name` varchar(20) comment '门票名字',
    `number` int comment '数量',
    `amount` decimal(10,2) comment '金额',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '订单门票详情表';
alter table `tb_order_ticket_detail` add index `idx_order_ticket_detail_01` (`order_id`);
alter table `tb_order_ticket_detail` add index `idx_order_ticket_detail_02` (`ticket_id`);