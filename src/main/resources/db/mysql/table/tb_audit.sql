DROP TABLE IF EXISTS `tb_audit`;
CREATE TABLE `tb_audit`
(
    `id`          BIGINT      NOT NULL AUTO_INCREMENT COMMENT '审核结果记录 ID，主键',
    `modify_id`   BIGINT      NULL COMMENT '修改条目 ID，为其他修改表主键',
    `trace_id`    VARCHAR(30) NULL COMMENT '审核任务id',
    `user_id`     BIGINT      NULL COMMENT '修改用户 ID，为用户表主键',
    `type`        VARCHAR(10) NULL COMMENT '审核类型：shop_text: 店铺文本配置，shop_media: 店铺媒体配置，user_text: 用户文本资料，user_media: 用户媒体资料',
    `suggest`     VARCHAR(6)  NULL COMMENT '审核建议，risky：风险 pass：通过 review：人工审核',
    `label`       VARCHAR(5)  NULL COMMENT '命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他',
    `status`      VARCHAR(1)  NULL COMMENT '记录状态 (0: 正常 1: 无效)',
    `modify_time` DATETIME    NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    `create_time` DATETIME    NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE INDEX `id_UNIQUE` (`id` ASC)
)
    ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COLLATE = utf8mb4_unicode_ci
    AUTO_INCREMENT = 10000000
    COMMENT '审核结果记录表';
