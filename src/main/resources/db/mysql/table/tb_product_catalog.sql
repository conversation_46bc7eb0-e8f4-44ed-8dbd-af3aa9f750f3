drop table if exists `tb_product_catalog` ;
create table `tb_product_catalog` (
    `id` bigint not null auto_increment comment '主键',
    `shop_id` bigint comment '店铺ID',
    `name` varchar(20) comment '分类名字',
    `sort` int comment '排序',
    `status` varchar(1) default '0' comment '状态(0:正常 1:无效)',
    `modify_time` datetime not null default current_timestamp on update current_timestamp comment '修改时间',
    `create_time` datetime not null default current_timestamp comment '创建时间',
    primary key(`id`)
) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
auto_increment=10000000
comment '产品分类表';
alter table `tb_product_catalog` add index `idx_product_catalog_01` (`shop_id`);