$assemblyPath = if ($env:WINSCP_PATH) { $env:WINSCP_PATH } else { $PSScriptRoot }
Add-Type -Path (Join-Path $assemblyPath "WinSCPnet.dll")
$sessionOptions = New-Object WinSCP.SessionOptions -Property @{
    Protocol              = [WinSCP.Protocol]::Scp
    HostName              = "***************"
    UserName              = "root"
    Password              = "Linka@6688"
    SshHostKeyFingerprint = "ssh-ed25519 255 j4GuvZs0ElDH4xEfaSxSnuSIyTXDNa5tR7QzO1qKi1w"
}
$session = New-Object WinSCP.Session
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
Set-Location $scriptDir
$ProjectRoot = "..\..\..\..\"
Set-Location $ProjectRoot
git pull
mvn package
$modelDir = ".\lingka-api\target\"
Set-Location $modelDir
$localJar = "lingka-api.jar"
$zipFile = "lingka-api.zip"
$remoteDir = "/home/<USER>/dev/lingka-api/"
$remoteCmd = "cd ${remoteDir} && unzip -o ${zipFile} && ./start.sh abupdate && rm -f ${zipFile}"
Compress-Archive -Path $localJar -DestinationPath $zipFile -Force
function FileTransferProgress {
    param($e)
 
    Write-Progress `
        -Activity "Uploading" -Status ("{0:P0} complete:" -f $e.OverallProgress) `
        -PercentComplete ($e.OverallProgress * 100)
    Write-Progress `
        -Id 1 -Activity $e.FileName -Status ("{0:P0} complete:" -f $e.FileProgress) `
        -PercentComplete ($e.FileProgress * 100)
}
$session = New-Object WinSCP.Session
try {
    $session.add_FileTransferProgress( { FileTransferProgress($_) } )
    $session.Open($sessionOptions)
    $transferOptions = New-Object WinSCP.TransferOptions
    $transferOptions.TransferMode = [WinSCP.TransferMode]::Binary
    $transferResult = $session.PutFileToDirectory("$PWD\$zipFile", $remoteDir, $True, $transferOptions)
    if ($null -ne $transferResult.Error) {
        throw $transferResult.Error
    }
    Write-Host "Upload of $($transferResult.FileName) succeeded"
    $ExecutionResult = $session.ExecuteCommand($remoteCmd)
    $ExecutionResult.Check()
    Write-Host $ExecutionResult.Output
}
finally {
    $session.Dispose()
}
Set-Location $scriptDir
