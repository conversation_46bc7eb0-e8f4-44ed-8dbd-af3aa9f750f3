<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ShopOpenCloseTimeDao">

    <resultMap type="com.dto.ShopOpenCloseTimeDTO" id="shopOpenCloseTimeResult">
        <result property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="day" column="day"/>
        <result property="openTime" column="open_time"/>
        <result property="closeTime" column="close_time"/>
        <result property="status" column="status"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="select" parameterType="com.query.ShopOpenCloseTimeQuery" resultMap="shopOpenCloseTimeResult">
        select * from tb_shop_open_close_time
        <where>
            <if test="shopId != null ">and shop_id = #{shopId}</if>
            <if test="day != null  and day != ''">and day = #{day}</if>
            <if test="openTime != null ">and open_time = #{openTime}</if>
            <if test="closeTime != null ">and close_time = #{closeTime}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="modifyTime != null ">and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="shopOpenCloseTimeResult">
        select *
        from tb_shop_open_close_time
        where id = #{id}
    </select>


    <select id="selectByIds" parameterType="java.util.List" resultMap="shopOpenCloseTimeResult">
        select * from tb_shop_open_close_time where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.ShopOpenCloseTime" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shop_open_close_time
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="day != null and day != ''">day,</if>
            <if test="openTime != null">open_time,</if>
            <if test="closeTime != null">close_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="day != null and day != ''">#{day},</if>
            <if test="openTime != null">#{openTime},</if>
            <if test="closeTime != null">#{closeTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ShopOpenCloseTime">
        update tb_shop_open_close_time
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="day != null and day != ''">day = #{day},</if>
            <if test="openTime != null">open_time = #{openTime},</if>
            <if test="closeTime != null">close_time = #{closeTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from tb_shop_open_close_time
        where id = #{id}
    </delete>

    <delete id="deleteByShopId" parameterType="java.lang.Long">
        delete
        from tb_shop_open_close_time
        where shop_id = #{shopId}
    </delete>
</mapper>
