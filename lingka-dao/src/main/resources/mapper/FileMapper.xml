<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dao.FileDao">
	<resultMap id="fileResultMap" type="com.domain.File">
		<id column="id" property="id" />
		<result column="name" property="name" />
		<result column="url" property="url" />
		<result column="oss_url" property="ossUrl" />
		<result column="status" property="status" />
		<result column="modify_time" property="modifyTime" />
		<result column="create_time" property="createTime" />
	</resultMap>
	<select id="selectById" resultMap="fileResultMap" >
		select * from tb_file where id = #{id}
	</select>
	<select id="selectMinById" resultMap="fileResultMap" parameterType="com.domain.File">
		select * from tb_file where id > #{id} order by id asc limit 0,1
	</select>
	<select id="selectByUrl" resultMap="fileResultMap" parameterType="com.domain.File">
		select * from tb_file where url = #{url}
	</select>
	<select id="count" resultType="java.lang.Integer" parameterType="com.domain.File">
		select count(*) from tb_file
		<trim prefix="where" prefixOverrides="and">
			<if test="name != null">
				<bind name="pattern" value="'%' + name + '%'" />
				and name like #{pattern}
			</if>
			<if test="status != null">
				 and status = #{status}
			</if>
		</trim>
	</select>
	<select id="select" resultMap="fileResultMap">
		select * from tb_file
		<trim prefix="where" prefixOverrides="and">
			<if test="file != null and file.name != null">
				<bind name="pattern" value="'%' + file.name + '%'" />
				and name like #{pattern}
			</if>
			<if test="file != null and file.status != null">
				 and status = #{file.status}
			</if>
		</trim>
		order by id desc limit #{start},#{limit}
	</select>
	<update id="updateById" parameterType="com.domain.File">
		update tb_file
		<trim prefix="set" suffixOverrides=",">
			<if test="name != null">
				name = #{name},
			</if>
			<if test="type != null">
				type = #{type},
			</if>
			<if test="size != null">
				size = #{size},
			</if>
			<if test="url != null">
				url = #{url},
			</if>
			<if test="ossUrl != null">
				oss_url = #{ossUrl},
			</if>
			<if test="status != null">
				status = #{status},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime},
			</if>
			<if test="createTime != null">
				create_time = #{createTime},
			</if>
		</trim>
		where id = #{id}
	</update>
	<insert id="insert" parameterType="com.domain.File" useGeneratedKeys="true" keyProperty="id">
		insert into tb_file
		<trim suffixOverrides="," prefix="(" suffix=") values">
			<if test="id != null">
				id,
			</if>
			<if test="name != null">
				name,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="size != null">
				size,
			</if>
			<if test="url != null">
				url,
			</if>
			<if test="ossUrl != null">
				oss_url,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="modifyTime != null">
				modify_time,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim suffixOverrides="," prefix="(" suffix=")">
			<if test="id != null">
				#{id},
			</if>
			<if test="name != null">
				#{name},
			</if>
			<if test="type != null">
				#{type},
			</if>
			<if test="size != null">
				#{size},
			</if>
			<if test="url != null">
				#{url},
			</if>
			<if test="ossUrl != null">
				#{ossUrl},
			</if>
			<if test="status != null">
				#{status},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
		</trim>
	</insert>
</mapper>