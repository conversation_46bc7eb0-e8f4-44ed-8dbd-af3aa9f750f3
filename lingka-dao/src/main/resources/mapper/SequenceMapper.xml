<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.SequenceDao">

    <resultMap type="com.dto.SequenceDTO" id="sequenceResult">
        <result property="id"    column="id"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.SequenceQuery" resultMap="sequenceResult">
        select id, status, modify_time, create_time from tb_sequence
        <where>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>


    <select id="selectById" parameterType="Long" resultMap="sequenceResult">
         select id, status, modify_time, create_time from tb_sequence
        where id = #{id}
    </select>

    <insert id="insert" parameterType="com.domain.Sequence" useGeneratedKeys="true" keyProperty="id">
        insert into tb_sequence
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Sequence">
        update tb_sequence
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from tb_sequence where id = #{id}
    </delete>


</mapper>
