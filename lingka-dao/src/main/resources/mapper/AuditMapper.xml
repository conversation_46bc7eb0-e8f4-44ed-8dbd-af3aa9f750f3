<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.AuditDao">

    <resultMap type="com.dto.AuditDTO" id="auditResult">
        <result property="id" column="id"/>
        <result property="modifyId" column="modify_id"/>
        <result property="traceId" column="trace_id"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="suggest" column="suggest"/>
        <result property="label" column="label"/>
        <result property="status" column="status"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="select" parameterType="com.query.AuditQuery" resultMap="auditResult">
        select * from tb_audit
        <where>
            <if test="modifyId != null ">and modify_id = #{modifyId}</if>
            <if test="traceId != null  and traceId != ''">and trace_id = #{traceId}</if>
            <if test="userId != null and userId != ''">and user_id = #{userId}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="suggest != null  and suggest != ''">and suggest = #{suggest}</if>
            <if test="label != null  and label != ''">and label = #{label}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="modifyTime != null ">and modify_time = #{modifyTime}</if>
        </where>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="auditResult">
        select *
        from tb_audit
        where id = #{id}
    </select>


    <select id="selectByIds" parameterType="java.util.List" resultMap="auditResult">
        select * from tb_audit where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.Audit" useGeneratedKeys="true" keyProperty="id">
        insert into tb_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="modifyId != null">modify_id,</if>
            <if test="traceId != null">trace_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="type != null">type,</if>
            <if test="suggest != null">suggest,</if>
            <if test="label != null">label,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="modifyId != null">#{modifyId},</if>
            <if test="traceId != null">#{traceId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="type != null">#{type},</if>
            <if test="suggest != null">#{suggest},</if>
            <if test="label != null">#{label},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Audit">
        update tb_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="modifyId != null">modify_id = #{modifyId},</if>
            <if test="traceId != null">trace_id = #{traceId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="type != null">type = #{type},</if>
            <if test="suggest != null">suggest = #{suggest},</if>
            <if test="label != null">label = #{label},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from tb_audit
        where id = #{id}
    </delete>


</mapper>