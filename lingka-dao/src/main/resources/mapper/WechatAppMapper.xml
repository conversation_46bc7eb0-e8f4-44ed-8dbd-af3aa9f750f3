<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.WechatAppDao">
    
    <resultMap type="com.dto.WechatAppDTO" id="wechatAppResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="secret"    column="secret"    />
        <result property="name"    column="name"    />
        <result property="accessToken"    column="access_token"    />
        <result property="accessTokenDatetime"    column="access_token_datetime"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.dto.WechatAppDTO" resultMap="wechatAppResult">
        select id, appid, secret, name, access_token, access_token_datetime, status, modify_time, create_time from tb_wechat_app
        <where>  
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="secret != null  and secret != ''"> and secret = #{secret}</if>
            <if test="name != null  and name != ''"> and name = #{name}</if>
            <if test="accessToken != null  and accessToken != ''"> and access_token = #{accessToken}</if>
            <if test="accessTokenDatetime != null "> and access_token_datetime = #{accessTokenDatetime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="Long" resultMap="wechatAppResult">
         select id, appid, secret, name, access_token, access_token_datetime, status, modify_time, create_time from tb_wechat_app
        where id = #{id}
    </select>
        
    <insert id="insert" parameterType="com.domain.WechatApp" useGeneratedKeys="true" keyProperty="id">
        insert into tb_wechat_app
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appid != null">appid,</if>
            <if test="secret != null">secret,</if>
            <if test="name != null">name,</if>
            <if test="accessToken != null">access_token,</if>
            <if test="accessTokenDatetime != null">access_token_datetime,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appid != null">#{appid},</if>
            <if test="secret != null">#{secret},</if>
            <if test="name != null">#{name},</if>
            <if test="accessToken != null">#{accessToken},</if>
            <if test="accessTokenDatetime != null">#{accessTokenDatetime},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.WechatApp">
        update tb_wechat_app
        <trim prefix="SET" suffixOverrides=",">
            <if test="appid != null">appid = #{appid},</if>
            <if test="secret != null">secret = #{secret},</if>
            <if test="name != null">name = #{name},</if>
            <if test="accessToken != null">access_token = #{accessToken},</if>
            <if test="accessTokenDatetime != null">access_token_datetime = #{accessTokenDatetime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from tb_wechat_app where id = #{id}
    </delete>


</mapper>