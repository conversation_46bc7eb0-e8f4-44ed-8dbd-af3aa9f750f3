<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ProductCatalogDao">
    
    <resultMap type="com.dto.ProductCatalogDTO" id="productCatalogResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="name"    column="name"    />
        <result property="sort"    column="sort"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.ProductCatalogQuery" resultMap="productCatalogResult">
        select * from tb_product_catalog
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>

    <select id="selectBySort" parameterType="com.query.ProductCatalogQuery" resultMap="productCatalogResult">
        select * from tb_product_catalog
        <where>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="sort != null "> and sort = #{sort}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by sort asc, id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="productCatalogResult">
         select * from tb_product_catalog where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="productCatalogResult">
        select * from tb_product_catalog where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.ProductCatalog" useGeneratedKeys="true" keyProperty="id">
        insert into tb_product_catalog
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="name != null">name,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="name != null">#{name},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ProductCatalog">
        update tb_product_catalog
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_product_catalog where id = #{id}
    </delete>


</mapper>