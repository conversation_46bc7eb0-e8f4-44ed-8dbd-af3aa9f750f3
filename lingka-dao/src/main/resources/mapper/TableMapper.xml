<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.TableDao">
    
    <resultMap type="com.dto.TableDTO" id="tableResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="tableName"    column="table_name"    />
        <result property="personNumber"    column="person_number"    />
        <result property="orderMode"    column="order_mode"    />
        <result property="qrCode"    column="qr_code"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.TableQuery" resultMap="tableResult">
        select * from tb_table
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="tableName != null  and tableName != ''"> and table_name like concat('%', #{tableName}, '%')</if>
            <if test="personNumber != null "> and person_number = #{personNumber}</if>
            <if test="orderMode != null  and orderMode != ''"> and order_mode = #{orderMode}</if>
            <if test="qrCode != null  and qrCode != ''"> and qr_code = #{qrCode}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="tableResult">
         select * from tb_table where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="tableResult">
        select * from tb_table where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.Table" useGeneratedKeys="true" keyProperty="id">
        insert into tb_table
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="tableName != null">table_name,</if>
            <if test="personNumber != null">person_number,</if>
            <if test="orderMode != null">order_mode,</if>
            <if test="qrCode != null">qr_code,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="tableName != null">#{tableName},</if>
            <if test="personNumber != null">#{personNumber},</if>
            <if test="orderMode != null">#{orderMode},</if>
            <if test="qrCode != null">#{qrCode},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Table">
        update tb_table
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="tableName != null">table_name = #{tableName},</if>
            <if test="personNumber != null">person_number = #{personNumber},</if>
            <if test="orderMode != null">order_mode = #{orderMode},</if>
            <if test="qrCode != null">qr_code = #{qrCode},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_table where id = #{id}
    </delete>


</mapper>