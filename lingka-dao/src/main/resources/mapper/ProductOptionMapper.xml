<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ProductOptionDao">
    
    <resultMap type="com.dto.ProductOptionDTO" id="productOptionResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="key"    column="key"    />
        <result property="value"    column="value"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.ProductOptionQuery" resultMap="productOptionResult">
        select * from tb_product_option
        <where>  
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="key != null  and key != ''"> and key = #{key}</if>
            <if test="value != null  and value != ''"> and value = #{value}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="productOptionResult">
         select * from tb_product_option where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="productOptionResult">
        select * from tb_product_option where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.ProductOption" useGeneratedKeys="true" keyProperty="id">
        insert into tb_product_option
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="key != null">`key`,</if>
            <if test="value != null">`value`,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="key != null">#{key},</if>
            <if test="value != null">#{value},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ProductOption">
        update tb_product_option
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="key != null">`key` = #{key},</if>
            <if test="value != null">`value` = #{value},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_product_option where id = #{id}
    </delete>


</mapper>