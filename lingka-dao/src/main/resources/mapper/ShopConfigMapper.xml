<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ShopConfigDao">
    
    <resultMap type="com.dto.ShopConfigDTO" id="shopConfigResult">
        <result property="id"    column="id"    />
        <result property="shopId" column="shop_id" />
        <result property="name"    column="name"    />
        <result property="logo"    column="logo"    />
        <result property="phone"    column="phone"    />
        <result property="photo"    column="photo"    />
        <result property="description"    column="description"    />
        <result property="tags"    column="tags"    />
        <result property="displaySwitch" column="display_switch" />
        <result property="state"    column="state"    />
        <result property="provinceId"    column="province_id"    />
        <result property="cityId"    column="city_id"    />
        <result property="distinctId"    column="distinct_id"    />
        <result property="address"    column="address"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.ShopConfigQuery" resultMap="shopConfigResult">
        select * from tb_shop_config
        <where>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="shopIds != null and shopIds.size > 0">
                and shop_id in
                <foreach collection="shopIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="logo != null  and logo != ''"> and logo = #{logo}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="tags != null  and tags != ''"> and tags = #{tags}</if>
            <if test="displaySwitch != null "> and display_switch = #{displaySwitch}</if>
            <if test="state != null  and state != ''"> and state = #{state}</if>
            <if test="provinceId != null "> and province_id = #{provinceId}</if>
            <if test="cityId != null "> and city_id = #{cityId}</if>
            <if test="distinctId != null "> and distinct_id = #{distinctId}</if>
            <if test="address != null  and address != ''"> and address = #{address}</if>
            <if test="latitude != null "> and latitude = #{latitude}</if>
            <if test="longitude != null "> and longitude = #{longitude}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="shopConfigResult">
        select *
        from tb_shop_config
        where id = #{id}
    </select>

    <select id="selectByShopId" parameterType="java.lang.Long" resultMap="shopConfigResult">
        select *
        from tb_shop_config
        where shop_id = #{shopId} and status = 0
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="shopConfigResult">
        select * from tb_shop_config where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByShopIds" parameterType="java.util.List" resultMap="shopConfigResult">
        select * from tb_shop_config where shop_id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        and status = 0
    </select>

    <insert id="insert" parameterType="com.domain.ShopConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shop_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="name != null">name,</if>
            <if test="logo != null">logo,</if>
            <if test="phone != null">phone,</if>
            <if test="photo != null">photo,</if>
            <if test="description != null">description,</if>
            <if test="tags != null">tags,</if>
            <if test="displaySwitch != null">display_switch,</if>
            <if test="state != null">state,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="cityId != null">city_id,</if>
            <if test="distinctId != null">distinct_id,</if>
            <if test="address != null">address,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="name != null">#{name},</if>
            <if test="logo != null">#{logo},</if>
            <if test="phone != null">#{phone},</if>
            <if test="photo != null">#{photo},</if>
            <if test="description != null">#{description},</if>
            <if test="tags != null">#{tags},</if>
            <if test="displaySwitch != null">#{displaySwitch},</if>
            <if test="state != null">#{state},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="distinctId != null">#{distinctId},</if>
            <if test="address != null">#{address},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ShopConfig">
        update tb_shop_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="logo != null">logo = #{logo},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="description != null">description = #{description},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="displaySwitch != null">display_switch = #{displaySwitch},</if>
            <if test="state != null">state = #{state},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="distinctId != null">distinct_id = #{distinctId},</if>
            <if test="address != null">address = #{address},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updatePartialById" parameterType="map">
        UPDATE tb_shop_config
        <set>
            <foreach collection="changes" index="key" item="value" separator=",">
                ${key} = #{value}
            </foreach>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_shop_config where id = #{id}
    </delete>


</mapper>