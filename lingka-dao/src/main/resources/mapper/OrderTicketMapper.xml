<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.OrderTicketDao">
    
    <resultMap type="com.dto.OrderTicketDTO" id="orderTicketResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="userId"    column="user_id"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="photo"    column="photo"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.OrderTicketQuery" resultMap="orderTicketResult">
        select * from tb_order_ticket
        <where>  
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="orderIds != null and orderIds.size > 0">
                and order_id in
                <foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="photo != null  and photo != ''"> and photo = #{photo}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="orderTicketResult">
         select * from tb_order_ticket where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="orderTicketResult">
        select * from tb_order_ticket where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.OrderTicket" useGeneratedKeys="true" keyProperty="id">
        insert into tb_order_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="photo != null">photo,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="photo != null">#{photo},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.OrderTicket">
        update tb_order_ticket
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_order_ticket where id = #{id}
    </delete>


</mapper>