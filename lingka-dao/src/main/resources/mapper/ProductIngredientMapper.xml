<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ProductIngredientDao">
    
    <resultMap type="com.dto.ProductIngredientDTO" id="productIngredientResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="name"    column="name"    />
        <result property="price"    column="price"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.ProductIngredientQuery" resultMap="productIngredientResult">
        select * from tb_product_ingredient
        <where>  
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productIds != null and productIds.size > 0">
                and product_id in
                <foreach collection="productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="productIngredientResult">
         select * from tb_product_ingredient where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="productIngredientResult">
        select * from tb_product_ingredient where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.ProductIngredient" useGeneratedKeys="true" keyProperty="id">
        insert into tb_product_ingredient
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="name != null">name,</if>
            <if test="price != null">price,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="name != null">#{name},</if>
            <if test="price != null">#{price},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ProductIngredient">
        update tb_product_ingredient
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_product_ingredient where id = #{id}
    </delete>


</mapper>