<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ProductDao">

    <resultMap type="com.dto.ProductDTO" id="productResult">
        <result property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="catalogId" column="catalog_id"/>
        <result property="name" column="name"/>
        <result property="tableFlag" column="table_flag"/>
        <result property="description" column="description"/>
        <result property="stage" column="stage"/>
        <result property="photo" column="photo"/>
        <result property="status" column="status"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="select" parameterType="com.query.ProductQuery" resultMap="productResult">
        select * from tb_product
        <where>
            <if test="shopId != null ">and shop_id = #{shopId}</if>
            <if test="catalogId != null ">and catalog_id = #{catalogId}</if>
            <if test="name != null and name != ''">and name = #{name}</if>
            <if test="nameLike != null and nameLike != ''">and name like concat('%', #{nameLike}, '%')</if>
            <if test="tableFlag != null and tableFlag != ''">and table_flag = #{tableFlag}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="stage != null and stage != ''">and stage = #{stage}</if>
            <if test="photo != null  and photo != ''">and photo = #{photo}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="modifyTime != null">and modify_time = #{modifyTime}</if>
        </where>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="productResult">
        select *
        from tb_product
        where id = #{id}
    </select>


    <select id="selectByIds" parameterType="java.util.List" resultMap="productResult">
        select * from tb_product where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.Product" useGeneratedKeys="true" keyProperty="id">
        insert into tb_product
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="catalogId != null">catalog_id,</if>
            <if test="name != null">name,</if>
            <if test="tableFlag != null">table_flag,</if>
            <if test="description != null">description,</if>
            <if test="stage != null">stage,</if>
            <if test="photo != null">photo,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="catalogId != null">#{catalogId},</if>
            <if test="name != null">#{name},</if>
            <if test="tableFlag != null">#{tableFlag},</if>
            <if test="description != null">#{description},</if>
            <if test="stage != null">#{stage},</if>
            <if test="photo != null">#{photo},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Product">
        update tb_product
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="catalogId != null">catalog_id = #{catalogId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="tableFlag != null">table_flag = #{tableFlag},</if>
            <if test="description != null">description = #{description},</if>
            <if test="stage != null">stage = #{stage},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from tb_product
        where id = #{id}
    </delete>


</mapper>