<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.UserTicketGiftRecordDao">
    
    <resultMap type="com.dto.UserTicketGiftRecordDTO" id="userTicketGiftRecordResult">
        <result property="id"    column="id"    />
        <result property="userTicketId"    column="user_ticket_id"    />
        <result property="sourceUserId"    column="source_user_id"    />
        <result property="targetUserId"    column="target_user_id"    />
        <result property="stage"    column="stage"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.UserTicketGiftRecordQuery" resultMap="userTicketGiftRecordResult">
        select * from tb_user_ticket_gift_record
        <where>  
            <if test="userTicketId != null "> and user_ticket_id = #{userTicketId}</if>
            <if test="sourceUserId != null "> and source_user_id = #{sourceUserId}</if>
            <if test="targetUserId != null "> and target_user_id = #{targetUserId}</if>
            <if test="stage != null "> and stage = #{stage}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="userTicketGiftRecordResult">
         select * from tb_user_ticket_gift_record where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="userTicketGiftRecordResult">
        select * from tb_user_ticket_gift_record where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.UserTicketGiftRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user_ticket_gift_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userTicketId != null">user_ticket_id,</if>
            <if test="sourceUserId != null">source_user_id,</if>
            <if test="targetUserId != null">target_user_id,</if>
            <if test="stage != null">stage,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userTicketId != null">#{userTicketId},</if>
            <if test="sourceUserId != null">#{sourceUserId},</if>
            <if test="targetUserId != null">#{targetUserId},</if>
            <if test="stage != null">#{stage},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.UserTicketGiftRecord">
        update tb_user_ticket_gift_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userTicketId != null">user_ticket_id = #{userTicketId},</if>
            <if test="sourceUserId != null">source_user_id = #{sourceUserId},</if>
            <if test="targetUserId != null">target_user_id = #{targetUserId},</if>
            <if test="stage != null">stage = #{stage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_user_ticket_gift_record where id = #{id}
    </delete>


</mapper>