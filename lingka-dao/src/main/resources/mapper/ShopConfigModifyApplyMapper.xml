<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ShopConfigModifyApplyDao">

    <resultMap type="com.dto.ShopConfigModifyApplyDTO" id="shopConfigModifyApplyResult">
        <result property="id" column="id"/>
        <result property="shopId" column="shop_id"/>
        <result property="name" column="name"/>
        <result property="photo" column="photo"/>
        <result property="description" column="description"/>
        <result property="audit" column="audit"/>
        <result property="auditAdminId" column="audit_admin_id"/>
        <result property="status" column="status"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="select" parameterType="com.query.ShopConfigModifyApplyQuery" resultMap="shopConfigModifyApplyResult">
        select * from tb_shop_config_modify_apply
        <where>
            <if test="shopId != null ">and shop_id = #{shopId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="photo != null  and photo != ''">and photo = #{photo}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="audit != null  and audit != ''">and audit = #{audit}</if>
            <if test="auditAdminId != null ">and audit_admin_id = #{auditAdminId}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="modifyTime != null ">and modify_time = #{modifyTime}</if>
        </where>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="shopConfigModifyApplyResult">
        select *
        from tb_shop_config_modify_apply
        where id = #{id}
    </select>


    <select id="selectByIds" parameterType="java.util.List" resultMap="shopConfigModifyApplyResult">
        select * from tb_shop_config_modify_apply where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectByShopId" parameterType="java.lang.Long" resultMap="shopConfigModifyApplyResult">
        select *
        from tb_shop_config_modify_apply
        where shop_id = #{shopId}
    </select>


    <insert id="insert" parameterType="com.domain.ShopConfigModifyApply" useGeneratedKeys="true" keyProperty="id">
        insert into tb_shop_config_modify_apply
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="name != null">name,</if>
            <if test="photo != null">photo,</if>
            <if test="description != null">description,</if>
            <if test="audit != null">audit,</if>
            <if test="auditAdminId != null">audit_admin_id,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="name != null">#{name},</if>
            <if test="photo != null">#{photo},</if>
            <if test="description != null">#{description},</if>
            <if test="audit != null">#{audit},</if>
            <if test="auditAdminId != null">#{auditAdminId},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.ShopConfigModifyApply">
        update tb_shop_config_modify_apply
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="photo != null">photo = #{photo},</if>
            <if test="description != null">description = #{description},</if>
            <if test="audit != null">audit = #{audit},</if>
            <if test="auditAdminId != null">audit_admin_id = #{auditAdminId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from tb_shop_config_modify_apply
        where id = #{id}
    </delete>

    <delete id="deleteByIds" parameterType="java.util.List">
        delete
        from tb_shop_config_modify_apply
        where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>
