<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.UserTicketDao">
    
    <resultMap type="com.dto.UserTicketDTO" id="userTicketResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="ticketId"    column="ticket_id"    />
        <result property="applyTime"    column="apply_time"    />
        <result property="stage"    column="stage"    />
        <result property="status"    column="status"    />
        <result property="catalog"    column="catalog"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.UserTicketQuery" resultMap="userTicketResult">
        select * from tb_user_ticket
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="orderId != null "> and order_id = #{orderId}</if>
            <if test="ticketId != null "> and ticket_id = #{ticketId}</if>
            <if test="applyTime != null "> and apply_time = #{applyTime}</if>
            <if test="stage != null  and stage != ''"> and stage = #{stage}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="catalog != null  and catalog != ''"> and catalog = #{catalog}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="userTicketResult">
         select * from tb_user_ticket where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="userTicketResult">
        select * from tb_user_ticket where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.UserTicket" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user_ticket
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="ticketId != null">ticket_id,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="stage != null">stage,</if>
            <if test="status != null">status,</if>
            <if test="catalog != null">catalog,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="ticketId != null">#{ticketId},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="stage != null">#{stage},</if>
            <if test="status != null">#{status},</if>
            <if test="catalog != null">#{catalog},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.UserTicket">
        update tb_user_ticket
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="ticketId != null">ticket_id = #{ticketId},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="stage != null">stage = #{stage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="catalog != null">catalog = #{catalog},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_user_ticket where id = #{id}
    </delete>


</mapper>