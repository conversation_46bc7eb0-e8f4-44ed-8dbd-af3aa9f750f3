<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.DistrictDao">
    
    <resultMap type="com.dto.DistrictDTO" id="districtResult">
        <result property="id"    column="id"    />
        <result property="cityId"    column="city_id"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.DistrictQuery" resultMap="districtResult">
        select * from tb_district
        <where>  
            <if test="cityId != null "> and city_id = #{cityId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="districtResult">
         select * from tb_district where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="districtResult">
        select * from tb_district where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.District" useGeneratedKeys="true" keyProperty="id">
        insert into tb_district
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="cityId != null">city_id,</if>
            <if test="name != null">name,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="name != null">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.District">
        update tb_district
        <trim prefix="SET" suffixOverrides=",">
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_district where id = #{id}
    </delete>


</mapper>