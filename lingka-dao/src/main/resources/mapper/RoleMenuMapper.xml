<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.RoleMenuDao">
    
    <resultMap type="com.dto.RoleMenuDTO" id="roleMenuResult">
        <result property="id"    column="id"    />
        <result property="roleId"    column="role_id"    />
        <result property="menuId"    column="menu_id"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.RoleMenuQuery" resultMap="roleMenuResult">
        select * from tb_role_menu
        <where>  
            <if test="roleId != null "> and role_id = #{roleId}</if>
            <if test="roleIds != null and roleIds.size > 0">
                and role_id in
                <foreach collection="roleIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="menuId != null "> and menu_id = #{menuId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="roleMenuResult">
         select * from tb_role_menu where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="roleMenuResult">
        select * from tb_role_menu where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.RoleMenu" useGeneratedKeys="true" keyProperty="id">
        insert into tb_role_menu
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">role_id,</if>
            <if test="menuId != null">menu_id,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">#{roleId},</if>
            <if test="menuId != null">#{menuId},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.RoleMenu">
        update tb_role_menu
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleId != null">role_id = #{roleId},</if>
            <if test="menuId != null">menu_id = #{menuId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_role_menu where id = #{id}
    </delete>


</mapper>