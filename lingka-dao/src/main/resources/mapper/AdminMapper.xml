<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.AdminDao">
    
    <resultMap type="com.dto.AdminDTO" id="adminResult">
        <result property="id"    column="id"    />
        <result property="username"    column="username"    />
        <result property="password"    column="password"    />
        <result property="mobile"    column="mobile"    />
        <result property="name"    column="name"    />
        <result property="gender"    column="gender"    />
        <result property="firstLogin"    column="first_login"    />
        <result property="avatar"    column="avatar"    />
        <result property="status"    column="status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.AdminQuery" resultMap="adminResult">
        select * from tb_admin
        <where>  
            <if test="username != null  and username != ''"> and username like concat('%', #{username}, '%')</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="firstLogin != null  and firstLogin != ''"> and first_login = #{firstLogin}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="adminResult">
         select * from tb_admin where id = #{id}
    </select>

    <select id="selectByUsername" parameterType="java.lang.String" resultMap="adminResult">
        select * from tb_admin where username = #{username}
    </select>

    <select id="selectByMobile" parameterType="java.lang.String" resultMap="adminResult">
        select * from tb_admin where mobile = #{mobile}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="adminResult">
        select * from tb_admin where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <insert id="insert" parameterType="com.domain.Admin" useGeneratedKeys="true" keyProperty="id">
        insert into tb_admin
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="username != null">username,</if>
            <if test="password != null">password,</if>
            <if test="mobile != null">mobile,</if>
            <if test="name != null">name,</if>
            <if test="gender != null">gender,</if>
            <if test="firstLogin != null">first_login,</if>
            <if test="avatar != null">avatar,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="username != null">#{username},</if>
            <if test="password != null">#{password},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="name != null">#{name},</if>
            <if test="gender != null">#{gender},</if>
            <if test="firstLogin != null">#{firstLogin},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.Admin">
        update tb_admin
        <trim prefix="SET" suffixOverrides=",">
            <if test="username != null">username = #{username},</if>
            <if test="password != null">password = #{password},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="name != null">name = #{name},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="firstLogin != null">first_login = #{firstLogin},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_admin where id = #{id}
    </delete>


</mapper>