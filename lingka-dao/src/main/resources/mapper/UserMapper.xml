<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.UserDao">

    <resultMap type="com.dto.UserDTO" id="userResult">
        <result property="id" column="id"/>
        <result property="mobile" column="mobile"/>
        <result property="nickname" column="nickname"/>
        <result property="avatar" column="avatar"/>
        <result property="gender" column="gender"/>
        <result property="openid" column="openid"/>
        <result property="introduce" column="introduce"/>
        <result property="photos" column="photos"/>
        <result property="status" column="status"/>
        <result property="modifyTime" column="modify_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="select" parameterType="com.query.UserQuery" resultMap="userResult">
        select * from tb_user
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="nickname != null  and nickname != ''">and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatar != null  and avatar != ''">and avatar = #{avatar}</if>
            <if test="gender != null  and gender != ''">and gender = #{gender}</if>
            <if test="openid != null  and openid != ''">and openid = #{openid}</if>
            <if test="introduce != null  and introduce != ''">and introduce = #{introduce}</if>
            <if test="photos != null  and photos != ''">and photos = #{photos}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="modifyTime != null ">and modify_time = #{modifyTime}</if>
        </where>
    </select>

    <select id="selectById" parameterType="java.lang.Long" resultMap="userResult">
        select *
        from tb_user
        where id = #{id}
    </select>

    <select id="selectByIds" parameterType="java.util.List" resultMap="userResult">
        select * from tb_user where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectByOpenid" parameterType="java.lang.String" resultMap="userResult">
        select *
        from tb_user
        where openid = #{openid}
    </select>

    <select id="selectByMobile" parameterType="java.lang.String" resultMap="userResult">
        select *
        from tb_user
        where mobile = #{mobile}
    </select>

    <insert id="insert" parameterType="com.domain.User" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="mobile != null">mobile,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatar != null">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="openid != null">openid,</if>
            <if test="introduce != null">introduce,</if>
            <if test="photos != null">photos,</if>
            <if test="status != null">status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="mobile != null">#{mobile},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="openid != null">#{openid},</if>
            <if test="introduce != null">#{introduce},</if>
            <if test="photos != null">#{photos},</if>
            <if test="status != null">#{status},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.User">
        update tb_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="introduce != null">introduce = #{introduce},</if>
            <if test="photos != null">photos = #{photos},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updatePartialById" parameterType="map">
        UPDATE tb_user
        <set>
            <foreach collection="changes" index="key" item="value" separator=",">
                ${key} = #{value}
            </foreach>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from tb_user
        where id = #{id}
    </delete>


</mapper>