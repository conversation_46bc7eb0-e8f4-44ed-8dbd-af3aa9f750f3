<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.UserCartDao">
    
    <resultMap type="com.dto.UserCartDTO" id="userCartResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="productId"    column="product_id"    />
        <result property="productSkuId"    column="product_sku_id"    />
        <result property="productOptionIds"    column="product_option_ids"    />
        <result property="productIngredientIds"    column="product_ingredient_ids"    />
        <result property="number"    column="number"    />
        <result property="status"    column="status"    />
        <result property="userCartStatus"    column="user_cart_status"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <select id="select" parameterType="com.query.UserCartQuery" resultMap="userCartResult">
        select * from tb_user_cart
        <where>  
            <if test="id != null "> and id = #{id}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="productId != null "> and product_id = #{productId}</if>
            <if test="productSkuId != null "> and product_sku_id = #{productSkuId}</if>
            <if test="productOptionIds != null  and productOptionIds != ''"> and product_option_ids = #{productOptionIds}</if>
            <if test="productIngredientIds != null  and productIngredientIds != ''"> and product_ingredient_ids = #{productIngredientIds}</if>
            <if test="number != null "> and number = #{number}</if>
            <if test="userCartStatus != null  and userCartStatus != ''"> and user_cart_status = #{userCartStatus}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
        </where>
        order by id desc
    </select>
    
    <select id="selectById" parameterType="java.lang.Long" resultMap="userCartResult">
         select * from tb_user_cart where id = #{id}
    </select>



    <select id="selectByIds" parameterType="java.util.List" resultMap="userCartResult">
        select * from tb_user_cart where id in
        <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        order by id desc
    </select>


    <insert id="insert" parameterType="com.domain.UserCart" useGeneratedKeys="true" keyProperty="id">
        insert into tb_user_cart
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="shopId != null">shop_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="productSkuId != null">product_sku_id,</if>
            <if test="productOptionIds != null">product_option_ids,</if>
            <if test="productIngredientIds != null">product_ingredient_ids,</if>
            <if test="number != null">number,</if>
            <if test="status != null">status,</if>
            <if test="userCartStatus != null">user_cart_status,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="shopId != null">#{shopId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="productSkuId != null">#{productSkuId},</if>
            <if test="productOptionIds != null">#{productOptionIds},</if>
            <if test="productIngredientIds != null">#{productIngredientIds},</if>
            <if test="number != null">#{number},</if>
            <if test="status != null">#{status},</if>
            <if test="userCartStatus != null">#{userCartStatus},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateById" parameterType="com.domain.UserCart">
        update tb_user_cart
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="productSkuId != null">product_sku_id = #{productSkuId},</if>
            <if test="productOptionIds != null">product_option_ids = #{productOptionIds},</if>
            <if test="productIngredientIds != null">product_ingredient_ids = #{productIngredientIds},</if>
            <if test="number != null">number = #{number},</if>
            <if test="userCartStatus != null">user_cart_status = #{userCartStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateByProductId" parameterType="com.domain.UserCart">
        update tb_user_cart
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="productSkuId != null">product_sku_id = #{productSkuId},</if>
            <if test="productOptionIds != null">product_option_ids = #{productOptionIds},</if>
            <if test="productIngredientIds != null">product_ingredient_ids = #{productIngredientIds},</if>
            <if test="number != null">number = #{number},</if>
            <if test="userCartStatus != null">user_cart_status = #{userCartStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where product_id = #{productId}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from tb_user_cart where id = #{id}
    </delete>


</mapper>