package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.District;
import com.dto.DistrictDTO;
import com.query.DistrictQuery;

/**
 * 区(县) 接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */

@Mapper
public interface DistrictDao {
    /**
     * 查询区(县)
     *
     * @param id 区(县)id
     * @return 区(县)
     */
    DistrictDTO selectById(Long id);


    /**
     * 查询区(县)列表
     *
     * @param ids 编号集合
     * @return 区(县)集合
     */
    List<DistrictDTO> selectByIds(List<Long> ids);


    /**
     * 查询区(县)列表
     *
     * @param districtQuery 区(县)
     * @return 区(县)集合
     */
    List<DistrictDTO> select(DistrictQuery districtQuery);

    /**
     * 新增区(县)
     *
     * @param district 区(县)
     * @return 结果
     */
    int insert(District district);

    /**
     * 修改区(县)
     *
     * @param district 区(县)
     * @return 结果
     */
    int updateById(District district);

    /**
     * 删除区(县)
     *
     * @param id 区(县)Id
     * @return 结果
     */
    int deleteById(Long id);

}
