package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Order;
import com.dto.OrderDTO;
import com.query.OrderQuery;

/**
 * 订单 接口
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */

@Mapper
public interface OrderDao {
    /**
     * 查询订单
     * 
     * @param id 订单id
     * @return 订单
     */
    OrderDTO selectById(Long id);


    /**
     * 查询订单列表
     *
     * @param ids 编号集合
     * @return 订单集合
     */
    List<OrderDTO> selectByIds(List<Long> ids);


    /**
     * 查询订单列表
     * 
     * @param orderQuery 订单
     * @return 订单集合
     */
    List<OrderDTO> select(OrderQuery orderQuery);

    /**
     * 新增订单
     * 
     * @param order 订单
     * @return 结果
     */
    int insert(Order order);

    /**
     * 修改订单
     * 
     * @param order 订单
     * @return 结果
     */
    int updateById(Order order);

    /**
     * 删除订单
     * 
     * @param id 订单Id
     * @return 结果
     */
    int deleteById(Long id);

}
