package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Tag;
import com.dto.TagDTO;
import com.query.TagQuery;

/**
 * 标签 接口
 *
 * <AUTHOR>
 * @date 2025-08-04
 */

@Mapper
public interface TagDao {
    /**
     * 查询标签
     *
     * @param id 标签id
     * @return 标签
     */
    TagDTO selectById(Long id);


    /**
     * 查询标签列表
     *
     * @param ids 编号集合
     * @return 标签集合
     */
    List<TagDTO> selectByIds(List<Long> ids);


    /**
     * 查询标签列表
     *
     * @param tagQuery 标签
     * @return 标签集合
     */
    List<TagDTO> select(TagQuery tagQuery);

    /**
     * 新增标签
     *
     * @param tag 标签
     * @return 结果
     */
    int insert(Tag tag);

    /**
     * 修改标签
     *
     * @param tag 标签
     * @return 结果
     */
    int updateById(Tag tag);

    /**
     * 删除标签
     *
     * @param id 标签Id
     * @return 结果
     */
    int deleteById(Long id);

}
