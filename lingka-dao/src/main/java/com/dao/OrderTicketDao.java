package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderTicket;
import com.dto.OrderTicketDTO;
import com.query.OrderTicketQuery;

/**
 * 订单门票 接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */

@Mapper
public interface OrderTicketDao {
    /**
     * 查询订单门票
     * 
     * @param id 订单门票id
     * @return 订单门票
     */
    OrderTicketDTO selectById(Long id);


    /**
     * 查询订单门票列表
     *
     * @param ids 编号集合
     * @return 订单门票集合
     */
    List<OrderTicketDTO> selectByIds(List<Long> ids);


    /**
     * 查询订单门票列表
     * 
     * @param orderTicketQuery 订单门票
     * @return 订单门票集合
     */
    List<OrderTicketDTO> select(OrderTicketQuery orderTicketQuery);

    /**
     * 新增订单门票
     * 
     * @param orderTicket 订单门票
     * @return 结果
     */
    int insert(OrderTicket orderTicket);

    /**
     * 修改订单门票
     * 
     * @param orderTicket 订单门票
     * @return 结果
     */
    int updateById(OrderTicket orderTicket);

    /**
     * 删除订单门票
     * 
     * @param id 订单门票Id
     * @return 结果
     */
    int deleteById(Long id);

}
