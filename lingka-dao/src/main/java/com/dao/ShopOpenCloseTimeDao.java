package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ShopOpenCloseTime;
import com.dto.ShopOpenCloseTimeDTO;
import com.query.ShopOpenCloseTimeQuery;

/**
 * 店铺营业时间 接口
 *
 */

@Mapper
public interface ShopOpenCloseTimeDao {
    /**
     * 查询店铺营业时间
     *
     * @param id 店铺营业时间id
     * @return 店铺营业时间
     */
    ShopOpenCloseTimeDTO selectById(Long id);


    /**
     * 查询店铺营业时间列表
     *
     * @param ids 编号集合
     * @return 店铺营业时间集合
     */
    List<ShopOpenCloseTimeDTO> selectByIds(List<Long> ids);


    /**
     * 查询店铺营业时间列表
     *
     * @param shopOpenCloseTimeQuery 店铺营业时间
     * @return 店铺营业时间集合
     */
    List<ShopOpenCloseTimeDTO> select(ShopOpenCloseTimeQuery shopOpenCloseTimeQuery);

    /**
     * 新增店铺营业时间
     *
     * @param shopOpenCloseTime 店铺营业时间
     * @return 结果
     */
    int insert(ShopOpenCloseTime shopOpenCloseTime);

    /**
     * 修改店铺营业时间
     *
     * @param shopOpenCloseTime 店铺营业时间
     * @return 结果
     */
    int updateById(ShopOpenCloseTime shopOpenCloseTime);

    /**
     * 删除店铺营业时间
     *
     * @param id 店铺营业时间Id
     * @return 删除计数
     */
    int deleteById(Long id);

    /**
     * 删除店铺营业时间
     *
     * @param shopId 店铺 Id
     * @return 删除计数
     */
    int deleteByShopId(Long shopId);
}
