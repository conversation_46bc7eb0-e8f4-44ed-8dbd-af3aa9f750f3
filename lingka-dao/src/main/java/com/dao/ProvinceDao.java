package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Province;
import com.dto.ProvinceDTO;
import com.query.ProvinceQuery;

/**
 * 省份 接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */

@Mapper
public interface ProvinceDao {
    /**
     * 查询省份
     *
     * @param id 省份id
     * @return 省份
     */
    ProvinceDTO selectById(Long id);


    /**
     * 查询省份列表
     *
     * @param ids 编号集合
     * @return 省份集合
     */
    List<ProvinceDTO> selectByIds(List<Long> ids);


    /**
     * 查询省份列表
     *
     * @param provinceQuery 省份
     * @return 省份集合
     */
    List<ProvinceDTO> select(ProvinceQuery provinceQuery);

    /**
     * 新增省份
     *
     * @param province 省份
     * @return 结果
     */
    int insert(Province province);

    /**
     * 修改省份
     *
     * @param province 省份
     * @return 结果
     */
    int updateById(Province province);

    /**
     * 删除省份
     *
     * @param id 省份Id
     * @return 结果
     */
    int deleteById(Long id);

}
