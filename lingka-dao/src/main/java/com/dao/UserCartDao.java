package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserCart;
import com.dto.UserCartDTO;
import com.query.UserCartQuery;

/**
 * 用户购物车 接口
 * 
 * <AUTHOR>
 * @date 2025-08-30
 */

@Mapper
public interface UserCartDao {
    /**
     * 查询用户购物车
     * 
     * @param id 用户购物车id
     * @return 用户购物车
     */
    UserCartDTO selectById(Long id);


    /**
     * 查询用户购物车列表
     *
     * @param ids 编号集合
     * @return 用户购物车集合
     */
    List<UserCartDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户购物车列表
     * 
     * @param userCartQuery 用户购物车
     * @return 用户购物车集合
     */
    List<UserCartDTO> select(UserCartQuery userCartQuery);

    /**
     * 新增用户购物车
     * 
     * @param userCart 用户购物车
     * @return 结果
     */
    int insert(UserCart userCart);

    /**
     * 修改用户购物车
     * 
     * @param userCart 用户购物车
     * @return 结果
     */
    int updateById(UserCart userCart);

    /**
     * 修改用户购物车
     *
     * @param userCart 用户购物车
     * @return 结果
     */
    int updateByProductId(UserCart userCart);

    /**
     * 删除用户购物车
     * 
     * @param id 用户购物车Id
     * @return 结果
     */
    int deleteById(Long id);

}
