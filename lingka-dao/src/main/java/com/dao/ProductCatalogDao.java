package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ProductCatalog;
import com.dto.ProductCatalogDTO;
import com.query.ProductCatalogQuery;

/**
 * 产品分类 接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

@Mapper
public interface ProductCatalogDao {
    /**
     * 查询产品分类
     * 
     * @param id 产品分类id
     * @return 产品分类
     */
    ProductCatalogDTO selectById(Long id);


    /**
     * 查询产品分类列表
     *
     * @param ids 编号集合
     * @return 产品分类集合
     */
    List<ProductCatalogDTO> selectByIds(List<Long> ids);


    /**
     * 查询产品分类列表
     * 
     * @param productCatalogQuery 产品分类
     * @return 产品分类集合
     */
    List<ProductCatalogDTO> select(ProductCatalogQuery productCatalogQuery);

    /**
     * 排序查询产品分类列表
     *
     * @param productCatalogQuery 产品分类
     * @return 产品分类集合
     */
    List<ProductCatalogDTO> selectBySort(ProductCatalogQuery productCatalogQuery);

    /**
     * 新增产品分类
     * 
     * @param productCatalog 产品分类
     * @return 结果
     */
    int insert(ProductCatalog productCatalog);

    /**
     * 修改产品分类
     * 
     * @param productCatalog 产品分类
     * @return 结果
     */
    int updateById(ProductCatalog productCatalog);

    /**
     * 删除产品分类
     * 
     * @param id 产品分类Id
     * @return 结果
     */
    int deleteById(Long id);

}
