package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserShopHistory;
import com.dto.UserShopHistoryDTO;
import com.query.UserShopHistoryQuery;


/**
 * 用户浏览店铺历史记录 接口
 *
 */

@Mapper
public interface UserShopHistoryDao {
    /**
     * 查询用户浏览店铺历史记录
     *
     * @param id 用户浏览店铺历史记录id
     * @return 用户浏览店铺历史记录
     */
    UserShopHistoryDTO selectById(Long id);

    /**
     * 查询用户浏览店铺历史记录列表
     *
     * @param ids 编号集合
     * @return 用户浏览店铺历史记录集合
     */
    List<UserShopHistoryDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户浏览店铺历史记录列表
     *
     * @param userShopHistoryQuery 用户浏览店铺历史记录
     * @return 用户浏览店铺历史记录集合
     */
    List<UserShopHistoryDTO> select(UserShopHistoryQuery userShopHistoryQuery);

    /**
     * 新增用户浏览店铺历史记录
     *
     * @param userShopHistory 用户浏览店铺历史记录
     * @return 结果
     */
    int insert(UserShopHistory userShopHistory);

    /**
     * 修改用户浏览店铺历史记录
     *
     * @param userShopHistory 用户浏览店铺历史记录
     * @return 结果
     */
    int updateById(UserShopHistory userShopHistory);

    /**
     * 删除用户浏览店铺历史记录
     *
     * @param id 用户浏览店铺历史记录Id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 删除用户浏览店铺历史记录
     *
     * @param shopId 店铺 Id
     * @return 删除计数
     */
    int deleteByShopId(Long shopId);

    /**
     * 删除用户浏览店铺历史记录
     *
     * @param userId 用户 Id
     * @return 删除计数
     */
    int deleteByUserId(Long userId);
}
