package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.City;
import com.dto.CityDTO;
import com.query.CityQuery;

/**
 * 城市 接口
 *
 * <AUTHOR>
 * @date 2025-08-06
 */

@Mapper
public interface CityDao {
    /**
     * 查询城市
     *
     * @param id 城市id
     * @return 城市
     */
    CityDTO selectById(Long id);


    /**
     * 查询城市列表
     *
     * @param ids 编号集合
     * @return 城市集合
     */
    List<CityDTO> selectByIds(List<Long> ids);


    /**
     * 查询城市列表
     *
     * @param cityQuery 城市
     * @return 城市集合
     */
    List<CityDTO> select(CityQuery cityQuery);

    /**
     * 新增城市
     *
     * @param city 城市
     * @return 结果
     */
    int insert(City city);

    /**
     * 修改城市
     *
     * @param city 城市
     * @return 结果
     */
    int updateById(City city);

    /**
     * 删除城市
     *
     * @param id 城市Id
     * @return 结果
     */
    int deleteById(Long id);

}
