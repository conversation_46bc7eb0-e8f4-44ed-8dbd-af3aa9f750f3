package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Admin;
import com.dto.AdminDTO;
import com.query.AdminQuery;

/**
 * 管理员 接口
 *
 * <AUTHOR>
 * @date 2025-08-01
 */

@Mapper
public interface AdminDao {
    /**
     * 查询管理员
     *
     * @param id 管理员id
     * @return 管理员
     */
    AdminDTO selectById(Long id);

    /**
     * 查询管理员
     *
     * @param username 管理员用户名
     * @return 管理员
     */
    AdminDTO selectByUsername(String username);

    /**
     * 查询管理员
     *
     * @param mobile 管理员手机号
     * @return 管理员
     */
    AdminDTO selectByMobile(String mobile);


    /**
     * 查询管理员列表
     *
     * @param ids 编号集合
     * @return 管理员集合
     */
    List<AdminDTO> selectByIds(List<Long> ids);


    /**
     * 查询管理员列表
     *
     * @param adminQuery 管理员
     * @return 管理员集合
     */
    List<AdminDTO> select(AdminQuery adminQuery);

    /**
     * 新增管理员
     *
     * @param admin 管理员
     * @return 结果
     */
    int insert(Admin admin);

    /**
     * 修改管理员
     *
     * @param admin 管理员
     * @return 结果
     */
    int updateById(Admin admin);

    /**
     * 删除管理员
     *
     * @param id 管理员Id
     * @return 结果
     */
    int deleteById(Long id);

}
