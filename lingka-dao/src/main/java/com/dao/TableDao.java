package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Table;
import com.dto.TableDTO;
import com.query.TableQuery;

/**
 * 桌子 接口
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */

@Mapper
public interface TableDao {
    /**
     * 查询桌子
     * 
     * @param id 桌子id
     * @return 桌子
     */
    TableDTO selectById(Long id);


    /**
     * 查询桌子列表
     *
     * @param ids 编号集合
     * @return 桌子集合
     */
    List<TableDTO> selectByIds(List<Long> ids);


    /**
     * 查询桌子列表
     * 
     * @param tableQuery 桌子
     * @return 桌子集合
     */
    List<TableDTO> select(TableQuery tableQuery);

    /**
     * 新增桌子
     * 
     * @param table 桌子
     * @return 结果
     */
    int insert(Table table);

    /**
     * 修改桌子
     * 
     * @param table 桌子
     * @return 结果
     */
    int updateById(Table table);

    /**
     * 删除桌子
     * 
     * @param id 桌子Id
     * @return 结果
     */
    int deleteById(Long id);

}
