package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ProductIngredientConfig;
import com.dto.ProductIngredientConfigDTO;
import com.query.ProductIngredientConfigQuery;

/**
 * 产品小料配置 接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

@Mapper
public interface ProductIngredientConfigDao {
    /**
     * 查询产品小料配置
     * 
     * @param id 产品小料配置id
     * @return 产品小料配置
     */
    ProductIngredientConfigDTO selectById(Long id);


    /**
     * 查询产品小料配置列表
     *
     * @param ids 编号集合
     * @return 产品小料配置集合
     */
    List<ProductIngredientConfigDTO> selectByIds(List<Long> ids);


    /**
     * 查询产品小料配置列表
     * 
     * @param productIngredientConfigQuery 产品小料配置
     * @return 产品小料配置集合
     */
    List<ProductIngredientConfigDTO> select(ProductIngredientConfigQuery productIngredientConfigQuery);

    /**
     * 新增产品小料配置
     * 
     * @param productIngredientConfig 产品小料配置
     * @return 结果
     */
    int insert(ProductIngredientConfig productIngredientConfig);

    /**
     * 修改产品小料配置
     * 
     * @param productIngredientConfig 产品小料配置
     * @return 结果
     */
    int updateById(ProductIngredientConfig productIngredientConfig);

    /**
     * 删除产品小料配置
     * 
     * @param id 产品小料配置Id
     * @return 结果
     */
    int deleteById(Long id);

}
