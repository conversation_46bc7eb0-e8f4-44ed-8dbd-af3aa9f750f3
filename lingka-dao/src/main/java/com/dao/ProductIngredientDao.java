package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ProductIngredient;
import com.dto.ProductIngredientDTO;
import com.query.ProductIngredientQuery;

/**
 * 产品小料 接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

@Mapper
public interface ProductIngredientDao {
    /**
     * 查询产品小料
     * 
     * @param id 产品小料id
     * @return 产品小料
     */
    ProductIngredientDTO selectById(Long id);


    /**
     * 查询产品小料列表
     *
     * @param ids 编号集合
     * @return 产品小料集合
     */
    List<ProductIngredientDTO> selectByIds(List<Long> ids);


    /**
     * 查询产品小料列表
     * 
     * @param productIngredientQuery 产品小料
     * @return 产品小料集合
     */
    List<ProductIngredientDTO> select(ProductIngredientQuery productIngredientQuery);

    /**
     * 新增产品小料
     * 
     * @param productIngredient 产品小料
     * @return 结果
     */
    int insert(ProductIngredient productIngredient);

    /**
     * 修改产品小料
     * 
     * @param productIngredient 产品小料
     * @return 结果
     */
    int updateById(ProductIngredient productIngredient);

    /**
     * 删除产品小料
     * 
     * @param id 产品小料Id
     * @return 结果
     */
    int deleteById(Long id);

}
