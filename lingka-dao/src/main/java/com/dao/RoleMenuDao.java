package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.RoleMenu;
import com.dto.RoleMenuDTO;
import com.query.RoleMenuQuery;

/**
 * 角色菜单关联 接口
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */

@Mapper
public interface RoleMenuDao {
    /**
     * 查询角色菜单关联
     * 
     * @param id 角色菜单关联id
     * @return 角色菜单关联
     */
    RoleMenuDTO selectById(Long id);


    /**
     * 查询角色菜单关联列表
     *
     * @param ids 编号集合
     * @return 角色菜单关联集合
     */
    List<RoleMenuDTO> selectByIds(List<Long> ids);


    /**
     * 查询角色菜单关联列表
     * 
     * @param roleMenuQuery 角色菜单关联
     * @return 角色菜单关联集合
     */
    List<RoleMenuDTO> select(RoleMenuQuery roleMenuQuery);

    /**
     * 新增角色菜单关联
     * 
     * @param roleMenu 角色菜单关联
     * @return 结果
     */
    int insert(RoleMenu roleMenu);

    /**
     * 修改角色菜单关联
     * 
     * @param roleMenu 角色菜单关联
     * @return 结果
     */
    int updateById(RoleMenu roleMenu);

    /**
     * 删除角色菜单关联
     * 
     * @param id 角色菜单关联Id
     * @return 结果
     */
    int deleteById(Long id);

}
