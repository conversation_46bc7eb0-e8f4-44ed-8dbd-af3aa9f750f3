package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ProductOption;
import com.dto.ProductOptionDTO;
import com.query.ProductOptionQuery;

/**
 * 产品选项 接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

@Mapper
public interface ProductOptionDao {
    /**
     * 查询产品选项
     * 
     * @param id 产品选项id
     * @return 产品选项
     */
    ProductOptionDTO selectById(Long id);


    /**
     * 查询产品选项列表
     *
     * @param ids 编号集合
     * @return 产品选项集合
     */
    List<ProductOptionDTO> selectByIds(List<Long> ids);


    /**
     * 查询产品选项列表
     * 
     * @param productOptionQuery 产品选项
     * @return 产品选项集合
     */
    List<ProductOptionDTO> select(ProductOptionQuery productOptionQuery);

    /**
     * 新增产品选项
     * 
     * @param productOption 产品选项
     * @return 结果
     */
    int insert(ProductOption productOption);

    /**
     * 修改产品选项
     * 
     * @param productOption 产品选项
     * @return 结果
     */
    int updateById(ProductOption productOption);

    /**
     * 删除产品选项
     * 
     * @param id 产品选项Id
     * @return 结果
     */
    int deleteById(Long id);

}
