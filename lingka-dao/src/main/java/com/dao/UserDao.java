package com.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.domain.User;
import com.dto.UserDTO;
import com.query.UserQuery;

/**
 * 用户 接口
 *
 * <AUTHOR>
 * @date 2025-08-03
 */

@Mapper
public interface UserDao {
    /**
     * 查询用户
     *
     * @param id 用户id
     * @return 用户
     */
    UserDTO selectById(Long id);


    /**
     * 查询用户列表
     *
     * @param ids 编号集合
     * @return 用户集合
     */
    List<UserDTO> selectByIds(List<Long> ids);

    /**
     * 查询用户
     *
     * @param openid openid
     * @return 用户
     */
    UserDTO selectByOpenid(String openid);

    /**
     * 查询用户
     *
     * @param mobile 手机号
     * @return 用户
     */
    UserDTO selectByMobile(String mobile);

    /**
     * 查询用户列表
     *
     * @param userQuery 用户
     * @return 用户集合
     */
    List<UserDTO> select(UserQuery userQuery);

    /**
     * 新增用户
     *
     * @param user 用户
     * @return 结果
     */
    int insert(User user);

    /**
     * 修改用户
     *
     * @param user 用户
     * @return 结果
     */
    int updateById(User user);

    /**
     * 删除用户
     *
     * @param id 用户Id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 增量修改用户配置
     *
     * @param params 用户差异配置
     * @return 更新数量
     */
    int updatePartialById(Map<String, Object> params);
}
