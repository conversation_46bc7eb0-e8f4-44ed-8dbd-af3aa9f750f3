package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserTicketApply;
import com.dto.UserTicketApplyDTO;
import com.query.UserTicketApplyQuery;

/**
 * 用户门票报名 接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */

@Mapper
public interface UserTicketApplyDao {
    /**
     * 查询用户门票报名
     * 
     * @param id 用户门票报名id
     * @return 用户门票报名
     */
    UserTicketApplyDTO selectById(Long id);


    /**
     * 查询用户门票报名列表
     *
     * @param ids 编号集合
     * @return 用户门票报名集合
     */
    List<UserTicketApplyDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户门票报名列表
     * 
     * @param userTicketApplyQuery 用户门票报名
     * @return 用户门票报名集合
     */
    List<UserTicketApplyDTO> select(UserTicketApplyQuery userTicketApplyQuery);

    /**
     * 新增用户门票报名
     * 
     * @param userTicketApply 用户门票报名
     * @return 结果
     */
    int insert(UserTicketApply userTicketApply);

    /**
     * 修改用户门票报名
     * 
     * @param userTicketApply 用户门票报名
     * @return 结果
     */
    int updateById(UserTicketApply userTicketApply);

    /**
     * 删除用户门票报名
     * 
     * @param id 用户门票报名Id
     * @return 结果
     */
    int deleteById(Long id);

}
