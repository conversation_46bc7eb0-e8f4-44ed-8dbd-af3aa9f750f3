package com.dao;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ShopConfig;
import com.dto.ShopConfigDTO;
import com.query.ShopConfigQuery;

/**
 * 店铺配置 接口
 *
 * <AUTHOR>
 */

@Mapper
public interface ShopConfigDao {
    /**
     * 查询店铺配置
     *
     * @param id 店铺配置id
     * @return 店铺配置
     */
    ShopConfigDTO selectById(Long id);

    /**
     * 查询店铺配置
     *
     * @param shoId 店铺id
     * @return 店铺配置
     */
    ShopConfigDTO selectByShopId(Long shoId);


    /**
     * 查询店铺配置列表
     *
     * @param ids 编号集合
     * @return 店铺配置集合
     */
    List<ShopConfigDTO> selectByIds(List<Long> ids);

    /**
     * 查询店铺配置列表
     *
     * @param shopIds 店铺编号集合
     * @return 店铺配置集合
     */
    List<ShopConfigDTO> selectByShopIds(List<Long> shopIds);


    /**
     * 查询店铺配置列表
     *
     * @param shopConfigQuery 店铺配置
     * @return 店铺配置集合
     */
    List<ShopConfigDTO> select(ShopConfigQuery shopConfigQuery);

    /**
     * 新增店铺配置
     *
     * @param shopConfig 店铺配置
     * @return 结果
     */
    int insert(ShopConfig shopConfig);

    /**
     * 修改店铺配置
     *
     * @param shopConfig 店铺配置
     * @return 结果
     */
    int updateById(ShopConfig shopConfig);

    /**
     * 部分修改店铺配置
     *
     * @param params 店铺差异配置
     * @return 更新数量
     */
    int updatePartialById(Map<String, Object> params);

    /**
     * 删除店铺配置
     *
     * @param id 店铺配置Id
     * @return 结果
     */
    int deleteById(Long id);

}
