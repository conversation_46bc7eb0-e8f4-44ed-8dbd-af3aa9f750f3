package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderProduct;
import com.dto.OrderProductDTO;
import com.query.OrderProductQuery;

/**
 * 订单产品 接口
 * 
 * <AUTHOR>
 * @date 2025-09-06
 */

@Mapper
public interface OrderProductDao {
    /**
     * 查询订单产品
     * 
     * @param id 订单产品id
     * @return 订单产品
     */
    OrderProductDTO selectById(Long id);


    /**
     * 查询订单产品列表
     *
     * @param ids 编号集合
     * @return 订单产品集合
     */
    List<OrderProductDTO> selectByIds(List<Long> ids);


    /**
     * 查询订单产品列表
     * 
     * @param orderProductQuery 订单产品
     * @return 订单产品集合
     */
    List<OrderProductDTO> select(OrderProductQuery orderProductQuery);

    /**
     * 新增订单产品
     * 
     * @param orderProduct 订单产品
     * @return 结果
     */
    int insert(OrderProduct orderProduct);

    /**
     * 修改订单产品
     * 
     * @param orderProduct 订单产品
     * @return 结果
     */
    int updateById(OrderProduct orderProduct);

    /**
     * 删除订单产品
     * 
     * @param id 订单产品Id
     * @return 结果
     */
    int deleteById(Long id);

}
