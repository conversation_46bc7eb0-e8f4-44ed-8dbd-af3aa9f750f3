package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Ticket;
import com.dto.TicketDTO;
import com.query.TicketQuery;

/**
 * 门票 接口
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */

@Mapper
public interface TicketDao {
    /**
     * 查询门票
     * 
     * @param id 门票id
     * @return 门票
     */
    TicketDTO selectById(Long id);


    /**
     * 查询门票列表
     *
     * @param ids 编号集合
     * @return 门票集合
     */
    List<TicketDTO> selectByIds(List<Long> ids);


    /**
     * 查询门票列表
     * 
     * @param ticketQuery 门票
     * @return 门票集合
     */
    List<TicketDTO> select(TicketQuery ticketQuery);

    /**
     * 新增门票
     * 
     * @param ticket 门票
     * @return 结果
     */
    int insert(Ticket ticket);

    /**
     * 修改门票
     * 
     * @param ticket 门票
     * @return 结果
     */
    int updateById(Ticket ticket);

    /**
     * 删除门票
     * 
     * @param id 门票Id
     * @return 结果
     */
    int deleteById(Long id);

}
