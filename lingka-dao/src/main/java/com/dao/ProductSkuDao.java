package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ProductSku;
import com.dto.ProductSkuDTO;
import com.query.ProductSkuQuery;

/**
 * 产品SKU 接口
 * 
 * <AUTHOR>
 * @date 2025-08-28
 */

@Mapper
public interface ProductSkuDao {
    /**
     * 查询产品SKU
     * 
     * @param id 产品SKUid
     * @return 产品SKU
     */
    ProductSkuDTO selectById(Long id);


    /**
     * 查询产品SKU列表
     *
     * @param ids 编号集合
     * @return 产品SKU集合
     */
    List<ProductSkuDTO> selectByIds(List<Long> ids);


    /**
     * 查询产品SKU列表
     * 
     * @param productSkuQuery 产品SKU
     * @return 产品SKU集合
     */
    List<ProductSkuDTO> select(ProductSkuQuery productSkuQuery);

    /**
     * 新增产品SKU
     * 
     * @param productSku 产品SKU
     * @return 结果
     */
    int insert(ProductSku productSku);

    /**
     * 修改产品SKU
     * 
     * @param productSku 产品SKU
     * @return 结果
     */
    int updateById(ProductSku productSku);

    /**
     * 删除产品SKU
     * 
     * @param id 产品SKUId
     * @return 结果
     */
    int deleteById(Long id);

}
