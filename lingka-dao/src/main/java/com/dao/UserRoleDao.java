package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.domain.UserRole;
import com.dto.UserRoleDTO;
import com.query.UserRoleQuery;

/**
 * 用户角色关联 接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */

@Mapper
public interface UserRoleDao {
    /**
     * 查询用户角色关联
     * 
     * @param id 用户角色关联id
     * @return 用户角色关联
     */
    UserRoleDTO selectById(Long id);


    /**
     * 查询用户角色关联列表
     *
     * @param ids 编号集合
     * @return 用户角色关联集合
     */
    List<UserRoleDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户角色关联列表
     * 
     * @param userRoleQuery 用户角色关联
     * @return 用户角色关联集合
     */
    List<UserRoleDTO> select(UserRoleQuery userRoleQuery);

    /**
     * 新增用户角色关联
     * 
     * @param userRole 用户角色关联
     * @return 结果
     */
    int insert(UserRole userRole);

    /**
     * 修改用户角色关联
     * 
     * @param userRole 用户角色关联
     * @return 结果
     */
    int updateById(UserRole userRole);

    /**
     * 删除用户角色关联
     * 
     * @param id 用户角色关联Id
     * @return 结果
     */
    int deleteById(Long id);

    int deleteByUserIdAndShopId(@Param("userId")Long userId, @Param("shopId")Long shopId);

    /**
     * 根据店铺 ID 删除用户角色关联
     *
     * @param shopId 店铺 ID
     * @return 删除数量
     */
    int deleteByShopId(Long shopId);
}
