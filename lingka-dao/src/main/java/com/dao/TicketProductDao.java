package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.TicketProduct;
import com.dto.TicketProductDTO;
import com.query.TicketProductQuery;

/**
 * 门票产品 接口
 * 
 * <AUTHOR>
 * @date 2025-09-11
 */

@Mapper
public interface TicketProductDao {
    /**
     * 查询门票产品
     * 
     * @param id 门票产品id
     * @return 门票产品
     */
    TicketProductDTO selectById(Long id);


    /**
     * 查询门票产品列表
     *
     * @param ids 编号集合
     * @return 门票产品集合
     */
    List<TicketProductDTO> selectByIds(List<Long> ids);


    /**
     * 查询门票产品列表
     * 
     * @param ticketProductQuery 门票产品
     * @return 门票产品集合
     */
    List<TicketProductDTO> select(TicketProductQuery ticketProductQuery);

    /**
     * 新增门票产品
     * 
     * @param ticketProduct 门票产品
     * @return 结果
     */
    int insert(TicketProduct ticketProduct);

    /**
     * 修改门票产品
     * 
     * @param ticketProduct 门票产品
     * @return 结果
     */
    int updateById(TicketProduct ticketProduct);

    /**
     * 删除门票产品
     * 
     * @param id 门票产品Id
     * @return 结果
     */
    int deleteById(Long id);

}
