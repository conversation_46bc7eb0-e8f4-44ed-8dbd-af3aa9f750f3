package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Role;
import com.dto.RoleDTO;
import com.query.RoleQuery;

/**
 * 角色 接口
 * 
 * <AUTHOR>
 * @date 2025-08-11
 */

@Mapper
public interface RoleDao {
    /**
     * 查询角色
     * 
     * @param id 角色id
     * @return 角色
     */
    RoleDTO selectById(Long id);


    /**
     * 查询角色列表
     *
     * @param ids 编号集合
     * @return 角色集合
     */
    List<RoleDTO> selectByIds(List<Long> ids);


    /**
     * 查询角色列表
     * 
     * @param roleQuery 角色
     * @return 角色集合
     */
    List<RoleDTO> select(RoleQuery roleQuery);

    /**
     * 新增角色
     * 
     * @param role 角色
     * @return 结果
     */
    int insert(Role role);

    /**
     * 修改角色
     * 
     * @param role 角色
     * @return 结果
     */
    int updateById(Role role);

    /**
     * 删除角色
     * 
     * @param id 角色Id
     * @return 结果
     */
    int deleteById(Long id);

}
