package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserTicketGiftRecord;
import com.dto.UserTicketGiftRecordDTO;
import com.query.UserTicketGiftRecordQuery;

/**
 * 用户门票转赠记录 接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */

@Mapper
public interface UserTicketGiftRecordDao {
    /**
     * 查询用户门票转赠记录
     * 
     * @param id 用户门票转赠记录id
     * @return 用户门票转赠记录
     */
    UserTicketGiftRecordDTO selectById(Long id);


    /**
     * 查询用户门票转赠记录列表
     *
     * @param ids 编号集合
     * @return 用户门票转赠记录集合
     */
    List<UserTicketGiftRecordDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户门票转赠记录列表
     * 
     * @param userTicketGiftRecordQuery 用户门票转赠记录
     * @return 用户门票转赠记录集合
     */
    List<UserTicketGiftRecordDTO> select(UserTicketGiftRecordQuery userTicketGiftRecordQuery);

    /**
     * 新增用户门票转赠记录
     * 
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    int insert(UserTicketGiftRecord userTicketGiftRecord);

    /**
     * 修改用户门票转赠记录
     * 
     * @param userTicketGiftRecord 用户门票转赠记录
     * @return 结果
     */
    int updateById(UserTicketGiftRecord userTicketGiftRecord);

    /**
     * 删除用户门票转赠记录
     * 
     * @param id 用户门票转赠记录Id
     * @return 结果
     */
    int deleteById(Long id);

}
