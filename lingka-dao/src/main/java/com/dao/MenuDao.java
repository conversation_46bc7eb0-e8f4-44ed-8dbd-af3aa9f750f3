package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Menu;
import com.dto.MenuDTO;
import com.query.MenuQuery;

/**
 * 菜单 接口
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */

@Mapper
public interface MenuDao {
    /**
     * 查询菜单
     * 
     * @param id 菜单id
     * @return 菜单
     */
    MenuDTO selectById(Long id);


    /**
     * 查询菜单列表
     *
     * @param ids 编号集合
     * @return 菜单集合
     */
    List<MenuDTO> selectByIds(List<Long> ids);


    /**
     * 查询菜单列表
     * 
     * @param menuQuery 菜单
     * @return 菜单集合
     */
    List<MenuDTO> select(MenuQuery menuQuery);

    /**
     * 新增菜单
     * 
     * @param menu 菜单
     * @return 结果
     */
    int insert(Menu menu);

    /**
     * 修改菜单
     * 
     * @param menu 菜单
     * @return 结果
     */
    int updateById(Menu menu);

    /**
     * 删除菜单
     * 
     * @param id 菜单Id
     * @return 结果
     */
    int deleteById(Long id);

}
