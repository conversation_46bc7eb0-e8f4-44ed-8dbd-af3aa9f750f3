package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Audit;
import com.dto.AuditDTO;
import com.query.AuditQuery;

/**
 * 审核结果记录 接口
 *
 * <AUTHOR>
 */

@Mapper
public interface AuditDao {
    /**
     * 查询审核结果记录
     *
     * @param id 审核结果记录id
     * @return 审核结果记录
     */
    AuditDTO selectById(Long id);


    /**
     * 查询审核结果记录列表
     *
     * @param ids 编号集合
     * @return 审核结果记录集合
     */
    List<AuditDTO> selectByIds(List<Long> ids);


    /**
     * 查询审核结果记录列表
     *
     * @param auditQuery 审核结果记录
     * @return 审核结果记录集合
     */
    List<AuditDTO> select(AuditQuery auditQuery);

    /**
     * 新增审核结果记录
     *
     * @param audit 审核结果记录
     * @return 结果
     */
    int insert(Audit audit);

    /**
     * 修改审核结果记录
     *
     * @param audit 审核结果记录
     * @return 结果
     */
    int updateById(Audit audit);

    /**
     * 删除审核结果记录
     *
     * @param id 审核结果记录Id
     * @return 结果
     */
    int deleteById(Long id);

}
