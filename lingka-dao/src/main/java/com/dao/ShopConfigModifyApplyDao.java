package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.ShopConfigModifyApply;
import com.dto.ShopConfigModifyApplyDTO;
import com.query.ShopConfigModifyApplyQuery;

/**
 * 店铺配置修改申请 接口
 *
 * <AUTHOR>
 */

@Mapper
public interface ShopConfigModifyApplyDao {
    /**
     * 查询店铺配置修改申请
     *
     * @param id 店铺配置修改申请id
     * @return 店铺配置修改申请
     */
    ShopConfigModifyApplyDTO selectById(Long id);


    /**
     * 查询店铺配置修改申请列表
     *
     * @param ids 编号集合
     * @return 店铺配置修改申请集合
     */
    List<ShopConfigModifyApplyDTO> selectByIds(List<Long> ids);

    /**
     * 通过店铺编号查询店铺配置修改申请列表
     *
     * @param shopId 店铺编号
     * @return 店铺配置修改申请集合
     */
    List<ShopConfigModifyApplyDTO> selectByShopId(Long shopId);

    /**
     * 查询店铺配置修改申请列表
     *
     * @param shopConfigModifyApplyQuery 店铺配置修改申请
     * @return 店铺配置修改申请集合
     */
    List<ShopConfigModifyApplyDTO> select(ShopConfigModifyApplyQuery shopConfigModifyApplyQuery);

    /**
     * 新增店铺配置修改申请
     *
     * @param shopConfigModifyApply 店铺配置修改申请
     * @return 结果
     */
    int insert(ShopConfigModifyApply shopConfigModifyApply);

    /**
     * 修改店铺配置修改申请
     *
     * @param shopConfigModifyApply 店铺配置修改申请
     * @return 结果
     */
    int updateById(ShopConfigModifyApply shopConfigModifyApply);

    /**
     * 删除店铺配置修改申请
     *
     * @param id 店铺配置修改申请Id
     * @return 结果
     */
    int deleteById(Long id);

    /**
     * 批量删除店铺配置修改申请
     *
     * @param ids 店铺配置修改申请Id
     * @return 删除数量
     */
    int deleteByIds(List<Long> ids);

}
