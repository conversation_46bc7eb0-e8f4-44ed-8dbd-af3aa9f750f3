package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.OrderTicketDetail;
import com.dto.OrderTicketDetailDTO;
import com.query.OrderTicketDetailQuery;

/**
 * 订单门票详情 接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */

@Mapper
public interface OrderTicketDetailDao {
    /**
     * 查询订单门票详情
     * 
     * @param id 订单门票详情id
     * @return 订单门票详情
     */
    OrderTicketDetailDTO selectById(Long id);


    /**
     * 查询订单门票详情列表
     *
     * @param ids 编号集合
     * @return 订单门票详情集合
     */
    List<OrderTicketDetailDTO> selectByIds(List<Long> ids);


    /**
     * 查询订单门票详情列表
     * 
     * @param orderTicketDetailQuery 订单门票详情
     * @return 订单门票详情集合
     */
    List<OrderTicketDetailDTO> select(OrderTicketDetailQuery orderTicketDetailQuery);

    /**
     * 新增订单门票详情
     * 
     * @param orderTicketDetail 订单门票详情
     * @return 结果
     */
    int insert(OrderTicketDetail orderTicketDetail);

    /**
     * 修改订单门票详情
     * 
     * @param orderTicketDetail 订单门票详情
     * @return 结果
     */
    int updateById(OrderTicketDetail orderTicketDetail);

    /**
     * 删除订单门票详情
     * 
     * @param id 订单门票详情Id
     * @return 结果
     */
    int deleteById(Long id);

}
