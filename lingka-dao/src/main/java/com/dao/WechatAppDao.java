package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.WechatApp;
import com.dto.WechatAppDTO;
import com.query.WechatAppQuery;

/**
 * 微信应用 接口
 * 
 * <AUTHOR>
 * @date 2023-07-21
 */

@Mapper
public interface WechatAppDao {
    /**
     * 查询微信应用
     * 
     * @param id 微信应用id
     * @return 微信应用
     */
    WechatAppDTO selectById(Long id);

    /**
     * 查询微信应用列表
     * 
     * @param wechatAppDTO 微信应用
     * @return 微信应用集合
     */
    List<WechatAppDTO> select(WechatAppQuery wechatAppDTO);

    /**
     * 新增微信应用
     * 
     * @param wechatApp 微信应用
     * @return 结果
     */
    int insert(WechatApp wechatApp);

    /**
     * 修改微信应用
     * 
     * @param wechatApp 微信应用
     * @return 结果
     */
    int updateById(WechatApp wechatApp);

    /**
     * 删除微信应用
     * 
     * @param id 微信应用Id
     * @return 结果
     */
    int deleteById(Long id);

}
