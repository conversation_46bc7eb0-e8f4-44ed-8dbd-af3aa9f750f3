package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.domain.File;

@Mapper
public interface FileDao {
	File selectById(Long id);
	File selectByUrl(String url);
	Integer count(File file);
	List<File> select(
			@Param("file") File file,
			@Param("start") Integer start,
			@Param("limit") Integer limit
	);
	Integer insert(File file);
	Integer updateById(File file);

	File selectMinById(Integer id);
}
