package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.UserTicket;
import com.dto.UserTicketDTO;
import com.query.UserTicketQuery;

/**
 * 用户门票 接口
 * 
 * <AUTHOR>
 * @date 2025-09-14
 */

@Mapper
public interface UserTicketDao {
    /**
     * 查询用户门票
     * 
     * @param id 用户门票id
     * @return 用户门票
     */
    UserTicketDTO selectById(Long id);


    /**
     * 查询用户门票列表
     *
     * @param ids 编号集合
     * @return 用户门票集合
     */
    List<UserTicketDTO> selectByIds(List<Long> ids);


    /**
     * 查询用户门票列表
     * 
     * @param userTicketQuery 用户门票
     * @return 用户门票集合
     */
    List<UserTicketDTO> select(UserTicketQuery userTicketQuery);

    /**
     * 新增用户门票
     * 
     * @param userTicket 用户门票
     * @return 结果
     */
    int insert(UserTicket userTicket);

    /**
     * 修改用户门票
     * 
     * @param userTicket 用户门票
     * @return 结果
     */
    int updateById(UserTicket userTicket);

    /**
     * 删除用户门票
     * 
     * @param id 用户门票Id
     * @return 结果
     */
    int deleteById(Long id);

}
