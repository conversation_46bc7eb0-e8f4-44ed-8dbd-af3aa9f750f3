package com.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.domain.Sequence;
import com.dto.SequenceDTO;
import com.query.SequenceQuery;

/**
 * 序列 接口
 * 
 * <AUTHOR>
 * @date 2023-06-22
 */

@Mapper
public interface SequenceDao {
    /**
     * 查询序列
     * 
     * @param id 序列id
     * @return 序列
     */
    SequenceDTO selectById(Long id);

    /**
     * 查询序列列表
     * 
     * @param sequenceQuery 序列
     * @return 序列集合
     */
    List<SequenceDTO> select(SequenceQuery sequenceQuery);

    /**
     * 新增序列
     * 
     * @param sequence 序列
     * @return 结果
     */
    int insert(Sequence sequence);

    /**
     * 修改序列
     * 
     * @param sequence 序列
     * @return 结果
     */
    int updateById(Sequence sequence);

    /**
     * 删除序列
     * 
     * @param id 序列Id
     * @return 结果
     */
    int deleteById(Long id);

}
