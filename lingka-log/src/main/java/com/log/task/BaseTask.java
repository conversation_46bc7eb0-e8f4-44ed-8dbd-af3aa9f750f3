package com.log.task;

import java.math.BigDecimal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

public class BaseTask {
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	protected final String DATE_FORMAT = "yyyy-MM-dd";
	protected final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	
	String getJSON(Object obj){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			return objectMapper.writeValueAsString(obj);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
	
	Boolean isEmpty(String str){
		if(str != null){
			str = str.trim();
			if(!"".equals(str) && !"null".equalsIgnoreCase(str) && !"undefined".equalsIgnoreCase(str)){
				return false;
			}
		}
		return true;
	}
	Boolean isEmpty(Object obj){
		if(obj != null){
			if(!"".equals(obj) && !"null".equals(obj) && !"undefined".equals(obj)){
				return false;
			}
		}
		return true;
	}
	Integer getInteger(String str){
		if(isEmpty(str)){
			return null;
		}
		return Integer.valueOf(str);
	}
	
	String getString(BigDecimal bigDecimal){
		if(bigDecimal != null){
			return bigDecimal.toString();
		}
		return null;
	}
	
	String getString(Integer integer){
		if(integer != null){
			return integer.toString();
		}
		return null;
	}
	
	Object getObject(String str,Class<?> clazz){
		ObjectMapper objectMapper = new ObjectMapper();
		try{
			objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
			objectMapper.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
			return objectMapper.readValue(str, clazz);
		}catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
	
}
