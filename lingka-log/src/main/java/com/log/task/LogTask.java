package com.log.task;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.common.bean.ExportMessage;
import com.common.constant.CacheKey;
import com.common.constant.DataStatus;
import com.common.util.DateUtil;
import com.log.domain.Log;
import com.log.service.LogService;

@Component
public class LogTask extends BaseTask {
	@Autowired
	private LogService logService;
	@Autowired
	private RedisTemplate<String,String > redisTemplate;

	/**
	 * 日志按月迁移归档
	 */
	@Scheduled(cron = "0 0 3 * * ? ")  //每天凌晨3点执行
	public void start() {
		try {
			while (true) {
				Log backupMinLog = logService.findMin();
				if (backupMinLog != null) {
					logService.backupByMonth(backupMinLog);
					logger.info("execute tb_log backup by month {}", backupMinLog.getId());
				} else {
					break;
				}
			}
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}

	@Scheduled(initialDelay = 30,fixedRate = 1 * 1000)
	public void createLogStart(){
		try {
			Date serverTime = DateUtil.getServerTime();
			String logMessageJson = redisTemplate.opsForList().rightPop(CacheKey.LOG);
			if (logMessageJson == null){
				return;
			}
			Log log = (Log) this.getObject(logMessageJson, Log.class);

			//计算时长
			if (!this.isEmpty(log.getEndTime()) && !this.isEmpty(log.getStartTime())) {
				Long duration = log.getEndTime().getTime() - log.getStartTime().getTime();
				log.setDuration(duration.intValue());
			}
			log.setCreateTime(serverTime);
			log.setModifyTime(serverTime);
			log.setStatus(DataStatus.Y.getCode());
			logService.create(log);

		}catch (Exception e){
			logger.error(e.getMessage(),e);
		}
	}
}
