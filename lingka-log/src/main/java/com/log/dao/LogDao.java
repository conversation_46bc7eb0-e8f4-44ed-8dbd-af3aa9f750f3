package com.log.dao;

import org.apache.ibatis.annotations.Mapper;

import com.log.domain.Log;
import com.log.domain.complex.LogQuery;

@Mapper
public interface LogDao {
	Log selectByTableName(LogQuery logQuery);
	Integer deleteByTableName(LogQuery logQuery);
	Integer deleteById(Long id);
	Integer insert(Log log);
	Integer insertByTableName(Log log);
	Log selectMin();
	Integer createLogTable(String tableName);
	Integer createLogTableIndex1(Log log);
	Integer createLogTableIndex2(Log log);
	Integer createLogTableIndex3(Log log);
	Integer existsTable(String tableName);
}
