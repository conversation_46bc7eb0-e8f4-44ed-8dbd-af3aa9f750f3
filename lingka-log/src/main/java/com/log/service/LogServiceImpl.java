package com.log.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.common.util.DateUtil;
import com.log.dao.LogDao;
import com.log.domain.Log;
import com.log.domain.complex.LogQuery;

@Service
public class LogServiceImpl implements LogService {
	protected final String DATE_FORMAT = "yyyy-MM-dd";
	protected final String YEAR_MONTH_FORMAT = "yyyyMM";

	@Autowired
	private LogDao logDao;

	@Override
	@Transactional
	public void backupByMonth(Log log) {
		//转换log的createTime字段，看归属于哪张表
		StringBuilder tableName = new StringBuilder("tb_log_");
		String tableNameSuffix = DateUtil.format(log.getCreateTime(), YEAR_MONTH_FORMAT);
		tableName.append(tableNameSuffix);

		//判断当前表是否存在，不存在先创建一个表
		//1表示存在，0表示不存在
		Integer tableCount = logDao.existsTable(tableName.toString());

		if (tableCount == 0){
			logDao.createLogTable(tableName.toString());
			log.setTableName(tableName.toString());
			log.setTableIndexName("`idx_" + tableName.toString().substring(3) + "_01`");
			logDao.createLogTableIndex1(log);
			log.setTableIndexName("`idx_" + tableName.toString().substring(3) + "_02`");
			logDao.createLogTableIndex2(log);
			log.setTableIndexName("`idx_" + tableName.toString().substring(3) + "_03`");
			logDao.createLogTableIndex3(log);
		}

		//删除总表信息
		logDao.deleteById(log.getId());

		//查询是否存在于要插入的表
		LogQuery logQuery = new LogQuery();
		logQuery.setId(log.getId());
		logQuery.setTableNameSuffix(tableNameSuffix);
		Log logEntity = logDao.selectByTableName(logQuery);
		//存在直接return
		if (logEntity != null){
			return;
		}
		//不存在添加进去
		log.setTableNameSuffix(tableNameSuffix);
		logDao.insertByTableName(log);
	}

	@Override
	public Log findMin() {
		return logDao.selectMin();
	}

	@Override
	public Integer create(Log log) {
		return logDao.insert(log);
	}

}
