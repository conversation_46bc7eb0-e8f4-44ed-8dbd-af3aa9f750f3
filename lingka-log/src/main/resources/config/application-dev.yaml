###########################################################
# 框架配置
###########################################################
spring:
  # 数据源
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    url: jdbc:mysql://************:3306/lingka?characterEncoding=utf8&useSSL=false
    username: lingka
    password: lingka
    driver-class-name: com.mysql.cj.jdbc.Driver
    initialSize: 1
    minIdle: 1
    maxActive: 10
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1 FROM DUAL
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
    poolPreparedStatements: true
    maxPoolPreparedStatementPerConnectionSize: 20
    filters: stat,wall,slf4j
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    useGlobalDataSourceStat: true
  # 缓存
  redis:
    host: ************
    password: 38659215534F141B5496BDF477D909A0
    port: 6379
    database: 0
# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
###########################################################
###########################################################
# 日志配置
###########################################################
logging:
  config: classpath:config/logback-spring.xml
  file:
    path: /Users/<USER>/develop/logs/dev/lingka-log