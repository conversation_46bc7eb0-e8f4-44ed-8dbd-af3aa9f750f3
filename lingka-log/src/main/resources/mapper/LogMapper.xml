<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.log.dao.LogDao">
	<resultMap id="logResultMap" type="com.log.domain.Log">
		<id column="id" property="id" />
		<result column="platform" property="platform" />
		<result column="version" property="version" />
		<result column="type" property="type" />
		<result column="user_id" property="userId" />
		<result column="ip" property="ip" />
		<result column="url" property="url" />
		<result column="request_id" property="requestId" />
		<result column="response_code" property="responseCode" />
		<result column="start_time" property="startTime" />
		<result column="end_time" property="endTime" />
		<result column="duration" property="duration" />
		<result column="status" property="status" />
		<result column="modify_time" property="modifyTime" />
		<result column="create_time" property="createTime" />
	</resultMap>

	<select id="selectByTableName" parameterType="com.log.domain.complex.LogQuery" resultMap="logResultMap">
		select * from tb_log_${tableNameSuffix} where id = #{id}
	</select>

	<delete id="deleteByTableName" parameterType="com.log.domain.complex.LogQuery">
		delete from tb_log_${tableNameSuffix} where id = #{id}
	</delete>

	<delete id="deleteById">
		delete from tb_log where id = #{id}
	</delete>

	<insert id="insert" parameterType="com.log.domain.Log" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into tb_log
		<trim suffixOverrides="," prefix="(" suffix=") values">
			<if test="id != null">
				 id,
			</if>
			<if test="platform != null">
				platform,
			</if>
			<if test="version != null">
				version,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="ip != null">
				ip,
			</if>
			<if test="url != null">
				url,
			</if>
			<if test="requestId != null">
				request_id,
			</if>
			<if test="responseCode != null">
				response_code,
			</if>
			<if test="startTime != null">
				start_time,
			</if>
			<if test="endTime != null">
				end_time,
			</if>
			<if test="duration != null">
				duration,
			</if>
			<if test="status != null">
				 status,
			</if>
			<if test="modifyTime != null">
				 modify_time,
			</if>
			<if test="createTime != null">
				 create_time,
			</if>
		</trim>
		<trim suffixOverrides="," prefix="(" suffix=")">
			<if test="id != null">
				#{id},
			</if>
			<if test="platform != null">
				#{platform},
			</if>
			<if test="version != null">
				#{version},
			</if>
			<if test="type != null">
				#{type},
			</if>
			<if test="userId != null">
				#{userId},
			</if>
			<if test="ip != null">
				#{ip},
			</if>
			<if test="url != null">
				#{url},
			</if>
			<if test="requestId != null">
				#{requestId},
			</if>
			<if test="responseCode != null">
				#{responseCode},
			</if>
			<if test="startTime != null">
				#{startTime},
			</if>
			<if test="endTime != null">
				#{endTime},
			</if>
			<if test="duration != null">
				#{duration},
			</if>
			<if test="status != null">
				#{status},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
		</trim>
	</insert>

	<insert id="insertByTableName" parameterType="com.log.domain.Log" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into tb_log_${tableNameSuffix}
		<trim suffixOverrides="," prefix="(" suffix=") values">
			<if test="id != null">
				id,
			</if>
			<if test="platform != null">
				platform,
			</if>
			<if test="version != null">
				version,
			</if>
			<if test="type != null">
				type,
			</if>
			<if test="userId != null">
				user_id,
			</if>
			<if test="ip != null">
				ip,
			</if>
			<if test="url != null">
				url,
			</if>
			<if test="requestId != null">
				request_id,
			</if>
			<if test="responseCode != null">
				response_code,
			</if>
			<if test="startTime != null">
				start_time,
			</if>
			<if test="endTime != null">
				end_time,
			</if>
			<if test="duration != null">
				duration,
			</if>
			<if test="status != null">
				status,
			</if>
			<if test="modifyTime != null">
				modify_time,
			</if>
			<if test="createTime != null">
				create_time,
			</if>
		</trim>
		<trim suffixOverrides="," prefix="(" suffix=")">
			<if test="id != null">
				#{id},
			</if>
			<if test="platform != null">
				#{platform},
			</if>
			<if test="version != null">
				#{version},
			</if>
			<if test="type != null">
				#{type},
			</if>
			<if test="userId != null">
				#{userId},
			</if>
			<if test="ip != null">
				#{ip},
			</if>
			<if test="url != null">
				#{url},
			</if>
			<if test="requestId != null">
				#{requestId},
			</if>
			<if test="responseCode != null">
				#{responseCode},
			</if>
			<if test="startTime != null">
				#{startTime},
			</if>
			<if test="endTime != null">
				#{endTime},
			</if>
			<if test="duration != null">
				#{duration},
			</if>
			<if test="status != null">
				#{status},
			</if>
			<if test="modifyTime != null">
				#{modifyTime},
			</if>
			<if test="createTime != null">
				#{createTime},
			</if>
		</trim>
	</insert>

	<select id="selectMin" resultMap="logResultMap" parameterType="com.log.domain.Log">
		select * from tb_log order by id asc limit 0,1
	</select>

	<insert id="createLogTable" parameterType="string">
		create table ${tableName} (
			`id` bigint not null auto_increment comment '主键',
			`platform` varchar(20) comment '平台',
			`version` varchar(10) comment '版本',
			`type` varchar(1) comment '用户类型',
			`user_id` bigint comment '用户编号',
			`ip` varchar(32) comment 'IP',
			`url` varchar(64) comment '地址',
			`request_id` varchar(64) comment '请求编号',
			`response_code` varchar(4) comment '响应码',
			`start_time` datetime comment '开始时间',
			`end_time` datetime comment '结束时间',
			`duration` int comment '执行时长',
			`status` varchar(1) comment '状态(0:正常 1:无效)',
			`modify_time` datetime not null comment '修改时间',
			`create_time` datetime not null comment '创建时间',
			primary key(`id`)
		) engine=innodb default charset=utf8mb4 collate=utf8mb4_unicode_ci
		auto_increment=10000000
		comment '用户日志表';
	</insert>

	<insert id="createLogTableIndex1" parameterType="com.log.domain.Log">
		alter table `${tableName}` add index ${tableIndexName} (`user_id`);
	</insert>
	<insert id="createLogTableIndex2" parameterType="com.log.domain.Log">
		alter table `${tableName}` add index ${tableIndexName} (`url`);
	</insert>
	<insert id="createLogTableIndex3" parameterType="com.log.domain.Log">
		alter table `${tableName}` add index ${tableIndexName} (`start_time`);
	</insert>

	<select id="existsTable" parameterType="string" resultType="java.lang.Integer">
        SELECT COUNT(*) as count FROM information_schema.TABLES WHERE table_name = #{tableName}
    </select>
</mapper>